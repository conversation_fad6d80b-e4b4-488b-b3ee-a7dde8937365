/**
 * Point d'entrée simplifié pour C2-EW
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import SimpleApp from './SimpleApp';
import './index.css';

// Styles CSS de base pour Tailwind
const tailwindCSS = `
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Classes utilitaires de base */
.min-h-screen { min-height: 100vh; }
.h-screen { height: 100vh; }
.w-full { width: 100%; }
.h-full { height: 100%; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-1 { flex: 1 1 0%; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* Couleurs */
.bg-blue-900 { background-color: rgb(30 58 138); }
.bg-blue-800 { background-color: rgb(30 64 175); }
.bg-blue-700 { background-color: rgb(29 78 216); }
.bg-blue-600 { background-color: rgb(37 99 235); }
.bg-blue-500 { background-color: rgb(59 130 246); }
.bg-white { background-color: rgb(255 255 255); }
.bg-gray-800 { background-color: rgb(31 41 55); }
.bg-green-500 { background-color: rgb(34 197 94); }
.bg-red-50 { background-color: rgb(254 242 242); }

.text-white { color: rgb(255 255 255); }
.text-gray-800 { color: rgb(31 41 55); }
.text-gray-700 { color: rgb(55 65 81); }
.text-gray-600 { color: rgb(75 85 99); }
.text-gray-500 { color: rgb(107 114 128); }
.text-blue-600 { color: rgb(37 99 235); }
.text-blue-200 { color: rgb(191 219 254); }
.text-red-700 { color: rgb(185 28 28); }

/* Bordures */
.border { border-width: 1px; }
.border-gray-300 { border-color: rgb(209 213 219); }
.border-red-200 { border-color: rgb(254 202 202); }
.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* Espacement */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.ml-1 { margin-left: 0.25rem; }

/* Tailles */
.w-2 { width: 0.5rem; }
.w-4 { width: 1rem; }
.h-2 { height: 0.5rem; }
.h-4 { height: 1rem; }
.max-w-md { max-width: 28rem; }
.max-w-xs { max-width: 20rem; }

/* Texte */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-center { text-align: center; }

/* Ombres */
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }

/* Positionnement */
.relative { position: relative; }
.absolute { position: absolute; }
.top-4 { top: 1rem; }
.right-4 { right: 1rem; }
.left-4 { left: 1rem; }
.bottom-4 { bottom: 1rem; }

/* Interactions */
.hover\\:bg-blue-700:hover { background-color: rgb(29 78 216); }
.hover\\:bg-blue-600:hover { background-color: rgb(37 99 235); }
.focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgb(59 130 246); }
.focus\\:ring-blue-500:focus { box-shadow: 0 0 0 2px rgb(59 130 246); }
.disabled\\:opacity-50:disabled { opacity: 0.5; }

/* Gradients */
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.from-blue-900 { --tw-gradient-from: rgb(30 58 138); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgb(30 58 138 / 0)); }
.to-blue-700 { --tw-gradient-to: rgb(29 78 216); }
`;

// Injecter les styles
const styleElement = document.createElement('style');
styleElement.textContent = tailwindCSS;
document.head.appendChild(styleElement);

// Rendu de l'application
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <SimpleApp />
  </React.StrictMode>
);

console.log('🚀 Application C2-EW simplifiée démarrée');
console.log('📍 Backend API: http://localhost:8000');
console.log('🗺️ Module SIG: Actif');
console.log('🇲🇦 Territoire: Maroc');
