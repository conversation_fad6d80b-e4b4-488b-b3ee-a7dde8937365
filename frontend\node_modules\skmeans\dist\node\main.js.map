{"version": 3, "sources": ["../../main.js"], "names": ["Distance", "require", "ClusterInit", "eudist", "mandist", "dist", "kmrand", "kmpp", "MAX", "init", "len", "val", "v", "i", "skmeans", "data", "k", "initial", "maxit", "ks", "old", "idxs", "conv", "it", "length", "vlen", "multi", "count", "idx", "Math", "floor", "random", "push", "min", "Infinity", "j", "abs", "sum", "dif", "vsum", "vect", "h", "ksj", "sumj", "oldj", "cj", "centroids", "module", "exports"], "mappings": ";;AAAA;;AAEA,IACCA,WAAWC,QAAQ,eAAR,CADZ;AAAA,IAECC,cAAcD,QAAQ,YAAR,CAFf;AAAA,IAGCE,SAASH,SAASG,MAHnB;AAAA,IAICC,UAAUJ,SAASI,OAJpB;AAAA,IAKCC,OAAOL,SAASK,IALjB;AAAA,IAMCC,SAASJ,YAAYI,MANtB;AAAA,IAOCC,OAAOL,YAAYK,IAPpB;;AASA,IAAMC,MAAM,KAAZ;;AAEA;;;AAGA,SAASC,IAAT,CAAcC,GAAd,EAAkBC,GAAlB,EAAsBC,CAAtB,EAAyB;AACxBA,KAAIA,KAAK,EAAT;AACA,MAAI,IAAIC,IAAE,CAAV,EAAYA,IAAEH,GAAd,EAAkBG,GAAlB;AAAuBD,IAAEC,CAAF,IAAOF,GAAP;AAAvB,EACA,OAAOC,CAAP;AACA;;AAED,SAASE,OAAT,CAAiBC,IAAjB,EAAsBC,CAAtB,EAAwBC,OAAxB,EAAgCC,KAAhC,EAAuC;AACtC,KAAIC,KAAK,EAAT;AAAA,KAAaC,MAAM,EAAnB;AAAA,KAAuBC,OAAO,EAA9B;AAAA,KAAkChB,OAAO,EAAzC;AACA,KAAIiB,OAAO,KAAX;AAAA,KAAkBC,KAAKL,SAASV,GAAhC;AACA,KAAIE,MAAMK,KAAKS,MAAf;AAAA,KAAuBC,OAAOV,KAAK,CAAL,EAAQS,MAAtC;AAAA,KAA8CE,QAAQD,OAAK,CAA3D;AACA,KAAIE,QAAQ,EAAZ;;AAEA,KAAG,CAACV,OAAJ,EAAa;AACZ,MAAII,QAAO,EAAX;AACA,SAAMF,GAAGK,MAAH,GAAUR,CAAhB,EAAmB;AAClB,OAAIY,MAAMC,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAcrB,GAAzB,CAAV;AACA,OAAG,CAACW,MAAKO,GAAL,CAAJ,EAAe;AACdP,UAAKO,GAAL,IAAY,IAAZ;AACAT,OAAGa,IAAH,CAAQjB,KAAKa,GAAL,CAAR;AACA;AACD;AACD,EATD,MAUK,IAAGX,WAAS,QAAZ,EAAsB;AAC1BE,OAAKb,OAAOS,IAAP,EAAYC,CAAZ,CAAL;AACA,EAFI,MAGA,IAAGC,WAAS,MAAZ,EAAoB;AACxBE,OAAKZ,KAAKQ,IAAL,EAAUC,CAAV,CAAL;AACA,EAFI,MAGA;AACJG,OAAKF,OAAL;AACA;;AAED,IAAG;AACF;AACAR,OAAKO,CAAL,EAAO,CAAP,EAASW,KAAT;;AAEA;AACA,OAAI,IAAId,IAAE,CAAV,EAAYA,IAAEH,GAAd,EAAkBG,GAAlB,EAAuB;AACtB,OAAIoB,MAAMC,QAAV;AAAA,OAAoBN,OAAM,CAA1B;AACA,QAAI,IAAIO,IAAE,CAAV,EAAYA,IAAEnB,CAAd,EAAgBmB,GAAhB,EAAqB;AACpB;AACA,QAAI9B,OAAOqB,QAAOvB,OAAOY,KAAKF,CAAL,CAAP,EAAeM,GAAGgB,CAAH,CAAf,CAAP,GAA+BN,KAAKO,GAAL,CAASrB,KAAKF,CAAL,IAAQM,GAAGgB,CAAH,CAAjB,CAA1C;AACA,QAAG9B,QAAM4B,GAAT,EAAc;AACbA,WAAM5B,IAAN;AACAuB,YAAMO,CAAN;AACA;AACD;AACDd,QAAKR,CAAL,IAAUe,IAAV,CAVsB,CAUP;AACfD,SAAMC,IAAN,IAXsB,CAWP;AACf;;AAED;AACA,MAAIS,MAAM,EAAV;AAAA,MAAcjB,MAAM,EAApB;AAAA,MAAwBkB,MAAM,CAA9B;AACA,OAAI,IAAIH,KAAE,CAAV,EAAYA,KAAEnB,CAAd,EAAgBmB,IAAhB,EAAqB;AACpB;AACAE,OAAIF,EAAJ,IAAST,QAAOjB,KAAKgB,IAAL,EAAU,CAAV,EAAYY,IAAIF,EAAJ,CAAZ,CAAP,GAA6B,CAAtC;AACAf,OAAIe,EAAJ,IAAShB,GAAGgB,EAAH,CAAT;AACA;;AAED;AACA,MAAGT,KAAH,EAAU;AACT,QAAI,IAAIS,MAAE,CAAV,EAAYA,MAAEnB,CAAd,EAAgBmB,KAAhB;AAAqBhB,OAAGgB,GAAH,IAAQ,EAAR;AAArB,IADS,CAGT;AACA,QAAI,IAAItB,KAAE,CAAV,EAAYA,KAAEH,GAAd,EAAkBG,IAAlB,EAAuB;AACtB,QAAIe,QAAMP,KAAKR,EAAL,CAAV;AAAA,QAAoB;AAClB0B,WAAOF,IAAIT,KAAJ,CADT;AAAA,QACmB;AACjBY,WAAOzB,KAAKF,EAAL,CAFT,CADsB,CAGH;;AAEnB;AACA,SAAI,IAAI4B,IAAE,CAAV,EAAYA,IAAEhB,IAAd,EAAmBgB,GAAnB,EAAwB;AACvBF,UAAKE,CAAL,KAAWD,KAAKC,CAAL,CAAX;AACA;AACD;AACD;AACAnB,UAAO,IAAP;AACA,QAAI,IAAIa,MAAE,CAAV,EAAYA,MAAEnB,CAAd,EAAgBmB,KAAhB,EAAqB;AACpB,QAAIO,MAAMvB,GAAGgB,GAAH,CAAV;AAAA,QAAkB;AAChBQ,WAAON,IAAIF,GAAJ,CADT;AAAA,QACiB;AACfS,WAAOxB,IAAIe,GAAJ,CAFT;AAAA,QAEkB;AAChBU,SAAKlB,MAAMQ,GAAN,CAHP,CADoB,CAIH;;AAEjB;AACA,SAAI,IAAIM,KAAE,CAAV,EAAYA,KAAEhB,IAAd,EAAmBgB,IAAnB,EAAwB;AACvBC,SAAID,EAAJ,IAAUE,KAAKF,EAAL,CAAD,GAAWI,EAAX,IAAkB,CAA3B,CADuB,CACO;AAC9B;;AAED;AACA,QAAGvB,IAAH,EAAS;AACR,UAAI,IAAImB,MAAE,CAAV,EAAYA,MAAEhB,IAAd,EAAmBgB,KAAnB,EAAwB;AACvB,UAAGG,KAAKH,GAAL,KAASC,IAAID,GAAJ,CAAZ,EAAoB;AACnBnB,cAAO,KAAP;AACA;AACA;AACD;AACD;AACD;AACD;AACD;AAtCA,OAuCK;AACJ;AACA,SAAI,IAAIT,MAAE,CAAV,EAAYA,MAAEH,GAAd,EAAkBG,KAAlB,EAAuB;AACtB,SAAIe,QAAMP,KAAKR,GAAL,CAAV;AACAwB,SAAIT,KAAJ,KAAYb,KAAKF,GAAL,CAAZ;AACA;AACD;AACA,SAAI,IAAIsB,MAAE,CAAV,EAAYA,MAAEnB,CAAd,EAAgBmB,KAAhB,EAAqB;AACpBhB,QAAGgB,GAAH,IAAQE,IAAIF,GAAJ,IAAOR,MAAMQ,GAAN,CAAP,IAAmB,CAA3B,CADoB,CACU;AAC9B;AACD;AACAb,WAAO,IAAP;AACA,SAAI,IAAIa,MAAE,CAAV,EAAYA,MAAEnB,CAAd,EAAgBmB,KAAhB,EAAqB;AACpB,SAAGf,IAAIe,GAAJ,KAAQhB,GAAGgB,GAAH,CAAX,EAAkB;AACjBb,aAAO,KAAP;AACA;AACA;AACD;AACD;;AAEDA,SAAOA,QAAS,EAAEC,EAAF,IAAM,CAAtB;AACA,EAxFD,QAwFO,CAACD,IAxFR;;AA0FA,QAAO;AACNC,MAAKf,MAAIe,EADH;AAENP,KAAIA,CAFE;AAGNK,QAAOA,IAHD;AAINyB,aAAY3B;AAJN,EAAP;AAMA;;AAED4B,OAAOC,OAAP,GAAiBlC,OAAjB", "file": "main.js", "sourcesContent": ["/*jshint esversion: 6 */\n\nconst\n\tDistance = require(\"./distance.js\"),\n\tClusterInit = require(\"./kinit.js\"),\n\teudist = Distance.eudist,\n\tmandist = Distance.mandist,\n\tdist = Distance.dist,\n\tkmrand = ClusterInit.kmrand,\n\tkmpp = ClusterInit.kmpp;\n\nconst MAX = 10000;\n\n/**\n * Inits an array with values\n */\nfunction init(len,val,v) {\n\tv = v || [];\n\tfor(let i=0;i<len;i++) v[i] = val;\n\treturn v;\n}\n\nfunction skmeans(data,k,initial,maxit) {\n\tvar ks = [], old = [], idxs = [], dist = [];\n\tvar conv = false, it = maxit || MAX;\n\tvar len = data.length, vlen = data[0].length, multi = vlen>0;\n\tvar count = [];\n\n\tif(!initial) {\n\t\tlet idxs = {};\n\t\twhile(ks.length<k) {\n\t\t\tlet idx = Math.floor(Math.random()*len);\n\t\t\tif(!idxs[idx]) {\n\t\t\t\tidxs[idx] = true;\n\t\t\t\tks.push(data[idx]);\n\t\t\t}\n\t\t}\n\t}\n\telse if(initial==\"kmrand\") {\n\t\tks = kmrand(data,k);\n\t}\n\telse if(initial==\"kmpp\") {\n\t\tks = kmpp(data,k);\n\t}\n\telse {\n\t\tks = initial;\n\t}\n\n\tdo {\n\t\t// Reset k count\n\t\tinit(k,0,count);\n\n\t\t// For each value in data, find the nearest centroid\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tlet min = Infinity, idx = 0;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\t// Multidimensional or unidimensional\n\t\t\t\tvar dist = multi? eudist(data[i],ks[j]) : Math.abs(data[i]-ks[j]);\n\t\t\t\tif(dist<=min) {\n\t\t\t\t\tmin = dist;\n\t\t\t\t\tidx = j;\n\t\t\t\t}\n\t\t\t}\n\t\t\tidxs[i] = idx;\t// Index of the selected centroid for that value\n\t\t\tcount[idx]++;\t\t// Number of values for this centroid\n\t\t}\n\n\t\t// Recalculate centroids\n\t\tvar sum = [], old = [], dif = 0;\n\t\tfor(let j=0;j<k;j++) {\n\t\t\t// Multidimensional or unidimensional\n\t\t\tsum[j] = multi? init(vlen,0,sum[j]) : 0;\n\t\t\told[j] = ks[j];\n\t\t}\n\n\t\t// If multidimensional\n\t\tif(multi) {\n\t\t\tfor(let j=0;j<k;j++) ks[j] = [];\n\n\t\t\t// Sum values and count for each centroid\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet\tidx = idxs[i],\t\t// Centroid for that item\n\t\t\t\t\t\tvsum = sum[idx],\t// Sum values for this centroid\n\t\t\t\t\t\tvect = data[i];\t\t// Current vector\n\n\t\t\t\t// Accumulate value on the centroid for current vector\n\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\tvsum[h] += vect[h];\n\t\t\t\t}\n\t\t\t}\n\t\t\t// Calculate the average for each centroid\n\t\t\tconv = true;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tlet ksj = ks[j],\t\t// Current centroid\n\t\t\t\t\t\tsumj = sum[j],\t// Accumulated centroid values\n\t\t\t\t\t\toldj = old[j], \t// Old centroid value\n\t\t\t\t\t\tcj = count[j];\t// Number of elements for this centroid\n\n\t\t\t\t// New average\n\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\tksj[h] = (sumj[h])/(cj) || 0;\t// New centroid\n\t\t\t\t}\n\n\t\t\t\t// Find if centroids have moved\n\t\t\t\tif(conv) {\n\t\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\t\tif(oldj[h]!=ksj[h]) {\n\t\t\t\t\t\t\tconv = false;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// If unidimensional\n\t\telse {\n\t\t\t// Sum values and count for each centroid\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet idx = idxs[i];\n\t\t\t\tsum[idx] += data[i];\n\t\t\t}\n\t\t\t// Calculate the average for each centroid\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tks[j] = sum[j]/count[j] || 0;\t// New centroid\n\t\t\t}\n\t\t\t// Find if centroids have moved\n\t\t\tconv = true;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tif(old[j]!=ks[j]) {\n\t\t\t\t\tconv = false;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconv = conv || (--it<=0);\n\t}while(!conv);\n\n\treturn {\n\t\tit : MAX-it,\n\t\tk : k,\n\t\tidxs : idxs,\n\t\tcentroids : ks\n\t};\n}\n\nmodule.exports = skmeans;\n"]}