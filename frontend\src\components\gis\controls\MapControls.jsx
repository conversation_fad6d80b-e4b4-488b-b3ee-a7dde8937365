/**
 * Contrôles de carte pour OpenLayers
 * Module cartographique C2-EW
 */

import React, { useState, useCallback } from 'react';
import { 
    ZoomIn, 
    ZoomOut, 
    RotateCcw, 
    Home, 
    Layers, 
    Settings,
    Wifi,
    WifiOff,
    Map,
    Satellite,
    Navigation
} from 'lucide-react';
import { useMapStore } from '../../../store/mapStore';

/**
 * Composant des contrôles de carte
 */
export const MapControls = ({
    gisEngine,
    layerManager,
    projectionManager,
    performanceManager,
    className = ''
}) => {
    const [showProjectionPanel, setShowProjectionPanel] = useState(false);
    const [showPerformancePanel, setShowPerformancePanel] = useState(false);
    
    const {
        offlineMode,
        selectedBaseLayer,
        setOfflineMode,
        setSelectedBaseLayer
    } = useMapStore();
    
    /**
     * Zoom avant
     */
    const handleZoomIn = useCallback(() => {
        if (!gisEngine) return;
        
        const view = gisEngine.getMap().getView();
        const currentZoom = view.getZoom();
        view.animate({
            zoom: currentZoom + 1,
            duration: 250
        });
    }, [gisEngine]);
    
    /**
     * Zoom arrière
     */
    const handleZoomOut = useCallback(() => {
        if (!gisEngine) return;
        
        const view = gisEngine.getMap().getView();
        const currentZoom = view.getZoom();
        view.animate({
            zoom: currentZoom - 1,
            duration: 250
        });
    }, [gisEngine]);
    
    /**
     * Réinitialiser la rotation
     */
    const handleResetRotation = useCallback(() => {
        if (!gisEngine) return;
        
        const view = gisEngine.getMap().getView();
        view.animate({
            rotation: 0,
            duration: 500
        });
    }, [gisEngine]);
    
    /**
     * Retour à la vue du Maroc
     */
    const handleGoHome = useCallback(() => {
        if (!gisEngine) return;
        
        gisEngine.setMoroccoExtent();
    }, [gisEngine]);
    
    /**
     * Basculer le mode hors ligne
     */
    const handleToggleOffline = useCallback(() => {
        const newOfflineMode = !offlineMode;
        setOfflineMode(newOfflineMode);
        
        if (gisEngine) {
            gisEngine.setOfflineMode(newOfflineMode);
        }
    }, [offlineMode, setOfflineMode, gisEngine]);
    
    /**
     * Changer la couche de base
     */
    const handleBaseLayerChange = useCallback((layerName) => {
        setSelectedBaseLayer(layerName);
        
        if (gisEngine) {
            gisEngine.setBaseLayer(layerName);
        }
    }, [setSelectedBaseLayer, gisEngine]);
    
    /**
     * Changer la projection
     */
    const handleProjectionChange = useCallback((projectionCode) => {
        if (projectionManager) {
            projectionManager.changeProjection(projectionCode);
        }
        setShowProjectionPanel(false);
    }, [projectionManager]);
    
    /**
     * Optimiser les performances
     */
    const handleOptimizePerformance = useCallback(() => {
        if (performanceManager) {
            performanceManager.forceOptimization();
        }
    }, [performanceManager]);
    
    return (
        <div className={`absolute top-4 left-4 space-y-2 ${className}`}>
            {/* Contrôles de navigation */}
            <div className="bg-white rounded-lg shadow-lg p-2 space-y-1">
                <button
                    onClick={handleZoomIn}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Zoom avant"
                >
                    <ZoomIn size={20} />
                </button>
                
                <button
                    onClick={handleZoomOut}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Zoom arrière"
                >
                    <ZoomOut size={20} />
                </button>
                
                <button
                    onClick={handleResetRotation}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Réinitialiser la rotation"
                >
                    <RotateCcw size={20} />
                </button>
                
                <button
                    onClick={handleGoHome}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Vue du Maroc"
                >
                    <Home size={20} />
                </button>
            </div>
            
            {/* Contrôles de couches de base */}
            <div className="bg-white rounded-lg shadow-lg p-2">
                <div className="flex space-x-1">
                    <button
                        onClick={() => handleBaseLayerChange('OpenStreetMap')}
                        className={`w-10 h-10 flex items-center justify-center rounded transition-colors ${
                            selectedBaseLayer === 'OpenStreetMap' 
                                ? 'bg-blue-100 text-blue-600' 
                                : 'hover:bg-gray-100'
                        }`}
                        title="Carte standard"
                    >
                        <Map size={20} />
                    </button>
                    
                    <button
                        onClick={() => handleBaseLayerChange('Vue satellite')}
                        className={`w-10 h-10 flex items-center justify-center rounded transition-colors ${
                            selectedBaseLayer === 'Vue satellite' 
                                ? 'bg-blue-100 text-blue-600' 
                                : 'hover:bg-gray-100'
                        }`}
                        title="Vue satellite"
                    >
                        <Satellite size={20} />
                    </button>
                </div>
            </div>
            
            {/* Contrôle mode hors ligne */}
            <div className="bg-white rounded-lg shadow-lg p-2">
                <button
                    onClick={handleToggleOffline}
                    className={`w-10 h-10 flex items-center justify-center rounded transition-colors ${
                        offlineMode 
                            ? 'bg-green-100 text-green-600' 
                            : 'bg-red-100 text-red-600 hover:bg-red-200'
                    }`}
                    title={offlineMode ? 'Mode hors ligne activé' : 'Mode en ligne'}
                >
                    {offlineMode ? <WifiOff size={20} /> : <Wifi size={20} />}
                </button>
            </div>
            
            {/* Contrôles avancés */}
            <div className="bg-white rounded-lg shadow-lg p-2 space-y-1">
                <button
                    onClick={() => setShowProjectionPanel(!showProjectionPanel)}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Projections"
                >
                    <Navigation size={20} />
                </button>
                
                <button
                    onClick={() => setShowPerformancePanel(!showPerformancePanel)}
                    className="w-10 h-10 flex items-center justify-center rounded hover:bg-gray-100 transition-colors"
                    title="Performance"
                >
                    <Settings size={20} />
                </button>
            </div>
            
            {/* Panel de sélection de projection */}
            {showProjectionPanel && projectionManager && (
                <div className="bg-white rounded-lg shadow-lg p-4 w-64">
                    <h3 className="font-semibold mb-3">Projections cartographiques</h3>
                    <div className="space-y-2">
                        {projectionManager.getAvailableProjections().map(proj => (
                            <button
                                key={proj.code}
                                onClick={() => handleProjectionChange(proj.code)}
                                className={`w-full text-left p-2 rounded text-sm transition-colors ${
                                    proj.code === projectionManager.getCurrentProjection()
                                        ? 'bg-blue-100 text-blue-800'
                                        : 'hover:bg-gray-100'
                                }`}
                            >
                                <div className="font-medium">{proj.name}</div>
                                <div className="text-gray-500 text-xs">{proj.description}</div>
                            </button>
                        ))}
                    </div>
                    
                    <button
                        onClick={() => setShowProjectionPanel(false)}
                        className="mt-3 w-full py-2 px-4 bg-gray-100 rounded text-sm hover:bg-gray-200 transition-colors"
                    >
                        Fermer
                    </button>
                </div>
            )}
            
            {/* Panel de performance */}
            {showPerformancePanel && performanceManager && (
                <div className="bg-white rounded-lg shadow-lg p-4 w-64">
                    <h3 className="font-semibold mb-3">Performance</h3>
                    
                    <div className="space-y-3">
                        <div>
                            <div className="text-sm text-gray-600">Niveau de performance</div>
                            <div className={`text-sm font-medium ${
                                performanceManager.getState().performanceLevel === 'high' ? 'text-green-600' :
                                performanceManager.getState().performanceLevel === 'medium' ? 'text-yellow-600' :
                                'text-red-600'
                            }`}>
                                {performanceManager.getState().performanceLevel.toUpperCase()}
                            </div>
                        </div>
                        
                        <div>
                            <div className="text-sm text-gray-600">FPS moyen</div>
                            <div className="text-sm font-medium">
                                {Math.round(performanceManager.getMetrics().frameRate || 0)}
                            </div>
                        </div>
                        
                        <div>
                            <div className="text-sm text-gray-600">Nombre de features</div>
                            <div className="text-sm font-medium">
                                {performanceManager.getMetrics().featureCount || 0}
                            </div>
                        </div>
                        
                        <button
                            onClick={handleOptimizePerformance}
                            className="w-full py-2 px-4 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                        >
                            Optimiser
                        </button>
                    </div>
                    
                    <button
                        onClick={() => setShowPerformancePanel(false)}
                        className="mt-3 w-full py-2 px-4 bg-gray-100 rounded text-sm hover:bg-gray-200 transition-colors"
                    >
                        Fermer
                    </button>
                </div>
            )}
        </div>
    );
};

export default MapControls;
