{"name": "turf-jsts", "description": "A JavaScript library of spatial predicates and functions for processing geometry", "version": "1.2.3", "main": "jsts.js", "module": "jsts.mjs", "browser": "jsts.min.js", "files": ["jsts.js", "jsts.mjs", "jsts.min.js", "src"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <@DenisCarriere>"], "keywords": ["<PERSON><PERSON>", "JSTS", "JavaScript", "JTS", "Java", "Topology", "Geometry"], "license": "(EDL-1.0 OR EPL-1.0)", "devDependencies": {"@std/esm": "*", "chai": "*", "cheerio": "*", "expect.js": "*", "git-rev-sync": "*", "jsdoc": "*", "mocha": "*", "react-scripts": "*", "rollup": "*", "rollup-plugin-buble": "*", "rollup-plugin-uglify": "*", "standard": "*", "uglify-js": "*"}, "repository": {"type": "git", "url": "git://github.com/DenisCarriere/turf-jsts.git"}, "standard": {"globals": ["describe", "it", "jsts", "ol"]}, "scripts": {"prepublish": "rollup -c rollup.config.js", "test": "NODE_PATH=src mocha -r @std/esm --recursive test/auto/node test/manual", "test-manual": "NODE_PATH=src mocha -r @std/esm test/manual", "create-react-app": "react-scripts build"}, "@std/esm": {"esm": "js", "cjs": true}}