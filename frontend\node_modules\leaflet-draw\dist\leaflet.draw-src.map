{"version": 3, "sources": ["src/Leaflet.draw.js", "src/Leaflet.Draw.Event.js", "src/draw/handler/Draw.Feature.js", "src/draw/handler/Draw.Polyline.js", "src/draw/handler/Draw.Polygon.js", "src/draw/handler/Draw.SimpleShape.js", "src/draw/handler/Draw.Rectangle.js", "src/draw/handler/Draw.Marker.js", "src/draw/handler/Draw.CircleMarker.js", "src/draw/handler/Draw.Circle.js", "src/edit/handler/Edit.Marker.js", "src/edit/handler/Edit.Poly.js", "src/edit/handler/Edit.SimpleShape.js", "src/edit/handler/Edit.Rectangle.js", "src/edit/handler/Edit.CircleMarker.js", "src/edit/handler/Edit.Circle.js", "src/ext/TouchEvents.js", "src/ext/LatLngUtil.js", "src/ext/GeometryUtil.js", "src/ext/LineUtil.Intersect.js", "src/ext/Polyline.Intersect.js", "src/ext/Polygon.Intersect.js", "src/Control.Draw.js", "src/Toolbar.js", "src/Tooltip.js", "src/draw/DrawToolbar.js", "src/edit/EditToolbar.js", "src/edit/handler/EditToolbar.Edit.js", "src/edit/handler/EditToolbar.Delete.js"], "names": [], "mappings": ";;;;;;;yCAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC7LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AChLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACxGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AChlBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACjIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACvHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AClGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC/HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACnFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACpFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACxgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACrOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC7HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACzDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AChRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACrKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC1GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACtHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC9UA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;ACtHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AC9GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AClMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AClSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["/**\n * Leaflet.draw assumes that you have already included the Leaflet library.\n */\nL.drawVersion = \"1.0.4\";\n/**\n * @class L.Draw\n * @aka Draw\n *\n *\n * To add the draw toolbar set the option drawControl: true in the map options.\n *\n * @example\n * ```js\n *      var map = L.map('map', {drawControl: true}).setView([51.505, -0.09], 13);\n *\n *      <PERSON><PERSON>tileLayer('http://{s}.tile.osm.org/{z}/{x}/{y}.png', {\n *          attribution: '&copy; <a href=\"http://osm.org/copyright\">OpenStreetMap</a> contributors'\n *      }).addTo(map);\n * ```\n *\n * ### Adding the edit toolbar\n * To use the edit toolbar you must initialise the Leaflet.draw control and manually add it to the map.\n *\n * ```js\n *      var map = L.map('map').setView([51.505, -0.09], 13);\n *\n *      <PERSON><PERSON>tileLayer('http://{s}.tile.osm.org/{z}/{x}/{y}.png', {\n *          attribution: '&copy; <a href=\"http://osm.org/copyright\">OpenStreetMap</a> contributors'\n *      }).addTo(map);\n *\n *      // FeatureGroup is to store editable layers\n *      var drawnItems = new L.FeatureGroup();\n *      map.addLayer(drawnItems);\n *\n *      var drawControl = new L.Control.Draw({\n *          edit: {\n *              featureGroup: drawnItems\n *          }\n *      });\n *      map.addControl(drawControl);\n * ```\n *\n * The key here is the featureGroup option. This tells the plugin which FeatureGroup contains the layers that\n * should be editable. The featureGroup can contain 0 or more features with geometry types Point, LineString, and Polygon.\n * Leaflet.draw does not work with multigeometry features such as MultiPoint, MultiLineString, MultiPolygon,\n * or GeometryCollection. If you need to add multigeometry features to the draw plugin, convert them to a\n * FeatureCollection of non-multigeometries (Points, LineStrings, or Polygons).\n */\nL.Draw = {};\n\n/**\n * @class L.drawLocal\n * @aka L.drawLocal\n *\n * The core toolbar class of the API — it is used to create the toolbar ui\n *\n * @example\n * ```js\n *      var modifiedDraw = L.drawLocal.extend({\n *          draw: {\n *              toolbar: {\n *                  buttons: {\n *                      polygon: 'Draw an awesome polygon'\n *                  }\n *              }\n *          }\n *      });\n * ```\n *\n * The default state for the control is the draw toolbar just below the zoom control.\n *  This will allow map users to draw vectors and markers.\n *  **Please note the edit toolbar is not enabled by default.**\n */\nL.drawLocal = {\n\t// format: {\n\t// \tnumeric: {\n\t// \t\tdelimiters: {\n\t// \t\t\tthousands: ',',\n\t// \t\t\tdecimal: '.'\n\t// \t\t}\n\t// \t}\n\t// },\n\tdraw: {\n\t\ttoolbar: {\n\t\t\t// #TODO: this should be reorganized where actions are nested in actions\n\t\t\t// ex: actions.undo  or actions.cancel\n\t\t\tactions: {\n\t\t\t\ttitle: 'Cancel drawing',\n\t\t\t\ttext: 'Cancel'\n\t\t\t},\n\t\t\tfinish: {\n\t\t\t\ttitle: 'Finish drawing',\n\t\t\t\ttext: 'Finish'\n\t\t\t},\n\t\t\tundo: {\n\t\t\t\ttitle: 'Delete last point drawn',\n\t\t\t\ttext: 'Delete last point'\n\t\t\t},\n\t\t\tbuttons: {\n\t\t\t\tpolyline: 'Draw a polyline',\n\t\t\t\tpolygon: 'Draw a polygon',\n\t\t\t\trectangle: 'Draw a rectangle',\n\t\t\t\tcircle: 'Draw a circle',\n\t\t\t\tmarker: 'Draw a marker',\n\t\t\t\tcirclemarker: 'Draw a circlemarker'\n\t\t\t}\n\t\t},\n\t\thandlers: {\n\t\t\tcircle: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click and drag to draw circle.'\n\t\t\t\t},\n\t\t\t\tradius: 'Radius'\n\t\t\t},\n\t\t\tcirclemarker: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click map to place circle marker.'\n\t\t\t\t}\n\t\t\t},\n\t\t\tmarker: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click map to place marker.'\n\t\t\t\t}\n\t\t\t},\n\t\t\tpolygon: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click to start drawing shape.',\n\t\t\t\t\tcont: 'Click to continue drawing shape.',\n\t\t\t\t\tend: 'Click first point to close this shape.'\n\t\t\t\t}\n\t\t\t},\n\t\t\tpolyline: {\n\t\t\t\terror: '<strong>Error:</strong> shape edges cannot cross!',\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click to start drawing line.',\n\t\t\t\t\tcont: 'Click to continue drawing line.',\n\t\t\t\t\tend: 'Click last point to finish line.'\n\t\t\t\t}\n\t\t\t},\n\t\t\trectangle: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tstart: 'Click and drag to draw rectangle.'\n\t\t\t\t}\n\t\t\t},\n\t\t\tsimpleshape: {\n\t\t\t\ttooltip: {\n\t\t\t\t\tend: 'Release mouse to finish drawing.'\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\tedit: {\n\t\ttoolbar: {\n\t\t\tactions: {\n\t\t\t\tsave: {\n\t\t\t\t\ttitle: 'Save changes',\n\t\t\t\t\ttext: 'Save'\n\t\t\t\t},\n\t\t\t\tcancel: {\n\t\t\t\t\ttitle: 'Cancel editing, discards all changes',\n\t\t\t\t\ttext: 'Cancel'\n\t\t\t\t},\n\t\t\t\tclearAll: {\n\t\t\t\t\ttitle: 'Clear all layers',\n\t\t\t\t\ttext: 'Clear All'\n\t\t\t\t}\n\t\t\t},\n\t\t\tbuttons: {\n\t\t\t\tedit: 'Edit layers',\n\t\t\t\teditDisabled: 'No layers to edit',\n\t\t\t\tremove: 'Delete layers',\n\t\t\t\tremoveDisabled: 'No layers to delete'\n\t\t\t}\n\t\t},\n\t\thandlers: {\n\t\t\tedit: {\n\t\t\t\ttooltip: {\n\t\t\t\t\ttext: 'Drag handles or markers to edit features.',\n\t\t\t\t\tsubtext: 'Click cancel to undo changes.'\n\t\t\t\t}\n\t\t\t},\n\t\t\tremove: {\n\t\t\t\ttooltip: {\n\t\t\t\t\ttext: 'Click on a feature to remove.'\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n", "/**\n * ### Events\n * Once you have successfully added the Leaflet.draw plugin to your map you will want to respond to the different\n * actions users can initiate. The following events will be triggered on the map:\n *\n * @class L.Draw.Event\n * @aka Draw.Event\n *\n * Use `L.Draw.Event.EVENTNAME` constants to ensure events are correct.\n *\n * @example\n * ```js\n * map.on(L.Draw.Event.CREATED; function (e) {\n *    var type = e.layerType,\n *        layer = e.layer;\n *\n *    if (type === 'marker') {\n *        // Do marker specific actions\n *    }\n *\n *    // Do whatever else you need to. (save to db; add to map etc)\n *    map.addLayer(layer);\n *});\n * ```\n */\nL.Draw.Event = {};\n/**\n * @event draw:created: PolyLine; Polygon; Rectangle; Circle; Marker | String\n *\n * Layer that was just created.\n * The type of layer this is. One of: `polyline`; `polygon`; `rectangle`; `circle`; `marker`\n * Triggered when a new vector or marker has been created.\n *\n */\nL.Draw.Event.CREATED = 'draw:created';\n\n/**\n * @event draw:edited: LayerGroup\n *\n * List of all layers just edited on the map.\n *\n *\n * Triggered when layers in the FeatureGroup; initialised with the plugin; have been edited and saved.\n *\n * @example\n * ```js\n *      map.on('draw:edited', function (e) {\n     *          var layers = e.layers;\n     *          layers.eachLayer(function (layer) {\n     *              //do whatever you want; most likely save back to db\n     *          });\n     *      });\n * ```\n */\nL.Draw.Event.EDITED = 'draw:edited';\n\n/**\n * @event draw:deleted: LayerGroup\n *\n * List of all layers just removed from the map.\n *\n * Triggered when layers have been removed (and saved) from the FeatureGroup.\n */\nL.Draw.Event.DELETED = 'draw:deleted';\n\n/**\n * @event draw:drawstart: String\n *\n * The type of layer this is. One of:`polyline`; `polygon`; `rectangle`; `circle`; `marker`\n *\n * Triggered when the user has chosen to draw a particular vector or marker.\n */\nL.Draw.Event.DRAWSTART = 'draw:drawstart';\n\n/**\n * @event draw:drawstop: String\n *\n * The type of layer this is. One of: `polyline`; `polygon`; `rectangle`; `circle`; `marker`\n *\n * Triggered when the user has finished a particular vector or marker.\n */\n\nL.Draw.Event.DRAWSTOP = 'draw:drawstop';\n\n/**\n * @event draw:drawvertex: LayerGroup\n *\n * List of all layers just being added from the map.\n *\n * Triggered when a vertex is created on a polyline or polygon.\n */\nL.Draw.Event.DRAWVERTEX = 'draw:drawvertex';\n\n/**\n * @event draw:editstart: String\n *\n * The type of edit this is. One of: `edit`\n *\n * Triggered when the user starts edit mode by clicking the edit tool button.\n */\n\nL.Draw.Event.EDITSTART = 'draw:editstart';\n\n/**\n * @event draw:editmove: ILayer\n *\n *  Layer that was just moved.\n *\n * Triggered as the user moves a rectangle; circle or marker.\n */\nL.Draw.Event.EDITMOVE = 'draw:editmove';\n\n/**\n * @event draw:editresize: ILayer\n *\n * Layer that was just moved.\n *\n * Triggered as the user resizes a rectangle or circle.\n */\nL.Draw.Event.EDITRESIZE = 'draw:editresize';\n\n/**\n * @event draw:editvertex: LayerGroup\n *\n * List of all layers just being edited from the map.\n *\n * Triggered when a vertex is edited on a polyline or polygon.\n */\nL.Draw.Event.EDITVERTEX = 'draw:editvertex';\n\n/**\n * @event draw:editstop: String\n *\n * The type of edit this is. One of: `edit`\n *\n * Triggered when the user has finshed editing (edit mode) and saves edits.\n */\nL.Draw.Event.EDITSTOP = 'draw:editstop';\n\n/**\n * @event draw:deletestart: String\n *\n * The type of edit this is. One of: `remove`\n *\n * Triggered when the user starts remove mode by clicking the remove tool button.\n */\nL.Draw.Event.DELETESTART = 'draw:deletestart';\n\n/**\n * @event draw:deletestop: String\n *\n * The type of edit this is. One of: `remove`\n *\n * Triggered when the user has finished removing shapes (remove mode) and saves.\n */\nL.Draw.Event.DELETESTOP = 'draw:deletestop';\n\n/**\n * @event draw:toolbaropened: String\n *\n * Triggered when a toolbar is opened.\n */\nL.Draw.Event.TOOLBAROPENED = 'draw:toolbaropened';\n\n/**\n * @event draw:toolbarclosed: String\n *\n * Triggered when a toolbar is closed.\n */\nL.Draw.Event.TOOLBARCLOSED = 'draw:toolbarclosed';\n\n/**\n * @event draw:markercontext: String\n *\n * Triggered when a marker is right clicked.\n */\nL.Draw.Event.MARKERCONTEXT = 'draw:markercontext';", "L.Draw = L.Draw || {};\n\n/**\n * @class L.Draw.Feature\n * @aka Draw.Feature\n */\nL.Draw.Feature = L.Handler.extend({\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\tthis._map = map;\n\t\tthis._container = map._container;\n\t\tthis._overlayPane = map._panes.overlayPane;\n\t\tthis._popupPane = map._panes.popupPane;\n\n\t\t// Merge default shapeOptions options with custom shapeOptions\n\t\tif (options && options.shapeOptions) {\n\t\t\toptions.shapeOptions = L.Util.extend({}, this.options.shapeOptions, options.shapeOptions);\n\t\t}\n\t\tL.setOptions(this, options);\n\n\t\tvar version = L.version.split('.');\n\t\t//If Version is >= 1.2.0\n\t\tif (parseInt(version[0], 10) === 1 && parseInt(version[1], 10) >= 2) {\n\t\t\tL.Draw.Feature.include(L.Evented.prototype);\n\t\t} else {\n\t\t\tL.Draw.Feature.include(L.Mixin.Events);\n\t\t}\n\t},\n\n\t// @method enable(): void\n\t// Enables this handler\n\tenable: function () {\n\t\tif (this._enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tL.Handler.prototype.enable.call(this);\n\n\t\tthis.fire('enabled', {handler: this.type});\n\n\t\tthis._map.fire(L.Draw.Event.DRAWSTART, {layerType: this.type});\n\t},\n\n\t// @method disable(): void\n\tdisable: function () {\n\t\tif (!this._enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tL.Handler.prototype.disable.call(this);\n\n\t\tthis._map.fire(L.Draw.Event.DRAWSTOP, {layerType: this.type});\n\n\t\tthis.fire('disabled', {handler: this.type});\n\t},\n\n\t// @method addHooks(): void\n\t// Add's event listeners to this handler\n\taddHooks: function () {\n\t\tvar map = this._map;\n\n\t\tif (map) {\n\t\t\tL.DomUtil.disableTextSelection();\n\n\t\t\tmap.getContainer().focus();\n\n\t\t\tthis._tooltip = new L.Draw.Tooltip(this._map);\n\n\t\t\tL.DomEvent.on(this._container, 'keyup', this._cancelDrawing, this);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Removes event listeners from this handler\n\tremoveHooks: function () {\n\t\tif (this._map) {\n\t\t\tL.DomUtil.enableTextSelection();\n\n\t\t\tthis._tooltip.dispose();\n\t\t\tthis._tooltip = null;\n\n\t\t\tL.DomEvent.off(this._container, 'keyup', this._cancelDrawing, this);\n\t\t}\n\t},\n\n\t// @method setOptions(object): void\n\t// Sets new options to this handler\n\tsetOptions: function (options) {\n\t\tL.setOptions(this, options);\n\t},\n\n\t_fireCreatedEvent: function (layer) {\n\t\tthis._map.fire(L.Draw.Event.CREATED, {layer: layer, layerType: this.type});\n\t},\n\n\t// Cancel drawing when the escape key is pressed\n\t_cancelDrawing: function (e) {\n\t\tif (e.keyCode === 27) {\n\t\t\tthis._map.fire('draw:canceled', {layerType: this.type});\n\t\t\tthis.disable();\n\t\t}\n\t}\n});\n", "/**\n * @class L.Draw.Polyline\n * @aka Draw.Polyline\n * @inherits L.Draw.Feature\n */\nL.Draw.Polyline = L.Draw.Feature.extend({\n\tstatics: {\n\t\tTYPE: 'polyline'\n\t},\n\n\tPoly: <PERSON><PERSON>,\n\n\toptions: {\n\t\tallowIntersection: true,\n\t\trepeatMode: false,\n\t\tdrawError: {\n\t\t\tcolor: '#b00b00',\n\t\t\ttimeout: 2500\n\t\t},\n\t\ticon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(8, 8),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon'\n\t\t}),\n\t\ttouchIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(20, 20),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-touch-icon'\n\t\t}),\n\t\tguidelineDistance: 20,\n\t\tmaxGuideLineLength: 4000,\n\t\tshapeOptions: {\n\t\t\tstroke: true,\n\t\t\tcolor: '#3388ff',\n\t\t\tweight: 4,\n\t\t\topacity: 0.5,\n\t\t\tfill: false,\n\t\t\tclickable: true\n\t\t},\n\t\tmetric: true, // Whether to use the metric measurement system or imperial\n\t\tfeet: true, // When not metric, to use feet instead of yards for display.\n\t\tnautic: false, // When not metric, not feet use nautic mile for display\n\t\tshowLength: true, // Whether to display distance in the tooltip\n\t\tzIndexOffset: 2000, // This should be > than the highest z-index any map layers\n\t\tfactor: 1, // To change distance calculation\n\t\tmaxPoints: 0 // Once this number of points are placed, finish shape\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\t// if touch, switch to touch icon\n\t\tif (L.Browser.touch) {\n\t\t\tthis.options.icon = this.options.touchIcon;\n\t\t}\n\n\t\t// Need to set this here to ensure the correct message is used.\n\t\tthis.options.drawError.message = L.drawLocal.draw.handlers.polyline.error;\n\n\t\t// Merge default drawError options with custom options\n\t\tif (options && options.drawError) {\n\t\t\toptions.drawError = L.Util.extend({}, this.options.drawError, options.drawError);\n\t\t}\n\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.Polyline.TYPE;\n\n\t\tL.Draw.Feature.prototype.initialize.call(this, map, options);\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler\n\taddHooks: function () {\n\t\tL.Draw.Feature.prototype.addHooks.call(this);\n\t\tif (this._map) {\n\t\t\tthis._markers = [];\n\n\t\t\tthis._markerGroup = new L.LayerGroup();\n\t\t\tthis._map.addLayer(this._markerGroup);\n\n\t\t\tthis._poly = new L.Polyline([], this.options.shapeOptions);\n\n\t\t\tthis._tooltip.updateContent(this._getTooltipText());\n\n\t\t\t// Make a transparent marker that will used to catch click events. These click\n\t\t\t// events will create the vertices. We need to do this so we can ensure that\n\t\t\t// we can create vertices over other map layers (markers, vector layers). We\n\t\t\t// also do not want to trigger any click handlers of objects we are clicking on\n\t\t\t// while drawing.\n\t\t\tif (!this._mouseMarker) {\n\t\t\t\tthis._mouseMarker = L.marker(this._map.getCenter(), {\n\t\t\t\t\ticon: L.divIcon({\n\t\t\t\t\t\tclassName: 'leaflet-mouse-marker',\n\t\t\t\t\t\ticonAnchor: [20, 20],\n\t\t\t\t\t\ticonSize: [40, 40]\n\t\t\t\t\t}),\n\t\t\t\t\topacity: 0,\n\t\t\t\t\tzIndexOffset: this.options.zIndexOffset\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tthis._mouseMarker\n\t\t\t\t.on('mouseout', this._onMouseOut, this)\n\t\t\t\t.on('mousemove', this._onMouseMove, this) // Necessary to prevent 0.8 stutter\n\t\t\t\t.on('mousedown', this._onMouseDown, this)\n\t\t\t\t.on('mouseup', this._onMouseUp, this) // Necessary for 0.8 compatibility\n\t\t\t\t.addTo(this._map);\n\n\t\t\tthis._map\n\t\t\t\t.on('mouseup', this._onMouseUp, this) // Necessary for 0.7 compatibility\n\t\t\t\t.on('mousemove', this._onMouseMove, this)\n\t\t\t\t.on('zoomlevelschange', this._onZoomEnd, this)\n\t\t\t\t.on('touchstart', this._onTouch, this)\n\t\t\t\t.on('zoomend', this._onZoomEnd, this);\n\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler.\n\tremoveHooks: function () {\n\t\tL.Draw.Feature.prototype.removeHooks.call(this);\n\n\t\tthis._clearHideErrorTimeout();\n\n\t\tthis._cleanUpShape();\n\n\t\t// remove markers from map\n\t\tthis._map.removeLayer(this._markerGroup);\n\t\tdelete this._markerGroup;\n\t\tdelete this._markers;\n\n\t\tthis._map.removeLayer(this._poly);\n\t\tdelete this._poly;\n\n\t\tthis._mouseMarker\n\t\t\t.off('mousedown', this._onMouseDown, this)\n\t\t\t.off('mouseout', this._onMouseOut, this)\n\t\t\t.off('mouseup', this._onMouseUp, this)\n\t\t\t.off('mousemove', this._onMouseMove, this);\n\t\tthis._map.removeLayer(this._mouseMarker);\n\t\tdelete this._mouseMarker;\n\n\t\t// clean up DOM\n\t\tthis._clearGuides();\n\n\t\tthis._map\n\t\t\t.off('mouseup', this._onMouseUp, this)\n\t\t\t.off('mousemove', this._onMouseMove, this)\n\t\t\t.off('zoomlevelschange', this._onZoomEnd, this)\n\t\t\t.off('zoomend', this._onZoomEnd, this)\n\t\t\t.off('touchstart', this._onTouch, this)\n\t\t\t.off('click', this._onTouch, this);\n\t},\n\n\t// @method deleteLastVertex(): void\n\t// Remove the last vertex from the polyline, removes polyline from map if only one point exists.\n\tdeleteLastVertex: function () {\n\t\tif (this._markers.length <= 1) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar lastMarker = this._markers.pop(),\n\t\t\tpoly = this._poly,\n\t\t\t// Replaces .spliceLatLngs()\n\t\t\tlatlngs = poly.getLatLngs(),\n\t\t\tlatlng = latlngs.splice(-1, 1)[0];\n\t\tthis._poly.setLatLngs(latlngs);\n\n\t\tthis._markerGroup.removeLayer(lastMarker);\n\n\t\tif (poly.getLatLngs().length < 2) {\n\t\t\tthis._map.removeLayer(poly);\n\t\t}\n\n\t\tthis._vertexChanged(latlng, false);\n\t},\n\n\t// @method addVertex(): void\n\t// Add a vertex to the end of the polyline\n\taddVertex: function (latlng) {\n\t\tvar markersLength = this._markers.length;\n\t\t// markersLength must be greater than or equal to 2 before intersections can occur\n\t\tif (markersLength >= 2 && !this.options.allowIntersection && this._poly.newLatLngIntersects(latlng)) {\n\t\t\tthis._showErrorTooltip();\n\t\t\treturn;\n\t\t}\n\t\telse if (this._errorShown) {\n\t\t\tthis._hideErrorTooltip();\n\t\t}\n\n\t\tthis._markers.push(this._createMarker(latlng));\n\n\t\tthis._poly.addLatLng(latlng);\n\n\t\tif (this._poly.getLatLngs().length === 2) {\n\t\t\tthis._map.addLayer(this._poly);\n\t\t}\n\n\t\tthis._vertexChanged(latlng, true);\n\t},\n\n\t// @method completeShape(): void\n\t// Closes the polyline between the first and last points\n\tcompleteShape: function () {\n\t\tif (this._markers.length <= 1 || !this._shapeIsValid()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._fireCreatedEvent();\n\t\tthis.disable();\n\n\t\tif (this.options.repeatMode) {\n\t\t\tthis.enable();\n\t\t}\n\t},\n\n\t_finishShape: function () {\n\t\tvar latlngs = this._poly._defaultShape ? this._poly._defaultShape() : this._poly.getLatLngs();\n\t\tvar intersects = this._poly.newLatLngIntersects(latlngs[latlngs.length - 1]);\n\n\t\tif ((!this.options.allowIntersection && intersects) || !this._shapeIsValid()) {\n\t\t\tthis._showErrorTooltip();\n\t\t\treturn;\n\t\t}\n\n\t\tthis._fireCreatedEvent();\n\t\tthis.disable();\n\t\tif (this.options.repeatMode) {\n\t\t\tthis.enable();\n\t\t}\n\t},\n\n\t// Called to verify the shape is valid when the user tries to finish it\n\t// Return false if the shape is not valid\n\t_shapeIsValid: function () {\n\t\treturn true;\n\t},\n\n\t_onZoomEnd: function () {\n\t\tif (this._markers !== null) {\n\t\t\tthis._updateGuide();\n\t\t}\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tvar newPos = this._map.mouseEventToLayerPoint(e.originalEvent);\n\t\tvar latlng = this._map.layerPointToLatLng(newPos);\n\n\t\t// Save latlng\n\t\t// should this be moved to _updateGuide() ?\n\t\tthis._currentLatLng = latlng;\n\n\t\tthis._updateTooltip(latlng);\n\n\t\t// Update the guide line\n\t\tthis._updateGuide(newPos);\n\n\t\t// Update the mouse marker position\n\t\tthis._mouseMarker.setLatLng(latlng);\n\n\t\tL.DomEvent.preventDefault(e.originalEvent);\n\t},\n\n\t_vertexChanged: function (latlng, added) {\n\t\tthis._map.fire(L.Draw.Event.DRAWVERTEX, {layers: this._markerGroup});\n\t\tthis._updateFinishHandler();\n\n\t\tthis._updateRunningMeasure(latlng, added);\n\n\t\tthis._clearGuides();\n\n\t\tthis._updateTooltip();\n\t},\n\n\t_onMouseDown: function (e) {\n\t\tif (!this._clickHandled && !this._touchHandled && !this._disableMarkers) {\n\t\t\tthis._onMouseMove(e);\n\t\t\tthis._clickHandled = true;\n\t\t\tthis._disableNewMarkers();\n\t\t\tvar originalEvent = e.originalEvent;\n\t\t\tvar clientX = originalEvent.clientX;\n\t\t\tvar clientY = originalEvent.clientY;\n\t\t\tthis._startPoint.call(this, clientX, clientY);\n\t\t}\n\t},\n\n\t_startPoint: function (clientX, clientY) {\n\t\tthis._mouseDownOrigin = L.point(clientX, clientY);\n\t},\n\n\t_onMouseUp: function (e) {\n\t\tvar originalEvent = e.originalEvent;\n\t\tvar clientX = originalEvent.clientX;\n\t\tvar clientY = originalEvent.clientY;\n\t\tthis._endPoint.call(this, clientX, clientY, e);\n\t\tthis._clickHandled = null;\n\t},\n\n\t_endPoint: function (clientX, clientY, e) {\n\t\tif (this._mouseDownOrigin) {\n\t\t\tvar dragCheckDistance = L.point(clientX, clientY)\n\t\t\t\t.distanceTo(this._mouseDownOrigin);\n\t\t\tvar lastPtDistance = this._calculateFinishDistance(e.latlng);\n\t\t\tif (this.options.maxPoints > 1 && this.options.maxPoints == this._markers.length + 1) {\n\t\t\t\tthis.addVertex(e.latlng);\n\t\t\t\tthis._finishShape();\n\t\t\t} else if (lastPtDistance < 10 && L.Browser.touch) {\n\t\t\t\tthis._finishShape();\n\t\t\t} else if (Math.abs(dragCheckDistance) < 9 * (window.devicePixelRatio || 1)) {\n\t\t\t\tthis.addVertex(e.latlng);\n\t\t\t}\n\t\t\tthis._enableNewMarkers(); // after a short pause, enable new markers\n\t\t}\n\t\tthis._mouseDownOrigin = null;\n\t},\n\n\t// ontouch prevented by clickHandled flag because some browsers fire both click/touch events,\n\t// causing unwanted behavior\n\t_onTouch: function (e) {\n\t\tvar originalEvent = e.originalEvent;\n\t\tvar clientX;\n\t\tvar clientY;\n\t\tif (originalEvent.touches && originalEvent.touches[0] && !this._clickHandled && !this._touchHandled && !this._disableMarkers) {\n\t\t\tclientX = originalEvent.touches[0].clientX;\n\t\t\tclientY = originalEvent.touches[0].clientY;\n\t\t\tthis._disableNewMarkers();\n\t\t\tthis._touchHandled = true;\n\t\t\tthis._startPoint.call(this, clientX, clientY);\n\t\t\tthis._endPoint.call(this, clientX, clientY, e);\n\t\t\tthis._touchHandled = null;\n\t\t}\n\t\tthis._clickHandled = null;\n\t},\n\n\t_onMouseOut: function () {\n\t\tif (this._tooltip) {\n\t\t\tthis._tooltip._onMouseOut.call(this._tooltip);\n\t\t}\n\t},\n\n\t// calculate if we are currently within close enough distance\n\t// of the closing point (first point for shapes, last point for lines)\n\t// this is semi-ugly code but the only reliable way i found to get the job done\n\t// note: calculating point.distanceTo between mouseDownOrigin and last marker did NOT work\n\t_calculateFinishDistance: function (potentialLatLng) {\n\t\tvar lastPtDistance;\n\t\tif (this._markers.length > 0) {\n\t\t\tvar finishMarker;\n\t\t\tif (this.type === L.Draw.Polyline.TYPE) {\n\t\t\t\tfinishMarker = this._markers[this._markers.length - 1];\n\t\t\t} else if (this.type === L.Draw.Polygon.TYPE) {\n\t\t\t\tfinishMarker = this._markers[0];\n\t\t\t} else {\n\t\t\t\treturn Infinity;\n\t\t\t}\n\t\t\tvar lastMarkerPoint = this._map.latLngToContainerPoint(finishMarker.getLatLng()),\n\t\t\t\tpotentialMarker = new L.Marker(potentialLatLng, {\n\t\t\t\t\ticon: this.options.icon,\n\t\t\t\t\tzIndexOffset: this.options.zIndexOffset * 2\n\t\t\t\t});\n\t\t\tvar potentialMarkerPint = this._map.latLngToContainerPoint(potentialMarker.getLatLng());\n\t\t\tlastPtDistance = lastMarkerPoint.distanceTo(potentialMarkerPint);\n\t\t} else {\n\t\t\tlastPtDistance = Infinity;\n\t\t}\n\t\treturn lastPtDistance;\n\t},\n\n\t_updateFinishHandler: function () {\n\t\tvar markerCount = this._markers.length;\n\t\t// The last marker should have a click handler to close the polyline\n\t\tif (markerCount > 1) {\n\t\t\tthis._markers[markerCount - 1].on('click', this._finishShape, this);\n\t\t}\n\n\t\t// Remove the old marker click handler (as only the last point should close the polyline)\n\t\tif (markerCount > 2) {\n\t\t\tthis._markers[markerCount - 2].off('click', this._finishShape, this);\n\t\t}\n\t},\n\n\t_createMarker: function (latlng) {\n\t\tvar marker = new L.Marker(latlng, {\n\t\t\ticon: this.options.icon,\n\t\t\tzIndexOffset: this.options.zIndexOffset * 2\n\t\t});\n\n\t\tthis._markerGroup.addLayer(marker);\n\n\t\treturn marker;\n\t},\n\n\t_updateGuide: function (newPos) {\n\t\tvar markerCount = this._markers ? this._markers.length : 0;\n\n\t\tif (markerCount > 0) {\n\t\t\tnewPos = newPos || this._map.latLngToLayerPoint(this._currentLatLng);\n\n\t\t\t// draw the guide line\n\t\t\tthis._clearGuides();\n\t\t\tthis._drawGuide(\n\t\t\t\tthis._map.latLngToLayerPoint(this._markers[markerCount - 1].getLatLng()),\n\t\t\t\tnewPos\n\t\t\t);\n\t\t}\n\t},\n\n\t_updateTooltip: function (latLng) {\n\t\tvar text = this._getTooltipText();\n\n\t\tif (latLng) {\n\t\t\tthis._tooltip.updatePosition(latLng);\n\t\t}\n\n\t\tif (!this._errorShown) {\n\t\t\tthis._tooltip.updateContent(text);\n\t\t}\n\t},\n\n\t_drawGuide: function (pointA, pointB) {\n\t\tvar length = Math.floor(Math.sqrt(Math.pow((pointB.x - pointA.x), 2) + Math.pow((pointB.y - pointA.y), 2))),\n\t\t\tguidelineDistance = this.options.guidelineDistance,\n\t\t\tmaxGuideLineLength = this.options.maxGuideLineLength,\n\t\t\t// Only draw a guideline with a max length\n\t\t\ti = length > maxGuideLineLength ? length - maxGuideLineLength : guidelineDistance,\n\t\t\tfraction,\n\t\t\tdashPoint,\n\t\t\tdash;\n\n\t\t//create the guides container if we haven't yet\n\t\tif (!this._guidesContainer) {\n\t\t\tthis._guidesContainer = L.DomUtil.create('div', 'leaflet-draw-guides', this._overlayPane);\n\t\t}\n\n\t\t//draw a dash every GuildeLineDistance\n\t\tfor (; i < length; i += this.options.guidelineDistance) {\n\t\t\t//work out fraction along line we are\n\t\t\tfraction = i / length;\n\n\t\t\t//calculate new x,y point\n\t\t\tdashPoint = {\n\t\t\t\tx: Math.floor((pointA.x * (1 - fraction)) + (fraction * pointB.x)),\n\t\t\t\ty: Math.floor((pointA.y * (1 - fraction)) + (fraction * pointB.y))\n\t\t\t};\n\n\t\t\t//add guide dash to guide container\n\t\t\tdash = L.DomUtil.create('div', 'leaflet-draw-guide-dash', this._guidesContainer);\n\t\t\tdash.style.backgroundColor =\n\t\t\t\t!this._errorShown ? this.options.shapeOptions.color : this.options.drawError.color;\n\n\t\t\tL.DomUtil.setPosition(dash, dashPoint);\n\t\t}\n\t},\n\n\t_updateGuideColor: function (color) {\n\t\tif (this._guidesContainer) {\n\t\t\tfor (var i = 0, l = this._guidesContainer.childNodes.length; i < l; i++) {\n\t\t\t\tthis._guidesContainer.childNodes[i].style.backgroundColor = color;\n\t\t\t}\n\t\t}\n\t},\n\n\t// removes all child elements (guide dashes) from the guides container\n\t_clearGuides: function () {\n\t\tif (this._guidesContainer) {\n\t\t\twhile (this._guidesContainer.firstChild) {\n\t\t\t\tthis._guidesContainer.removeChild(this._guidesContainer.firstChild);\n\t\t\t}\n\t\t}\n\t},\n\n\t_getTooltipText: function () {\n\t\tvar showLength = this.options.showLength,\n\t\t\tlabelText, distanceStr;\n\t\tif (this._markers.length === 0) {\n\t\t\tlabelText = {\n\t\t\t\ttext: L.drawLocal.draw.handlers.polyline.tooltip.start\n\t\t\t};\n\t\t} else {\n\t\t\tdistanceStr = showLength ? this._getMeasurementString() : '';\n\n\t\t\tif (this._markers.length === 1) {\n\t\t\t\tlabelText = {\n\t\t\t\t\ttext: L.drawLocal.draw.handlers.polyline.tooltip.cont,\n\t\t\t\t\tsubtext: distanceStr\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tlabelText = {\n\t\t\t\t\ttext: L.drawLocal.draw.handlers.polyline.tooltip.end,\n\t\t\t\t\tsubtext: distanceStr\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\treturn labelText;\n\t},\n\n\t_updateRunningMeasure: function (latlng, added) {\n\t\tvar markersLength = this._markers.length,\n\t\t\tpreviousMarkerIndex, distance;\n\n\t\tif (this._markers.length === 1) {\n\t\t\tthis._measurementRunningTotal = 0;\n\t\t} else {\n\t\t\tpreviousMarkerIndex = markersLength - (added ? 2 : 1);\n\n\t\t\t// Calculate the distance based on the version\n\t\t\tif (L.GeometryUtil.isVersion07x()) {\n\t\t\t\tdistance = latlng.distanceTo(this._markers[previousMarkerIndex].getLatLng()) * (this.options.factor || 1);\n\t\t\t} else {\n\t\t\t\tdistance = this._map.distance(latlng, this._markers[previousMarkerIndex].getLatLng()) * (this.options.factor || 1);\n\t\t\t}\n\n\t\t\tthis._measurementRunningTotal += distance * (added ? 1 : -1);\n\t\t}\n\t},\n\n\t_getMeasurementString: function () {\n\t\tvar currentLatLng = this._currentLatLng,\n\t\t\tpreviousLatLng = this._markers[this._markers.length - 1].getLatLng(),\n\t\t\tdistance;\n\n\t\t// Calculate the distance from the last fixed point to the mouse position based on the version\n\t\tif (L.GeometryUtil.isVersion07x()) {\n\t\t\tdistance = previousLatLng && currentLatLng && currentLatLng.distanceTo ? this._measurementRunningTotal + currentLatLng.distanceTo(previousLatLng) * (this.options.factor || 1) : this._measurementRunningTotal || 0;\n\t\t} else {\n\t\t\tdistance = previousLatLng && currentLatLng ? this._measurementRunningTotal + this._map.distance(currentLatLng, previousLatLng) * (this.options.factor || 1) : this._measurementRunningTotal || 0;\n\t\t}\n\n\t\treturn L.GeometryUtil.readableDistance(distance, this.options.metric, this.options.feet, this.options.nautic, this.options.precision);\n\t},\n\n\t_showErrorTooltip: function () {\n\t\tthis._errorShown = true;\n\n\t\t// Update tooltip\n\t\tthis._tooltip\n\t\t\t.showAsError()\n\t\t\t.updateContent({text: this.options.drawError.message});\n\n\t\t// Update shape\n\t\tthis._updateGuideColor(this.options.drawError.color);\n\t\tthis._poly.setStyle({color: this.options.drawError.color});\n\n\t\t// Hide the error after 2 seconds\n\t\tthis._clearHideErrorTimeout();\n\t\tthis._hideErrorTimeout = setTimeout(L.Util.bind(this._hideErrorTooltip, this), this.options.drawError.timeout);\n\t},\n\n\t_hideErrorTooltip: function () {\n\t\tthis._errorShown = false;\n\n\t\tthis._clearHideErrorTimeout();\n\n\t\t// Revert tooltip\n\t\tthis._tooltip\n\t\t\t.removeError()\n\t\t\t.updateContent(this._getTooltipText());\n\n\t\t// Revert shape\n\t\tthis._updateGuideColor(this.options.shapeOptions.color);\n\t\tthis._poly.setStyle({color: this.options.shapeOptions.color});\n\t},\n\n\t_clearHideErrorTimeout: function () {\n\t\tif (this._hideErrorTimeout) {\n\t\t\tclearTimeout(this._hideErrorTimeout);\n\t\t\tthis._hideErrorTimeout = null;\n\t\t}\n\t},\n\n\t// disable new markers temporarily;\n\t// this is to prevent duplicated touch/click events in some browsers\n\t_disableNewMarkers: function () {\n\t\tthis._disableMarkers = true;\n\t},\n\n\t// see _disableNewMarkers\n\t_enableNewMarkers: function () {\n\t\tsetTimeout(function () {\n\t\t\tthis._disableMarkers = false;\n\t\t}.bind(this), 50);\n\t},\n\n\t_cleanUpShape: function () {\n\t\tif (this._markers.length > 1) {\n\t\t\tthis._markers[this._markers.length - 1].off('click', this._finishShape, this);\n\t\t}\n\t},\n\n\t_fireCreatedEvent: function () {\n\t\tvar poly = new this.Poly(this._poly.getLatLngs(), this.options.shapeOptions);\n\t\tL.Draw.Feature.prototype._fireCreatedEvent.call(this, poly);\n\t}\n});\n", "/**\n * @class L.Draw.Polygon\n * @aka Draw.Polygon\n * @inherits L.Draw.Polyline\n */\nL.Draw.Polygon = L.Draw.Polyline.extend({\n\tstatics: {\n\t\tTYPE: 'polygon'\n\t},\n\n\tPoly: L.Polygon,\n\n\toptions: {\n\t\tshowArea: false,\n\t\tshowLength: false,\n\t\tshapeOptions: {\n\t\t\tstroke: true,\n\t\t\tcolor: '#3388ff',\n\t\t\tweight: 4,\n\t\t\topacity: 0.5,\n\t\t\tfill: true,\n\t\t\tfillColor: null, //same as color by default\n\t\t\tfillOpacity: 0.2,\n\t\t\tclickable: true\n\t\t},\n\t\t// Whether to use the metric measurement system (truthy) or not (falsy).\n\t\t// Also defines the units to use for the metric system as an array of\n\t\t// strings (e.g. `['ha', 'm']`).\n\t\tmetric: true,\n\t\tfeet: true, // When not metric, to use feet instead of yards for display.\n\t\tnautic: false, // When not metric, not feet use nautic mile for display\n\t\t// Defines the precision for each type of unit (e.g. {km: 2, ft: 0}\n\t\tprecision: {}\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\tL.Draw.Polyline.prototype.initialize.call(this, map, options);\n\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.Polygon.TYPE;\n\t},\n\n\t_updateFinishHandler: function () {\n\t\tvar markerCount = this._markers.length;\n\n\t\t// The first marker should have a click handler to close the polygon\n\t\tif (markerCount === 1) {\n\t\t\tthis._markers[0].on('click', this._finishShape, this);\n\t\t}\n\n\t\t// Add and update the double click handler\n\t\tif (markerCount > 2) {\n\t\t\tthis._markers[markerCount - 1].on('dblclick', this._finishShape, this);\n\t\t\t// Only need to remove handler if has been added before\n\t\t\tif (markerCount > 3) {\n\t\t\t\tthis._markers[markerCount - 2].off('dblclick', this._finishShape, this);\n\t\t\t}\n\t\t}\n\t},\n\n\t_getTooltipText: function () {\n\t\tvar text, subtext;\n\n\t\tif (this._markers.length === 0) {\n\t\t\ttext = L.drawLocal.draw.handlers.polygon.tooltip.start;\n\t\t} else if (this._markers.length < 3) {\n\t\t\ttext = L.drawLocal.draw.handlers.polygon.tooltip.cont;\n\t\t\tsubtext = this._getMeasurementString();\n\t\t} else {\n\t\t\ttext = L.drawLocal.draw.handlers.polygon.tooltip.end;\n\t\t\tsubtext = this._getMeasurementString();\n\t\t}\n\n\t\treturn {\n\t\t\ttext: text,\n\t\t\tsubtext: subtext\n\t\t};\n\t},\n\n\t_getMeasurementString: function () {\n\t\tvar area = this._area,\n\t\t\tmeasurementString = '';\n\n\n\t\tif (!area && !this.options.showLength) {\n\t\t\treturn null;\n\t\t}\n\n\t\tif (this.options.showLength) {\n\t\t\tmeasurementString = L.Draw.Polyline.prototype._getMeasurementString.call(this);\n\t\t}\n\n\t\tif (area) {\n\t\t\tmeasurementString += '<br>' + L.GeometryUtil.readableArea(area, this.options.metric, this.options.precision);\n\t\t}\n\n\t\treturn measurementString;\n\t},\n\n\t_shapeIsValid: function () {\n\t\treturn this._markers.length >= 3;\n\t},\n\n\t_vertexChanged: function (latlng, added) {\n\t\tvar latLngs;\n\n\t\t// Check to see if we should show the area\n\t\tif (!this.options.allowIntersection && this.options.showArea) {\n\t\t\tlatLngs = this._poly.getLatLngs();\n\n\t\t\tthis._area = L.GeometryUtil.geodesicArea(latLngs);\n\t\t}\n\n\t\tL.Draw.Polyline.prototype._vertexChanged.call(this, latlng, added);\n\t},\n\n\t_cleanUpShape: function () {\n\t\tvar markerCount = this._markers.length;\n\n\t\tif (markerCount > 0) {\n\t\t\tthis._markers[0].off('click', this._finishShape, this);\n\n\t\t\tif (markerCount > 2) {\n\t\t\t\tthis._markers[markerCount - 1].off('dblclick', this._finishShape, this);\n\t\t\t}\n\t\t}\n\t}\n});\n", "L.SimpleShape = {};\n/**\n * @class L.Draw.SimpleShape\n * @aka Draw.SimpleShape\n * @inherits L.Draw.Feature\n */\nL.Draw.SimpleShape = L.Draw.Feature.extend({\n\toptions: {\n\t\trepeatMode: false\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\tthis._endLabelText = L.drawLocal.draw.handlers.simpleshape.tooltip.end;\n\n\t\tL.Draw.Feature.prototype.initialize.call(this, map, options);\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler.\n\taddHooks: function () {\n\t\tL.Draw.Feature.prototype.addHooks.call(this);\n\t\tif (this._map) {\n\t\t\tthis._mapDraggable = this._map.dragging.enabled();\n\n\t\t\tif (this._mapDraggable) {\n\t\t\t\tthis._map.dragging.disable();\n\t\t\t}\n\n\t\t\t//TODO refactor: move cursor to styles\n\t\t\tthis._container.style.cursor = 'crosshair';\n\n\t\t\tthis._tooltip.updateContent({text: this._initialLabelText});\n\n\t\t\tthis._map\n\t\t\t\t.on('mousedown', this._onMouseDown, this)\n\t\t\t\t.on('mousemove', this._onMouseMove, this)\n\t\t\t\t.on('touchstart', this._onMouseDown, this)\n\t\t\t\t.on('touchmove', this._onMouseMove, this);\n\n\t\t\t// we should prevent default, otherwise default behavior (scrolling) will fire,\n\t\t\t// and that will cause document.touchend to fire and will stop the drawing\n\t\t\t// (circle, rectangle) in touch mode.\n\t\t\t// (update): we have to send passive now to prevent scroll, because by default it is {passive: true} now, which means,\n\t\t\t// handler can't event.preventDefault\n\t\t\t// check the news https://developers.google.com/web/updates/2016/06/passive-event-listeners\n\t\t\tdocument.addEventListener('touchstart', L.DomEvent.preventDefault, {passive: false});\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler.\n\tremoveHooks: function () {\n\t\tL.Draw.Feature.prototype.removeHooks.call(this);\n\t\tif (this._map) {\n\t\t\tif (this._mapDraggable) {\n\t\t\t\tthis._map.dragging.enable();\n\t\t\t}\n\n\t\t\t//TODO refactor: move cursor to styles\n\t\t\tthis._container.style.cursor = '';\n\n\t\t\tthis._map\n\t\t\t\t.off('mousedown', this._onMouseDown, this)\n\t\t\t\t.off('mousemove', this._onMouseMove, this)\n\t\t\t\t.off('touchstart', this._onMouseDown, this)\n\t\t\t\t.off('touchmove', this._onMouseMove, this);\n\n\t\t\tL.DomEvent.off(document, 'mouseup', this._onMouseUp, this);\n\t\t\tL.DomEvent.off(document, 'touchend', this._onMouseUp, this);\n\n\t\t\tdocument.removeEventListener('touchstart', L.DomEvent.preventDefault);\n\n\t\t\t// If the box element doesn't exist they must not have moved the mouse, so don't need to destroy/return\n\t\t\tif (this._shape) {\n\t\t\t\tthis._map.removeLayer(this._shape);\n\t\t\t\tdelete this._shape;\n\t\t\t}\n\t\t}\n\t\tthis._isDrawing = false;\n\t},\n\n\t_getTooltipText: function () {\n\t\treturn {\n\t\t\ttext: this._endLabelText\n\t\t};\n\t},\n\n\t_onMouseDown: function (e) {\n\t\tthis._isDrawing = true;\n\t\tthis._startLatLng = e.latlng;\n\n\t\tL.DomEvent\n\t\t\t.on(document, 'mouseup', this._onMouseUp, this)\n\t\t\t.on(document, 'touchend', this._onMouseUp, this)\n\t\t\t.preventDefault(e.originalEvent);\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tvar latlng = e.latlng;\n\n\t\tthis._tooltip.updatePosition(latlng);\n\t\tif (this._isDrawing) {\n\t\t\tthis._tooltip.updateContent(this._getTooltipText());\n\t\t\tthis._drawShape(latlng);\n\t\t}\n\t},\n\n\t_onMouseUp: function () {\n\t\tif (this._shape) {\n\t\t\tthis._fireCreatedEvent();\n\t\t}\n\n\t\tthis.disable();\n\t\tif (this.options.repeatMode) {\n\t\t\tthis.enable();\n\t\t}\n\t}\n});\n", "/**\n * @class L.Draw.Rectangle\n * @aka Draw.Rectangle\n * @inherits L.Draw.SimpleShape\n */\nL.Draw.Rectangle = L.Draw.SimpleShape.extend({\n\tstatics: {\n\t\tTYPE: 'rectangle'\n\t},\n\n\toptions: {\n\t\tshapeOptions: {\n\t\t\tstroke: true,\n\t\t\tcolor: '#3388ff',\n\t\t\tweight: 4,\n\t\t\topacity: 0.5,\n\t\t\tfill: true,\n\t\t\tfillColor: null, //same as color by default\n\t\t\tfillOpacity: 0.2,\n\t\t\tclickable: true\n\t\t},\n\t\tshowArea: true, //Whether to show the area in the tooltip\n\t\tmetric: true // Whether to use the metric measurement system or imperial\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.Rectangle.TYPE;\n\n\t\tthis._initialLabelText = L.drawLocal.draw.handlers.rectangle.tooltip.start;\n\n\t\tL.Draw.SimpleShape.prototype.initialize.call(this, map, options);\n\t},\n\n\t// @method disable(): void\n\tdisable: function () {\n\t\tif (!this._enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._isCurrentlyTwoClickDrawing = false;\n\t\tL.Draw.SimpleShape.prototype.disable.call(this);\n\t},\n\n\t_onMouseUp: function (e) {\n\t\tif (!this._shape && !this._isCurrentlyTwoClickDrawing) {\n\t\t\tthis._isCurrentlyTwoClickDrawing = true;\n\t\t\treturn;\n\t\t}\n\n\t\t// Make sure closing click is on map\n\t\tif (this._isCurrentlyTwoClickDrawing && !_hasAncestor(e.target, 'leaflet-pane')) {\n\t\t\treturn;\n\t\t}\n\n\t\tL.Draw.SimpleShape.prototype._onMouseUp.call(this);\n\t},\n\n\t_drawShape: function (latlng) {\n\t\tif (!this._shape) {\n\t\t\tthis._shape = new L.Rectangle(new L.LatLngBounds(this._startLatLng, latlng), this.options.shapeOptions);\n\t\t\tthis._map.addLayer(this._shape);\n\t\t} else {\n\t\t\tthis._shape.setBounds(new L.LatLngBounds(this._startLatLng, latlng));\n\t\t}\n\t},\n\n\t_fireCreatedEvent: function () {\n\t\tvar rectangle = new L.Rectangle(this._shape.getBounds(), this.options.shapeOptions);\n\t\tL.Draw.SimpleShape.prototype._fireCreatedEvent.call(this, rectangle);\n\t},\n\n\t_getTooltipText: function () {\n\t\tvar tooltipText = L.Draw.SimpleShape.prototype._getTooltipText.call(this),\n\t\t\tshape = this._shape,\n\t\t\tshowArea = this.options.showArea,\n\t\t\tlatLngs, area, subtext;\n\n\t\tif (shape) {\n\t\t\tlatLngs = this._shape._defaultShape ? this._shape._defaultShape() : this._shape.getLatLngs();\n\t\t\tarea = L.GeometryUtil.geodesicArea(latLngs);\n\t\t\tsubtext = showArea ? L.GeometryUtil.readableArea(area, this.options.metric) : '';\n\t\t}\n\n\t\treturn {\n\t\t\ttext: tooltipText.text,\n\t\t\tsubtext: subtext\n\t\t};\n\t}\n});\n\nfunction _hasAncestor(el, cls) {\n\twhile ((el = el.parentElement) && !el.classList.contains(cls)) {\n\t\t;\n\t}\n\treturn el;\n}\n", "/**\n * @class L.Draw.Marker\n * @aka Draw.Marker\n * @inherits L.Draw.Feature\n */\nL.Draw.Marker = L.Draw.Feature.extend({\n\tstatics: {\n\t\tTYPE: 'marker'\n\t},\n\n\toptions: {\n\t\ticon: new L.Icon.Default(),\n\t\trepeatMode: false,\n\t\tzIndexOffset: 2000 // This should be > than the highest z-index any markers\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.Marker.TYPE;\n\n\t\tthis._initialLabelText = L.drawLocal.draw.handlers.marker.tooltip.start;\n\n\t\tL.Draw.Feature.prototype.initialize.call(this, map, options);\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler.\n\taddHooks: function () {\n\t\tL.Draw.Feature.prototype.addHooks.call(this);\n\n\t\tif (this._map) {\n\t\t\tthis._tooltip.updateContent({text: this._initialLabelText});\n\n\t\t\t// Same mouseMarker as in Draw.Polyline\n\t\t\tif (!this._mouseMarker) {\n\t\t\t\tthis._mouseMarker = L.marker(this._map.getCenter(), {\n\t\t\t\t\ticon: L.divIcon({\n\t\t\t\t\t\tclassName: 'leaflet-mouse-marker',\n\t\t\t\t\t\ticonAnchor: [20, 20],\n\t\t\t\t\t\ticonSize: [40, 40]\n\t\t\t\t\t}),\n\t\t\t\t\topacity: 0,\n\t\t\t\t\tzIndexOffset: this.options.zIndexOffset\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tthis._mouseMarker\n\t\t\t\t.on('click', this._onClick, this)\n\t\t\t\t.addTo(this._map);\n\n\t\t\tthis._map.on('mousemove', this._onMouseMove, this);\n\t\t\tthis._map.on('click', this._onTouch, this);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler.\n\tremoveHooks: function () {\n\t\tL.Draw.Feature.prototype.removeHooks.call(this);\n\n\t\tif (this._map) {\n\t\t\tthis._map\n\t\t\t\t.off('click', this._onClick, this)\n\t\t\t\t.off('click', this._onTouch, this);\n\t\t\tif (this._marker) {\n\t\t\t\tthis._marker.off('click', this._onClick, this);\n\t\t\t\tthis._map\n\t\t\t\t\t.removeLayer(this._marker);\n\t\t\t\tdelete this._marker;\n\t\t\t}\n\n\t\t\tthis._mouseMarker.off('click', this._onClick, this);\n\t\t\tthis._map.removeLayer(this._mouseMarker);\n\t\t\tdelete this._mouseMarker;\n\n\t\t\tthis._map.off('mousemove', this._onMouseMove, this);\n\t\t}\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tvar latlng = e.latlng;\n\n\t\tthis._tooltip.updatePosition(latlng);\n\t\tthis._mouseMarker.setLatLng(latlng);\n\n\t\tif (!this._marker) {\n\t\t\tthis._marker = this._createMarker(latlng);\n\t\t\t// Bind to both marker and map to make sure we get the click event.\n\t\t\tthis._marker.on('click', this._onClick, this);\n\t\t\tthis._map\n\t\t\t\t.on('click', this._onClick, this)\n\t\t\t\t.addLayer(this._marker);\n\t\t}\n\t\telse {\n\t\t\tlatlng = this._mouseMarker.getLatLng();\n\t\t\tthis._marker.setLatLng(latlng);\n\t\t}\n\t},\n\n\t_createMarker: function (latlng) {\n\t\treturn new L.Marker(latlng, {\n\t\t\ticon: this.options.icon,\n\t\t\tzIndexOffset: this.options.zIndexOffset\n\t\t});\n\t},\n\n\t_onClick: function () {\n\t\tthis._fireCreatedEvent();\n\n\t\tthis.disable();\n\t\tif (this.options.repeatMode) {\n\t\t\tthis.enable();\n\t\t}\n\t},\n\n\t_onTouch: function (e) {\n\t\t// called on click & tap, only really does any thing on tap\n\t\tthis._onMouseMove(e); // creates & places marker\n\t\tthis._onClick(); // permanently places marker & ends interaction\n\t},\n\n\t_fireCreatedEvent: function () {\n\t\tvar marker = new L.Marker.Touch(this._marker.getLatLng(), {icon: this.options.icon});\n\t\tL.Draw.Feature.prototype._fireCreatedEvent.call(this, marker);\n\t}\n});\n", "/**\n * @class L.Draw.CircleMarker\n * @aka Draw.CircleMarker\n * @inherits L.Draw.Marker\n */\nL.Draw.CircleMarker = L.Draw.Marker.extend({\n\tstatics: {\n\t\tTYPE: 'circlemarker'\n\t},\n\n\toptions: {\n\t\tstroke: true,\n\t\tcolor: '#3388ff',\n\t\tweight: 4,\n\t\topacity: 0.5,\n\t\tfill: true,\n\t\tfillColor: null, //same as color by default\n\t\tfillOpacity: 0.2,\n\t\tclickable: true,\n\t\tzIndexOffset: 2000 // This should be > than the highest z-index any markers\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.CircleMarker.TYPE;\n\n\t\tthis._initialLabelText = L.drawLocal.draw.handlers.circlemarker.tooltip.start;\n\n\t\tL.Draw.Feature.prototype.initialize.call(this, map, options);\n\t},\n\n\n\t_fireCreatedEvent: function () {\n\t\tvar circleMarker = new L.CircleMarker(this._marker.getLatLng(), this.options);\n\t\tL.Draw.Feature.prototype._fireCreatedEvent.call(this, circleMarker);\n\t},\n\n\t_createMarker: function (latlng) {\n\t\treturn new L.CircleMarker(latlng, this.options);\n\t}\n});\n", "/**\n * @class L.Draw.Circle\n * @aka Draw.Circle\n * @inherits L.Draw.SimpleShape\n */\nL.Draw.Circle = L.Draw.SimpleShape.extend({\n\tstatics: {\n\t\tTYPE: 'circle'\n\t},\n\n\toptions: {\n\t\tshapeOptions: {\n\t\t\tstroke: true,\n\t\t\tcolor: '#3388ff',\n\t\t\tweight: 4,\n\t\t\topacity: 0.5,\n\t\t\tfill: true,\n\t\t\tfillColor: null, //same as color by default\n\t\t\tfillOpacity: 0.2,\n\t\t\tclickable: true\n\t\t},\n\t\tshowRadius: true,\n\t\tmetric: true, // Whether to use the metric measurement system or imperial\n\t\tfeet: true, // When not metric, use feet instead of yards for display\n\t\tnautic: false // When not metric, not feet use nautic mile for display\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (map, options) {\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.Draw.Circle.TYPE;\n\n\t\tthis._initialLabelText = L.drawLocal.draw.handlers.circle.tooltip.start;\n\n\t\tL.Draw.SimpleShape.prototype.initialize.call(this, map, options);\n\t},\n\n\t_drawShape: function (latlng) {\n\t\t// Calculate the distance based on the version\n\t\tif (L.GeometryUtil.isVersion07x()) {\n\t\t\tvar distance = this._startLatLng.distanceTo(latlng);\n\t\t} else {\n\t\t\tvar distance = this._map.distance(this._startLatLng, latlng);\n\t\t}\n\n\t\tif (!this._shape) {\n\t\t\tthis._shape = new L.Circle(this._startLatLng, distance, this.options.shapeOptions);\n\t\t\tthis._map.addLayer(this._shape);\n\t\t} else {\n\t\t\tthis._shape.setRadius(distance);\n\t\t}\n\t},\n\n\t_fireCreatedEvent: function () {\n\t\tvar circle = new L.Circle(this._startLatLng, this._shape.getRadius(), this.options.shapeOptions);\n\t\tL.Draw.SimpleShape.prototype._fireCreatedEvent.call(this, circle);\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tvar latlng = e.latlng,\n\t\t\tshowRadius = this.options.showRadius,\n\t\t\tuseMetric = this.options.metric,\n\t\t\tradius;\n\n\t\tthis._tooltip.updatePosition(latlng);\n\t\tif (this._isDrawing) {\n\t\t\tthis._drawShape(latlng);\n\n\t\t\t// Get the new radius (rounded to 1 dp)\n\t\t\tradius = this._shape.getRadius().toFixed(1);\n\n\t\t\tvar subtext = '';\n\t\t\tif (showRadius) {\n\t\t\t\tsubtext = L.drawLocal.draw.handlers.circle.radius + ': ' +\n\t\t\t\t\tL.GeometryUtil.readableDistance(radius, useMetric, this.options.feet, this.options.nautic);\n\t\t\t}\n\t\t\tthis._tooltip.updateContent({\n\t\t\t\ttext: this._endLabelText,\n\t\t\t\tsubtext: subtext\n\t\t\t});\n\t\t}\n\t}\n});\n", "L.Edit = L.Edit || {};\n\n/**\n * @class L.Edit.Marker\n * @aka Edit.Marker\n */\nL.Edit.Marker = <PERSON>.<PERSON>.extend({\n\t// @method initialize(): void\n\tinitialize: function (marker, options) {\n\t\tthis._marker = marker;\n\t\tL.setOptions(this, options);\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler\n\taddHooks: function () {\n\t\tvar marker = this._marker;\n\n\t\tmarker.dragging.enable();\n\t\tmarker.on('dragend', this._onDragEnd, marker);\n\t\tthis._toggleMarkerHighlight();\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler\n\tremoveHooks: function () {\n\t\tvar marker = this._marker;\n\n\t\tmarker.dragging.disable();\n\t\tmarker.off('dragend', this._onDragEnd, marker);\n\t\tthis._toggleMarkerHighlight();\n\t},\n\n\t_onDragEnd: function (e) {\n\t\tvar layer = e.target;\n\t\tlayer.edited = true;\n\t\tthis._map.fire(L.Draw.Event.EDITMOVE, {layer: layer});\n\t},\n\n\t_toggleMarkerHighlight: function () {\n\t\tvar icon = this._marker._icon;\n\n\t\t// Don't do anything if this layer is a marker but doesn't have an icon. Markers\n\t\t// should usually have icons. If using Leaflet.draw with Leaflet.markercluster there\n\t\t// is a chance that a marker doesn't.\n\t\tif (!icon) {\n\t\t\treturn;\n\t\t}\n\n\t\t// This is quite naughty, but I don't see another way of doing it. (short of setting a new icon)\n\t\ticon.style.display = 'none';\n\n\t\tif (L.DomUtil.hasClass(icon, 'leaflet-edit-marker-selected')) {\n\t\t\tL.DomUtil.removeClass(icon, 'leaflet-edit-marker-selected');\n\t\t\t// Offset as the border will make the icon move.\n\t\t\tthis._offsetMarker(icon, -4);\n\n\t\t} else {\n\t\t\tL.DomUtil.addClass(icon, 'leaflet-edit-marker-selected');\n\t\t\t// Offset as the border will make the icon move.\n\t\t\tthis._offsetMarker(icon, 4);\n\t\t}\n\n\t\ticon.style.display = '';\n\t},\n\n\t_offsetMarker: function (icon, offset) {\n\t\tvar iconMarginTop = parseInt(icon.style.marginTop, 10) - offset,\n\t\t\ticonMarginLeft = parseInt(icon.style.marginLeft, 10) - offset;\n\n\t\ticon.style.marginTop = iconMarginTop + 'px';\n\t\ticon.style.marginLeft = iconMarginLeft + 'px';\n\t}\n});\n\nL.Marker.addInitHook(function () {\n\tif (L.Edit.Marker) {\n\t\tthis.editing = new L.Edit.Marker(this);\n\n\t\tif (this.options.editable) {\n\t\t\tthis.editing.enable();\n\t\t}\n\t}\n});\n", "L.Edit = L.Edit || {};\n\n/**\n * @class L.Edit.Polyline\n * @aka L.Edit.Poly\n * @aka Edit.Poly\n */\nL.Edit.Poly = L.Handler.extend({\n\t// @method initialize(): void\n\tinitialize: function (poly) {\n\n\t\tthis.latlngs = [poly._latlngs];\n\t\tif (poly._holes) {\n\t\t\tthis.latlngs = this.latlngs.concat(poly._holes);\n\t\t}\n\n\t\tthis._poly = poly;\n\n\t\tthis._poly.on('revert-edited', this._updateLatLngs, this);\n\t},\n\n\t// Compatibility method to normalize Poly* objects\n\t// between 0.7.x and 1.0+\n\t_defaultShape: function () {\n\t\tif (!L.Polyline._flat) {\n\t\t\treturn this._poly._latlngs;\n\t\t}\n\t\treturn L.Polyline._flat(this._poly._latlngs) ? this._poly._latlngs : this._poly._latlngs[0];\n\t},\n\n\t_eachVertexHandler: function (callback) {\n\t\tfor (var i = 0; i < this._verticesHandlers.length; i++) {\n\t\t\tcallback(this._verticesHandlers[i]);\n\t\t}\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler\n\taddHooks: function () {\n\t\tthis._initHandlers();\n\t\tthis._eachVertexHandler(function (handler) {\n\t\t\thandler.addHooks();\n\t\t});\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler\n\tremoveHooks: function () {\n\t\tthis._eachVertexHandler(function (handler) {\n\t\t\thandler.removeHooks();\n\t\t});\n\t},\n\n\t// @method updateMarkers(): void\n\t// Fire an update for each vertex handler\n\tupdateMarkers: function () {\n\t\tthis._eachVertexHandler(function (handler) {\n\t\t\thandler.updateMarkers();\n\t\t});\n\t},\n\n\t_initHandlers: function () {\n\t\tthis._verticesHandlers = [];\n\t\tfor (var i = 0; i < this.latlngs.length; i++) {\n\t\t\tthis._verticesHandlers.push(new L.Edit.PolyVerticesEdit(this._poly, this.latlngs[i], this._poly.options.poly));\n\t\t}\n\t},\n\n\t_updateLatLngs: function (e) {\n\t\tthis.latlngs = [e.layer._latlngs];\n\t\tif (e.layer._holes) {\n\t\t\tthis.latlngs = this.latlngs.concat(e.layer._holes);\n\t\t}\n\t}\n\n});\n\n/**\n * @class L.Edit.PolyVerticesEdit\n * @aka Edit.PolyVerticesEdit\n */\nL.Edit.PolyVerticesEdit = L.Handler.extend({\n\toptions: {\n\t\ticon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(8, 8),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon'\n\t\t}),\n\t\ttouchIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(20, 20),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-touch-icon'\n\t\t}),\n\t\tdrawError: {\n\t\t\tcolor: '#b00b00',\n\t\t\ttimeout: 1000\n\t\t}\n\n\n\t},\n\n\t// @method intialize(): void\n\tinitialize: function (poly, latlngs, options) {\n\t\t// if touch, switch to touch icon\n\t\tif (L.Browser.touch) {\n\t\t\tthis.options.icon = this.options.touchIcon;\n\t\t}\n\t\tthis._poly = poly;\n\n\t\tif (options && options.drawError) {\n\t\t\toptions.drawError = L.Util.extend({}, this.options.drawError, options.drawError);\n\t\t}\n\n\t\tthis._latlngs = latlngs;\n\n\t\tL.setOptions(this, options);\n\t},\n\n\t// Compatibility method to normalize Poly* objects\n\t// between 0.7.x and 1.0+\n\t_defaultShape: function () {\n\t\tif (!L.Polyline._flat) {\n\t\t\treturn this._latlngs;\n\t\t}\n\t\treturn L.Polyline._flat(this._latlngs) ? this._latlngs : this._latlngs[0];\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler.\n\taddHooks: function () {\n\t\tvar poly = this._poly;\n\t\tvar path = poly._path;\n\n\t\tif (!(poly instanceof L.Polygon)) {\n\t\t\tpoly.options.fill = false;\n\t\t\tif (poly.options.editing) {\n\t\t\t\tpoly.options.editing.fill = false;\n\t\t\t}\n\t\t}\n\n\t\tif (path) {\n\t\t\tif (poly.options.editing && poly.options.editing.className) {\n\t\t\t\tif (poly.options.original.className) {\n\t\t\t\t\tpoly.options.original.className.split(' ').forEach(function (className) {\n\t\t\t\t\t\tL.DomUtil.removeClass(path, className);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tpoly.options.editing.className.split(' ').forEach(function (className) {\n\t\t\t\t\tL.DomUtil.addClass(path, className);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tpoly.setStyle(poly.options.editing);\n\n\t\tif (this._poly._map) {\n\n\t\t\tthis._map = this._poly._map; // Set map\n\n\t\t\tif (!this._markerGroup) {\n\t\t\t\tthis._initMarkers();\n\t\t\t}\n\t\t\tthis._poly._map.addLayer(this._markerGroup);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler.\n\tremoveHooks: function () {\n\t\tvar poly = this._poly;\n\t\tvar path = poly._path;\n\n\t\tif (path) {\n\t\t\tif (poly.options.editing && poly.options.editing.className) {\n\t\t\t\tpoly.options.editing.className.split(' ').forEach(function (className) {\n\t\t\t\t\tL.DomUtil.removeClass(path, className);\n\t\t\t\t});\n\t\t\t\tif (poly.options.original.className) {\n\t\t\t\t\tpoly.options.original.className.split(' ').forEach(function (className) {\n\t\t\t\t\t\tL.DomUtil.addClass(path, className);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tpoly.setStyle(poly.options.original);\n\n\t\tif (poly._map) {\n\t\t\tpoly._map.removeLayer(this._markerGroup);\n\t\t\tdelete this._markerGroup;\n\t\t\tdelete this._markers;\n\t\t}\n\t},\n\n\t// @method updateMarkers(): void\n\t// Clear markers and update their location\n\tupdateMarkers: function () {\n\t\tthis._markerGroup.clearLayers();\n\t\tthis._initMarkers();\n\t},\n\n\t_initMarkers: function () {\n\t\tif (!this._markerGroup) {\n\t\t\tthis._markerGroup = new L.LayerGroup();\n\t\t}\n\t\tthis._markers = [];\n\n\t\tvar latlngs = this._defaultShape(),\n\t\t\ti, j, len, marker;\n\n\t\tfor (i = 0, len = latlngs.length; i < len; i++) {\n\n\t\t\tmarker = this._createMarker(latlngs[i], i);\n\t\t\tmarker.on('click', this._onMarkerClick, this);\n\t\t\tmarker.on('contextmenu', this._onContextMenu, this);\n\t\t\tthis._markers.push(marker);\n\t\t}\n\n\t\tvar markerLeft, markerRight;\n\n\t\tfor (i = 0, j = len - 1; i < len; j = i++) {\n\t\t\tif (i === 0 && !(L.Polygon && (this._poly instanceof L.Polygon))) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tmarkerLeft = this._markers[j];\n\t\t\tmarkerRight = this._markers[i];\n\n\t\t\tthis._createMiddleMarker(markerLeft, markerRight);\n\t\t\tthis._updatePrevNext(markerLeft, markerRight);\n\t\t}\n\t},\n\n\t_createMarker: function (latlng, index) {\n\t\t// Extending L.Marker in TouchEvents.js to include touch.\n\t\tvar marker = new L.Marker.Touch(latlng, {\n\t\t\tdraggable: true,\n\t\t\ticon: this.options.icon,\n\t\t});\n\n\t\tmarker._origLatLng = latlng;\n\t\tmarker._index = index;\n\n\t\tmarker\n\t\t\t.on('dragstart', this._onMarkerDragStart, this)\n\t\t\t.on('drag', this._onMarkerDrag, this)\n\t\t\t.on('dragend', this._fireEdit, this)\n\t\t\t.on('touchmove', this._onTouchMove, this)\n\t\t\t.on('touchend', this._fireEdit, this)\n\t\t\t.on('MSPointerMove', this._onTouchMove, this)\n\t\t\t.on('MSPointerUp', this._fireEdit, this);\n\n\t\tthis._markerGroup.addLayer(marker);\n\n\t\treturn marker;\n\t},\n\n\t_onMarkerDragStart: function () {\n\t\tthis._poly.fire('editstart');\n\t},\n\n\t_spliceLatLngs: function () {\n\t\tvar latlngs = this._defaultShape();\n\t\tvar removed = [].splice.apply(latlngs, arguments);\n\t\tthis._poly._convertLatLngs(latlngs, true);\n\t\tthis._poly.redraw();\n\t\treturn removed;\n\t},\n\n\t_removeMarker: function (marker) {\n\t\tvar i = marker._index;\n\n\t\tthis._markerGroup.removeLayer(marker);\n\t\tthis._markers.splice(i, 1);\n\t\tthis._spliceLatLngs(i, 1);\n\t\tthis._updateIndexes(i, -1);\n\n\t\tmarker\n\t\t\t.off('dragstart', this._onMarkerDragStart, this)\n\t\t\t.off('drag', this._onMarkerDrag, this)\n\t\t\t.off('dragend', this._fireEdit, this)\n\t\t\t.off('touchmove', this._onMarkerDrag, this)\n\t\t\t.off('touchend', this._fireEdit, this)\n\t\t\t.off('click', this._onMarkerClick, this)\n\t\t\t.off('MSPointerMove', this._onTouchMove, this)\n\t\t\t.off('MSPointerUp', this._fireEdit, this);\n\t},\n\n\t_fireEdit: function () {\n\t\tthis._poly.edited = true;\n\t\tthis._poly.fire('edit');\n\t\tthis._poly._map.fire(L.Draw.Event.EDITVERTEX, {layers: this._markerGroup, poly: this._poly});\n\t},\n\n\t_onMarkerDrag: function (e) {\n\t\tvar marker = e.target;\n\t\tvar poly = this._poly;\n\n\t\tvar oldOrigLatLng = L.LatLngUtil.cloneLatLng(marker._origLatLng);\n\t\tL.extend(marker._origLatLng, marker._latlng);\n\t\tif (poly.options.poly) {\n\t\t\tvar tooltip = poly._map._editTooltip; // Access the tooltip\n\n\t\t\t// If we don't allow intersections and the polygon intersects\n\t\t\tif (!poly.options.poly.allowIntersection && poly.intersects()) {\n\t\t\t\tL.extend(marker._origLatLng, oldOrigLatLng);\n\t\t\t\tmarker.setLatLng(oldOrigLatLng);\n\t\t\t\tvar originalColor = poly.options.color;\n\t\t\t\tpoly.setStyle({color: this.options.drawError.color});\n\t\t\t\tif (tooltip) {\n\t\t\t\t\ttooltip.updateContent({\n\t\t\t\t\t\ttext: L.drawLocal.draw.handlers.polyline.error\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// Reset everything back to normal after a second\n\t\t\t\tsetTimeout(function () {\n\t\t\t\t\tpoly.setStyle({color: originalColor});\n\t\t\t\t\tif (tooltip) {\n\t\t\t\t\t\ttooltip.updateContent({\n\t\t\t\t\t\t\ttext: L.drawLocal.edit.handlers.edit.tooltip.text,\n\t\t\t\t\t\t\tsubtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}, 1000);\n\t\t\t}\n\t\t}\n\n\t\tif (marker._middleLeft) {\n\t\t\tmarker._middleLeft.setLatLng(this._getMiddleLatLng(marker._prev, marker));\n\t\t}\n\t\tif (marker._middleRight) {\n\t\t\tmarker._middleRight.setLatLng(this._getMiddleLatLng(marker, marker._next));\n\t\t}\n\n\t\t//refresh the bounds when draging\n\t\tthis._poly._bounds._southWest = L.latLng(Infinity, Infinity);\n\t\tthis._poly._bounds._northEast = L.latLng(-Infinity, -Infinity);\n\t\tvar latlngs = this._poly.getLatLngs();\n\t\tthis._poly._convertLatLngs(latlngs, true);\n\t\tthis._poly.redraw();\n\t\tthis._poly.fire('editdrag');\n\t},\n\n\t_onMarkerClick: function (e) {\n\n\t\tvar minPoints = L.Polygon && (this._poly instanceof L.Polygon) ? 4 : 3,\n\t\t\tmarker = e.target;\n\n\t\t// If removing this point would create an invalid polyline/polygon don't remove\n\t\tif (this._defaultShape().length < minPoints) {\n\t\t\treturn;\n\t\t}\n\n\t\t// remove the marker\n\t\tthis._removeMarker(marker);\n\n\t\t// update prev/next links of adjacent markers\n\t\tthis._updatePrevNext(marker._prev, marker._next);\n\n\t\t// remove ghost markers near the removed marker\n\t\tif (marker._middleLeft) {\n\t\t\tthis._markerGroup.removeLayer(marker._middleLeft);\n\t\t}\n\t\tif (marker._middleRight) {\n\t\t\tthis._markerGroup.removeLayer(marker._middleRight);\n\t\t}\n\n\t\t// create a ghost marker in place of the removed one\n\t\tif (marker._prev && marker._next) {\n\t\t\tthis._createMiddleMarker(marker._prev, marker._next);\n\n\t\t} else if (!marker._prev) {\n\t\t\tmarker._next._middleLeft = null;\n\n\t\t} else if (!marker._next) {\n\t\t\tmarker._prev._middleRight = null;\n\t\t}\n\n\t\tthis._fireEdit();\n\t},\n\n\t_onContextMenu: function (e) {\n\t\tvar marker = e.target;\n\t\tvar poly = this._poly;\n\t\tthis._poly._map.fire(L.Draw.Event.MARKERCONTEXT, {marker: marker, layers: this._markerGroup, poly: this._poly});\n\t\tL.DomEvent.stopPropagation;\n\t},\n\n\t_onTouchMove: function (e) {\n\n\t\tvar layerPoint = this._map.mouseEventToLayerPoint(e.originalEvent.touches[0]),\n\t\t\tlatlng = this._map.layerPointToLatLng(layerPoint),\n\t\t\tmarker = e.target;\n\n\t\tL.extend(marker._origLatLng, latlng);\n\n\t\tif (marker._middleLeft) {\n\t\t\tmarker._middleLeft.setLatLng(this._getMiddleLatLng(marker._prev, marker));\n\t\t}\n\t\tif (marker._middleRight) {\n\t\t\tmarker._middleRight.setLatLng(this._getMiddleLatLng(marker, marker._next));\n\t\t}\n\n\t\tthis._poly.redraw();\n\t\tthis.updateMarkers();\n\t},\n\n\t_updateIndexes: function (index, delta) {\n\t\tthis._markerGroup.eachLayer(function (marker) {\n\t\t\tif (marker._index > index) {\n\t\t\t\tmarker._index += delta;\n\t\t\t}\n\t\t});\n\t},\n\n\t_createMiddleMarker: function (marker1, marker2) {\n\t\tvar latlng = this._getMiddleLatLng(marker1, marker2),\n\t\t\tmarker = this._createMarker(latlng),\n\t\t\tonClick,\n\t\t\tonDragStart,\n\t\t\tonDragEnd;\n\n\t\tmarker.setOpacity(0.6);\n\n\t\tmarker1._middleRight = marker2._middleLeft = marker;\n\n\t\tonDragStart = function () {\n\t\t\tmarker.off('touchmove', onDragStart, this);\n\t\t\tvar i = marker2._index;\n\n\t\t\tmarker._index = i;\n\n\t\t\tmarker\n\t\t\t\t.off('click', onClick, this)\n\t\t\t\t.on('click', this._onMarkerClick, this);\n\n\t\t\tlatlng.lat = marker.getLatLng().lat;\n\t\t\tlatlng.lng = marker.getLatLng().lng;\n\t\t\tthis._spliceLatLngs(i, 0, latlng);\n\t\t\tthis._markers.splice(i, 0, marker);\n\n\t\t\tmarker.setOpacity(1);\n\n\t\t\tthis._updateIndexes(i, 1);\n\t\t\tmarker2._index++;\n\t\t\tthis._updatePrevNext(marker1, marker);\n\t\t\tthis._updatePrevNext(marker, marker2);\n\n\t\t\tthis._poly.fire('editstart');\n\t\t};\n\n\t\tonDragEnd = function () {\n\t\t\tmarker.off('dragstart', onDragStart, this);\n\t\t\tmarker.off('dragend', onDragEnd, this);\n\t\t\tmarker.off('touchmove', onDragStart, this);\n\n\t\t\tthis._createMiddleMarker(marker1, marker);\n\t\t\tthis._createMiddleMarker(marker, marker2);\n\t\t};\n\n\t\tonClick = function () {\n\t\t\tonDragStart.call(this);\n\t\t\tonDragEnd.call(this);\n\t\t\tthis._fireEdit();\n\t\t};\n\n\t\tmarker\n\t\t\t.on('click', onClick, this)\n\t\t\t.on('dragstart', onDragStart, this)\n\t\t\t.on('dragend', onDragEnd, this)\n\t\t\t.on('touchmove', onDragStart, this);\n\n\t\tthis._markerGroup.addLayer(marker);\n\t},\n\n\t_updatePrevNext: function (marker1, marker2) {\n\t\tif (marker1) {\n\t\t\tmarker1._next = marker2;\n\t\t}\n\t\tif (marker2) {\n\t\t\tmarker2._prev = marker1;\n\t\t}\n\t},\n\n\t_getMiddleLatLng: function (marker1, marker2) {\n\t\tvar map = this._poly._map,\n\t\t\tp1 = map.project(marker1.getLatLng()),\n\t\t\tp2 = map.project(marker2.getLatLng());\n\n\t\treturn map.unproject(p1._add(p2)._divideBy(2));\n\t}\n});\n\nL.Polyline.addInitHook(function () {\n\n\t// Check to see if handler has already been initialized. This is to support versions of Leaflet that still have L.Handler.PolyEdit\n\tif (this.editing) {\n\t\treturn;\n\t}\n\n\tif (L.Edit.Poly) {\n\n\t\tthis.editing = new L.Edit.Poly(this);\n\n\t\tif (this.options.editable) {\n\t\t\tthis.editing.enable();\n\t\t}\n\t}\n\n\tthis.on('add', function () {\n\t\tif (this.editing && this.editing.enabled()) {\n\t\t\tthis.editing.addHooks();\n\t\t}\n\t});\n\n\tthis.on('remove', function () {\n\t\tif (this.editing && this.editing.enabled()) {\n\t\t\tthis.editing.removeHooks();\n\t\t}\n\t});\n});\n", "L.Edit = L.Edit || {};\n/**\n * @class L.Edit.SimpleShape\n * @aka Edit.SimpleShape\n */\nL.Edit.SimpleShape = L.Handler.extend({\n\toptions: {\n\t\tmoveIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(8, 8),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-edit-move'\n\t\t}),\n\t\tresizeIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(8, 8),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-edit-resize'\n\t\t}),\n\t\ttouchMoveIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(20, 20),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-edit-move leaflet-touch-icon'\n\t\t}),\n\t\ttouchResizeIcon: new L.DivIcon({\n\t\t\ticonSize: new L.Point(20, 20),\n\t\t\tclassName: 'leaflet-div-icon leaflet-editing-icon leaflet-edit-resize leaflet-touch-icon'\n\t\t}),\n\t},\n\n\t// @method intialize(): void\n\tinitialize: function (shape, options) {\n\t\t// if touch, switch to touch icon\n\t\tif (L.Browser.touch) {\n\t\t\tthis.options.moveIcon = this.options.touchMoveIcon;\n\t\t\tthis.options.resizeIcon = this.options.touchResizeIcon;\n\t\t}\n\n\t\tthis._shape = shape;\n\t\tL.Util.setOptions(this, options);\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler\n\taddHooks: function () {\n\t\tvar shape = this._shape;\n\t\tif (this._shape._map) {\n\t\t\tthis._map = this._shape._map;\n\t\t\tshape.setStyle(shape.options.editing);\n\n\t\t\tif (shape._map) {\n\t\t\t\tthis._map = shape._map;\n\t\t\t\tif (!this._markerGroup) {\n\t\t\t\t\tthis._initMarkers();\n\t\t\t\t}\n\t\t\t\tthis._map.addLayer(this._markerGroup);\n\t\t\t}\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler\n\tremoveHooks: function () {\n\t\tvar shape = this._shape;\n\n\t\tshape.setStyle(shape.options.original);\n\n\t\tif (shape._map) {\n\t\t\tthis._unbindMarker(this._moveMarker);\n\n\t\t\tfor (var i = 0, l = this._resizeMarkers.length; i < l; i++) {\n\t\t\t\tthis._unbindMarker(this._resizeMarkers[i]);\n\t\t\t}\n\t\t\tthis._resizeMarkers = null;\n\n\t\t\tthis._map.removeLayer(this._markerGroup);\n\t\t\tdelete this._markerGroup;\n\t\t}\n\n\t\tthis._map = null;\n\t},\n\n\t// @method updateMarkers(): void\n\t// Remove the edit markers from this layer\n\tupdateMarkers: function () {\n\t\tthis._markerGroup.clearLayers();\n\t\tthis._initMarkers();\n\t},\n\n\t_initMarkers: function () {\n\t\tif (!this._markerGroup) {\n\t\t\tthis._markerGroup = new L.LayerGroup();\n\t\t}\n\n\t\t// Create center marker\n\t\tthis._createMoveMarker();\n\n\t\t// Create edge marker\n\t\tthis._createResizeMarker();\n\t},\n\n\t_createMoveMarker: function () {\n\t\t// Children override\n\t},\n\n\t_createResizeMarker: function () {\n\t\t// Children override\n\t},\n\n\t_createMarker: function (latlng, icon) {\n\t\t// Extending L.Marker in TouchEvents.js to include touch.\n\t\tvar marker = new L.Marker.Touch(latlng, {\n\t\t\tdraggable: true,\n\t\t\ticon: icon,\n\t\t\tzIndexOffset: 10\n\t\t});\n\n\t\tthis._bindMarker(marker);\n\n\t\tthis._markerGroup.addLayer(marker);\n\n\t\treturn marker;\n\t},\n\n\t_bindMarker: function (marker) {\n\t\tmarker\n\t\t\t.on('dragstart', this._onMarkerDragStart, this)\n\t\t\t.on('drag', this._onMarkerDrag, this)\n\t\t\t.on('dragend', this._onMarkerDragEnd, this)\n\t\t\t.on('touchstart', this._onTouchStart, this)\n\t\t\t.on('touchmove', this._onTouchMove, this)\n\t\t\t.on('MSPointerMove', this._onTouchMove, this)\n\t\t\t.on('touchend', this._onTouchEnd, this)\n\t\t\t.on('MSPointerUp', this._onTouchEnd, this);\n\t},\n\n\t_unbindMarker: function (marker) {\n\t\tmarker\n\t\t\t.off('dragstart', this._onMarkerDragStart, this)\n\t\t\t.off('drag', this._onMarkerDrag, this)\n\t\t\t.off('dragend', this._onMarkerDragEnd, this)\n\t\t\t.off('touchstart', this._onTouchStart, this)\n\t\t\t.off('touchmove', this._onTouchMove, this)\n\t\t\t.off('MSPointerMove', this._onTouchMove, this)\n\t\t\t.off('touchend', this._onTouchEnd, this)\n\t\t\t.off('MSPointerUp', this._onTouchEnd, this);\n\t},\n\n\t_onMarkerDragStart: function (e) {\n\t\tvar marker = e.target;\n\t\tmarker.setOpacity(0);\n\n\t\tthis._shape.fire('editstart');\n\t},\n\n\t_fireEdit: function () {\n\t\tthis._shape.edited = true;\n\t\tthis._shape.fire('edit');\n\t},\n\n\t_onMarkerDrag: function (e) {\n\t\tvar marker = e.target,\n\t\t\tlatlng = marker.getLatLng();\n\n\t\tif (marker === this._moveMarker) {\n\t\t\tthis._move(latlng);\n\t\t} else {\n\t\t\tthis._resize(latlng);\n\t\t}\n\n\t\tthis._shape.redraw();\n\t\tthis._shape.fire('editdrag');\n\t},\n\n\t_onMarkerDragEnd: function (e) {\n\t\tvar marker = e.target;\n\t\tmarker.setOpacity(1);\n\n\t\tthis._fireEdit();\n\t},\n\n\t_onTouchStart: function (e) {\n\t\tL.Edit.SimpleShape.prototype._onMarkerDragStart.call(this, e);\n\n\t\tif (typeof(this._getCorners) === 'function') {\n\t\t\t// Save a reference to the opposite point\n\t\t\tvar corners = this._getCorners(),\n\t\t\t\tmarker = e.target,\n\t\t\t\tcurrentCornerIndex = marker._cornerIndex;\n\n\t\t\tmarker.setOpacity(0);\n\n\t\t\t// Copyed from Edit.Rectangle.js line 23 _onMarkerDragStart()\n\t\t\t// Latlng is null otherwise.\n\t\t\tthis._oppositeCorner = corners[(currentCornerIndex + 2) % 4];\n\t\t\tthis._toggleCornerMarkers(0, currentCornerIndex);\n\t\t}\n\n\t\tthis._shape.fire('editstart');\n\t},\n\n\t_onTouchMove: function (e) {\n\t\tvar layerPoint = this._map.mouseEventToLayerPoint(e.originalEvent.touches[0]),\n\t\t\tlatlng = this._map.layerPointToLatLng(layerPoint),\n\t\t\tmarker = e.target;\n\n\t\tif (marker === this._moveMarker) {\n\t\t\tthis._move(latlng);\n\t\t} else {\n\t\t\tthis._resize(latlng);\n\t\t}\n\n\t\tthis._shape.redraw();\n\n\t\t// prevent touchcancel in IOS\n\t\t// e.preventDefault();\n\t\treturn false;\n\t},\n\n\t_onTouchEnd: function (e) {\n\t\tvar marker = e.target;\n\t\tmarker.setOpacity(1);\n\t\tthis.updateMarkers();\n\t\tthis._fireEdit();\n\t},\n\n\t_move: function () {\n\t\t// Children override\n\t},\n\n\t_resize: function () {\n\t\t// Children override\n\t}\n});\n", "L.Edit = L.Edit || {};\n/**\n * @class L.Edit.Rectangle\n * @aka Edit.Rectangle\n * @inherits L.Edit.SimpleShape\n */\nL.Edit.Rectangle = L.Edit.SimpleShape.extend({\n\t_createMoveMarker: function () {\n\t\tvar bounds = this._shape.getBounds(),\n\t\t\tcenter = bounds.getCenter();\n\n\t\tthis._moveMarker = this._createMarker(center, this.options.moveIcon);\n\t},\n\n\t_createResizeMarker: function () {\n\t\tvar corners = this._getCorners();\n\n\t\tthis._resizeMarkers = [];\n\n\t\tfor (var i = 0, l = corners.length; i < l; i++) {\n\t\t\tthis._resizeMarkers.push(this._createMarker(corners[i], this.options.resizeIcon));\n\t\t\t// Monkey in the corner index as we will need to know this for dragging\n\t\t\tthis._resizeMarkers[i]._cornerIndex = i;\n\t\t}\n\t},\n\n\t_onMarkerDragStart: function (e) {\n\t\tL.Edit.SimpleShape.prototype._onMarkerDragStart.call(this, e);\n\n\t\t// Save a reference to the opposite point\n\t\tvar corners = this._getCorners(),\n\t\t\tmarker = e.target,\n\t\t\tcurrentCornerIndex = marker._cornerIndex;\n\n\t\tthis._oppositeCorner = corners[(currentCornerIndex + 2) % 4];\n\n\t\tthis._toggleCornerMarkers(0, currentCornerIndex);\n\t},\n\n\t_onMarkerDragEnd: function (e) {\n\t\tvar marker = e.target,\n\t\t\tbounds, center;\n\n\t\t// Reset move marker position to the center\n\t\tif (marker === this._moveMarker) {\n\t\t\tbounds = this._shape.getBounds();\n\t\t\tcenter = bounds.getCenter();\n\n\t\t\tmarker.setLatLng(center);\n\t\t}\n\n\t\tthis._toggleCornerMarkers(1);\n\n\t\tthis._repositionCornerMarkers();\n\n\t\tL.Edit.SimpleShape.prototype._onMarkerDragEnd.call(this, e);\n\t},\n\n\t_move: function (newCenter) {\n\t\tvar latlngs = this._shape._defaultShape ? this._shape._defaultShape() : this._shape.getLatLngs(),\n\t\t\tbounds = this._shape.getBounds(),\n\t\t\tcenter = bounds.getCenter(),\n\t\t\toffset, newLatLngs = [];\n\n\t\t// Offset the latlngs to the new center\n\t\tfor (var i = 0, l = latlngs.length; i < l; i++) {\n\t\t\toffset = [latlngs[i].lat - center.lat, latlngs[i].lng - center.lng];\n\t\t\tnewLatLngs.push([newCenter.lat + offset[0], newCenter.lng + offset[1]]);\n\t\t}\n\n\t\tthis._shape.setLatLngs(newLatLngs);\n\n\t\t// Reposition the resize markers\n\t\tthis._repositionCornerMarkers();\n\n\t\tthis._map.fire(L.Draw.Event.EDITMOVE, {layer: this._shape});\n\t},\n\n\t_resize: function (latlng) {\n\t\tvar bounds;\n\n\t\t// Update the shape based on the current position of this corner and the opposite point\n\t\tthis._shape.setBounds(L.latLngBounds(latlng, this._oppositeCorner));\n\n\t\t// Reposition the move marker\n\t\tbounds = this._shape.getBounds();\n\t\tthis._moveMarker.setLatLng(bounds.getCenter());\n\n\t\tthis._map.fire(L.Draw.Event.EDITRESIZE, {layer: this._shape});\n\t},\n\n\t_getCorners: function () {\n\t\tvar bounds = this._shape.getBounds(),\n\t\t\tnw = bounds.getNorthWest(),\n\t\t\tne = bounds.getNorthEast(),\n\t\t\tse = bounds.getSouthEast(),\n\t\t\tsw = bounds.getSouthWest();\n\n\t\treturn [nw, ne, se, sw];\n\t},\n\n\t_toggleCornerMarkers: function (opacity) {\n\t\tfor (var i = 0, l = this._resizeMarkers.length; i < l; i++) {\n\t\t\tthis._resizeMarkers[i].setOpacity(opacity);\n\t\t}\n\t},\n\n\t_repositionCornerMarkers: function () {\n\t\tvar corners = this._getCorners();\n\n\t\tfor (var i = 0, l = this._resizeMarkers.length; i < l; i++) {\n\t\t\tthis._resizeMarkers[i].setLatLng(corners[i]);\n\t\t}\n\t}\n});\n\nL.Rectangle.addInitHook(function () {\n\tif (L.Edit.Rectangle) {\n\t\tthis.editing = new L.Edit.Rectangle(this);\n\n\t\tif (this.options.editable) {\n\t\t\tthis.editing.enable();\n\t\t}\n\t}\n});\n", "L.Edit = L.Edit || {};\n/**\n * @class L.Edit.CircleMarker\n * @aka Edit.Circle\n * @inherits L.Edit.SimpleShape\n */\nL.Edit.CircleMarker = L.Edit.SimpleShape.extend({\n\t_createMoveMarker: function () {\n\t\tvar center = this._shape.getLatLng();\n\n\t\tthis._moveMarker = this._createMarker(center, this.options.moveIcon);\n\t},\n\n\t_createResizeMarker: function () {\n\t\t// To avoid an undefined check in L.Edit.SimpleShape.removeHooks\n\t\tthis._resizeMarkers = [];\n\t},\n\n\t_move: function (latlng) {\n\t\tif (this._resizeMarkers.length) {\n\t\t\tvar resizemarkerPoint = this._getResizeMarkerPoint(latlng);\n\t\t\t// Move the resize marker\n\t\t\tthis._resizeMarkers[0].setLatLng(resizemarkerPoint);\n\t\t}\n\n\t\t// Move the circle\n\t\tthis._shape.setLatLng(latlng);\n\n\t\tthis._map.fire(L.Draw.Event.EDITMOVE, {layer: this._shape});\n\t},\n});\n\nL.CircleMarker.addInitHook(function () {\n\tif (L.Edit.CircleMarker) {\n\t\tthis.editing = new L.Edit.CircleMarker(this);\n\n\t\tif (this.options.editable) {\n\t\t\tthis.editing.enable();\n\t\t}\n\t}\n\n\tthis.on('add', function () {\n\t\tif (this.editing && this.editing.enabled()) {\n\t\t\tthis.editing.addHooks();\n\t\t}\n\t});\n\n\tthis.on('remove', function () {\n\t\tif (this.editing && this.editing.enabled()) {\n\t\t\tthis.editing.removeHooks();\n\t\t}\n\t});\n});\n", "L.Edit = L.Edit || {};\n/**\n * @class L.Edit.Circle\n * @aka Edit.Circle\n * @inherits L.Edit.CircleMarker\n */\nL.Edit.Circle = L.Edit.CircleMarker.extend({\n\n\t_createResizeMarker: function () {\n\t\tvar center = this._shape.getLatLng(),\n\t\t\tresizemarkerPoint = this._getResizeMarkerPoint(center);\n\n\t\tthis._resizeMarkers = [];\n\t\tthis._resizeMarkers.push(this._createMarker(resizemarkerPoint, this.options.resizeIcon));\n\t},\n\n\t_getResizeMarkerPoint: function (latlng) {\n\t\t// From L.shape.getBounds()\n\t\tvar delta = this._shape._radius * Math.cos(Math.PI / 4),\n\t\t\tpoint = this._map.project(latlng);\n\t\treturn this._map.unproject([point.x + delta, point.y - delta]);\n\t},\n\n\t_resize: function (latlng) {\n\t\tvar moveLatLng = this._moveMarker.getLatLng();\n\n\t\t// Calculate the radius based on the version\n\t\tif (L.GeometryUtil.isVersion07x()) {\n\t\t\tradius = moveLatLng.distanceTo(latlng);\n\t\t} else {\n\t\t\tradius = this._map.distance(moveLatLng, latlng);\n\t\t}\n\t\tthis._shape.setRadius(radius);\n\n\t\tif (this._map.editTooltip) {\n\t\t\tthis._map._editTooltip.updateContent({\n\t\t\t\ttext: L.drawLocal.edit.handlers.edit.tooltip.subtext + '<br />' + L.drawLocal.edit.handlers.edit.tooltip.text,\n\t\t\t\tsubtext: L.drawLocal.draw.handlers.circle.radius + ': ' +\n\t\t\t\tL.GeometryUtil.readableDistance(radius, true, this.options.feet, this.options.nautic)\n\t\t\t});\n\t\t}\n\n\t\tthis._shape.setRadius(radius);\n\n\t\tthis._map.fire(L.Draw.Event.EDITRESIZE, {layer: this._shape});\n\t}\n});\n\nL.Circle.addInitHook(function () {\n\tif (L.Edit.Circle) {\n\t\tthis.editing = new L.Edit.Circle(this);\n\n\t\tif (this.options.editable) {\n\t\t\tthis.editing.enable();\n\t\t}\n\t}\n});\n", "L.Map.mergeOptions({\n\ttouchExtend: true\n});\n\n/**\n * @class L.Map.TouchExtend\n * @aka TouchExtend\n */\nL.Map.TouchExtend = L.Handler.extend({\n\n\t// @method initialize(): void\n\t// Sets TouchExtend private accessor variables\n\tinitialize: function (map) {\n\t\tthis._map = map;\n\t\tthis._container = map._container;\n\t\tthis._pane = map._panes.overlayPane;\n\t},\n\n\t// @method addHooks(): void\n\t// Adds dom listener events to the map container\n\taddHooks: function () {\n\t\tL.DomEvent.on(this._container, 'touchstart', this._onTouchStart, this);\n\t\tL.DomEvent.on(this._container, 'touchend', this._onTouchEnd, this);\n\t\tL.DomEvent.on(this._container, 'touchmove', this._onTouchMove, this);\n\t\tif (this._detectIE()) {\n\t\t\tL.DomEvent.on(this._container, 'MSPointerDown', this._onTouchStart, this);\n\t\t\tL.<PERSON>.on(this._container, 'MSPointerUp', this._onTouchEnd, this);\n\t\t\tL.DomEvent.on(this._container, 'MSPointerMove', this._onTouchMove, this);\n\t\t\tL.<PERSON>.on(this._container, 'MSPointerCancel', this._onTouchCancel, this);\n\n\t\t} else {\n\t\t\tL.DomEvent.on(this._container, 'touchcancel', this._onTouchCancel, this);\n\t\t\tL.DomEvent.on(this._container, 'touchleave', this._onTouchLeave, this);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Removes dom listener events from the map container\n\tremoveHooks: function () {\n\t\tL.DomEvent.off(this._container, 'touchstart', this._onTouchStart, this);\n\t\tL.DomEvent.off(this._container, 'touchend', this._onTouchEnd, this);\n\t\tL.DomEvent.off(this._container, 'touchmove', this._onTouchMove, this);\n\t\tif (this._detectIE()) {\n\t\t\tL.DomEvent.off(this._container, 'MSPointerDown', this._onTouchStart, this);\n\t\t\tL.DomEvent.off(this._container, 'MSPointerUp', this._onTouchEnd, this);\n\t\t\tL.DomEvent.off(this._container, 'MSPointerMove', this._onTouchMove, this);\n\t\t\tL.DomEvent.off(this._container, 'MSPointerCancel', this._onTouchCancel, this);\n\t\t} else {\n\t\t\tL.DomEvent.off(this._container, 'touchcancel', this._onTouchCancel, this);\n\t\t\tL.DomEvent.off(this._container, 'touchleave', this._onTouchLeave, this);\n\t\t}\n\t},\n\n\t_touchEvent: function (e, type) {\n\t\t// #TODO: fix the pageX error that is do a bug in Android where a single touch triggers two click events\n\t\t// _filterClick is what leaflet uses as a workaround.\n\t\t// This is a problem with more things than just android. Another problem is touchEnd has no touches in\n\t\t// its touch list.\n\t\tvar touchEvent = {};\n\t\tif (typeof e.touches !== 'undefined') {\n\t\t\tif (!e.touches.length) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\ttouchEvent = e.touches[0];\n\t\t} else if (e.pointerType === 'touch') {\n\t\t\ttouchEvent = e;\n\t\t\tif (!this._filterClick(e)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else {\n\t\t\treturn;\n\t\t}\n\n\t\tvar containerPoint = this._map.mouseEventToContainerPoint(touchEvent),\n\t\t\tlayerPoint = this._map.mouseEventToLayerPoint(touchEvent),\n\t\t\tlatlng = this._map.layerPointToLatLng(layerPoint);\n\n\t\tthis._map.fire(type, {\n\t\t\tlatlng: latlng,\n\t\t\tlayerPoint: layerPoint,\n\t\t\tcontainerPoint: containerPoint,\n\t\t\tpageX: touchEvent.pageX,\n\t\t\tpageY: touchEvent.pageY,\n\t\t\toriginalEvent: e\n\t\t});\n\t},\n\n\t/** Borrowed from Leaflet and modified for bool ops **/\n\t_filterClick: function (e) {\n\t\tvar timeStamp = (e.timeStamp || e.originalEvent.timeStamp),\n\t\t\telapsed = L.DomEvent._lastClick && (timeStamp - L.DomEvent._lastClick);\n\n\t\t// are they closer together than 500ms yet more than 100ms?\n\t\t// Android typically triggers them ~300ms apart while multiple listeners\n\t\t// on the same event should be triggered far faster;\n\t\t// or check if click is simulated on the element, and if it is, reject any non-simulated events\n\t\tif ((elapsed && elapsed > 100 && elapsed < 500) || (e.target._simulatedClick && !e._simulated)) {\n\t\t\tL.DomEvent.stop(e);\n\t\t\treturn false;\n\t\t}\n\t\tL.DomEvent._lastClick = timeStamp;\n\t\treturn true;\n\t},\n\n\t_onTouchStart: function (e) {\n\t\tif (!this._map._loaded) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar type = 'touchstart';\n\t\tthis._touchEvent(e, type);\n\n\t},\n\n\t_onTouchEnd: function (e) {\n\t\tif (!this._map._loaded) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar type = 'touchend';\n\t\tthis._touchEvent(e, type);\n\t},\n\n\t_onTouchCancel: function (e) {\n\t\tif (!this._map._loaded) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar type = 'touchcancel';\n\t\tif (this._detectIE()) {\n\t\t\ttype = 'pointercancel';\n\t\t}\n\t\tthis._touchEvent(e, type);\n\t},\n\n\t_onTouchLeave: function (e) {\n\t\tif (!this._map._loaded) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar type = 'touchleave';\n\t\tthis._touchEvent(e, type);\n\t},\n\n\t_onTouchMove: function (e) {\n\t\tif (!this._map._loaded) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar type = 'touchmove';\n\t\tthis._touchEvent(e, type);\n\t},\n\n\t_detectIE: function () {\n\t\tvar ua = window.navigator.userAgent;\n\n\t\tvar msie = ua.indexOf('MSIE ');\n\t\tif (msie > 0) {\n\t\t\t// IE 10 or older => return version number\n\t\t\treturn parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n\t\t}\n\n\t\tvar trident = ua.indexOf('Trident/');\n\t\tif (trident > 0) {\n\t\t\t// IE 11 => return version number\n\t\t\tvar rv = ua.indexOf('rv:');\n\t\t\treturn parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);\n\t\t}\n\n\t\tvar edge = ua.indexOf('Edge/');\n\t\tif (edge > 0) {\n\t\t\t// IE 12 => return version number\n\t\t\treturn parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);\n\t\t}\n\n\t\t// other browser\n\t\treturn false;\n\t}\n});\n\nL.Map.addInitHook('addHandler', 'touchExtend', L.Map.TouchExtend);\n\n\n/**\n * @class L.Marker.Touch\n * @aka Marker.Touch\n *\n * This isn't full Touch support. This is just to get markers to also support dom touch events after creation\n * #TODO: find a better way of getting markers to support touch.\n */\nL.Marker.Touch = L.Marker.extend({\n\n\t_initInteraction: function () {\n\t\tif (!this.addInteractiveTarget) {\n\t\t\t// 0.7.x support\n\t\t\treturn this._initInteractionLegacy();\n\t\t}\n\t\t// TODO this may need be updated to re-add touch events for 1.0+\n\t\treturn L.Marker.prototype._initInteraction.apply(this);\n\t},\n\n\t// This is an exact copy of https://github.com/Leaflet/Leaflet/blob/v0.7/src/layer/marker/Marker.js\n\t// with the addition of the touch events\n\t_initInteractionLegacy: function () {\n\n\t\tif (!this.options.clickable) {\n\t\t\treturn;\n\t\t}\n\n\t\t// TODO refactor into something shared with Map/Path/etc. to DRY it up\n\n\t\tvar icon = this._icon,\n\t\t\tevents = ['dblclick',\n\t\t\t\t'mousedown',\n\t\t\t\t'mouseover',\n\t\t\t\t'mouseout',\n\t\t\t\t'contextmenu',\n\t\t\t\t'touchstart',\n\t\t\t\t'touchend',\n\t\t\t\t'touchmove'];\n\t\tif (this._detectIE) {\n\t\t\tevents.concat(['MSPointerDown',\n\t\t\t\t'MSPointerUp',\n\t\t\t\t'MSPointerMove',\n\t\t\t\t'MSPointerCancel']);\n\t\t} else {\n\t\t\tevents.concat(['touchcancel']);\n\t\t}\n\n\t\tL.DomUtil.addClass(icon, 'leaflet-clickable');\n\t\tL.DomEvent.on(icon, 'click', this._onMouseClick, this);\n\t\tL.DomEvent.on(icon, 'keypress', this._onKeyPress, this);\n\n\t\tfor (var i = 0; i < events.length; i++) {\n\t\t\tL.DomEvent.on(icon, events[i], this._fireMouseEvent, this);\n\t\t}\n\n\t\tif (L.Handler.MarkerDrag) {\n\t\t\tthis.dragging = new L.Handler.MarkerDrag(this);\n\n\t\t\tif (this.options.draggable) {\n\t\t\t\tthis.dragging.enable();\n\t\t\t}\n\t\t}\n\t},\n\n\t_detectIE: function () {\n\t\tvar ua = window.navigator.userAgent;\n\n\t\tvar msie = ua.indexOf('MSIE ');\n\t\tif (msie > 0) {\n\t\t\t// IE 10 or older => return version number\n\t\t\treturn parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);\n\t\t}\n\n\t\tvar trident = ua.indexOf('Trident/');\n\t\tif (trident > 0) {\n\t\t\t// IE 11 => return version number\n\t\t\tvar rv = ua.indexOf('rv:');\n\t\t\treturn parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);\n\t\t}\n\n\t\tvar edge = ua.indexOf('Edge/');\n\t\tif (edge > 0) {\n\t\t\t// IE 12 => return version number\n\t\t\treturn parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);\n\t\t}\n\n\t\t// other browser\n\t\treturn false;\n\t}\n});\n", "/**\n * @class L.LatLngUtil\n * @aka LatLngUtil\n */\nL.LatLngUtil = {\n\t// Clones a LatLngs[], returns [][]\n\n\t// @method cloneLatLngs(LatLngs[]): L.LatLngs[]\n\t// Clone the latLng point or points or nested points and return an array with those points\n\tcloneLatLngs: function (latlngs) {\n\t\tvar clone = [];\n\t\tfor (var i = 0, l = latlngs.length; i < l; i++) {\n\t\t\t// Check for nested array (Polyline/Polygon)\n\t\t\tif (Array.isArray(latlngs[i])) {\n\t\t\t\tclone.push(L.LatLngUtil.cloneLatLngs(latlngs[i]));\n\t\t\t} else {\n\t\t\t\tclone.push(this.cloneLatLng(latlngs[i]));\n\t\t\t}\n\t\t}\n\t\treturn clone;\n\t},\n\n\t// @method cloneLatLng(LatLng): L.LatLng\n\t// Clone the latLng and return a new LatLng object.\n\tcloneLatLng: function (latlng) {\n\t\treturn L.latLng(latlng.lat, latlng.lng);\n\t}\n};\n", "(function () {\n\n\tvar defaultPrecision = {\n\t\tkm: 2,\n\t\tha: 2,\n\t\tm: 0,\n\t\tmi: 2,\n\t\tac: 2,\n\t\tyd: 0,\n\t\tft: 0,\n\t\tnm: 2\n\t};\n\n\n\t/**\n\t * @class L.GeometryUtil\n\t * @aka GeometryUtil\n\t */\n\tL.GeometryUtil = L.extend(L.GeometryUtil || {}, {\n\t\t// Ported from the OpenLayers implementation. See https://github.com/openlayers/openlayers/blob/master/lib/OpenLayers/Geometry/LinearRing.js#L270\n\n\t\t// @method geodesicArea(): number\n\t\tgeodesicArea: function (latLngs) {\n\t\t\tvar pointsCount = latLngs.length,\n\t\t\t\tarea = 0.0,\n\t\t\t\td2r = Math.PI / 180,\n\t\t\t\tp1, p2;\n\n\t\t\tif (pointsCount > 2) {\n\t\t\t\tfor (var i = 0; i < pointsCount; i++) {\n\t\t\t\t\tp1 = latLngs[i];\n\t\t\t\t\tp2 = latLngs[(i + 1) % pointsCount];\n\t\t\t\t\tarea += ((p2.lng - p1.lng) * d2r) *\n\t\t\t\t\t\t(2 + Math.sin(p1.lat * d2r) + Math.sin(p2.lat * d2r));\n\t\t\t\t}\n\t\t\t\tarea = area * 6378137.0 * 6378137.0 / 2.0;\n\t\t\t}\n\n\t\t\treturn Math.abs(area);\n\t\t},\n\n\t\t// @method formattedNumber(n, precision): string\n\t\t// Returns n in specified number format (if defined) and precision\n\t\tformattedNumber: function (n, precision) {\n\t\t\tvar formatted = parseFloat(n).toFixed(precision),\n\t\t\t\tformat = L.drawLocal.format && L.drawLocal.format.numeric,\n\t\t\t\tdelimiters = format && format.delimiters,\n\t\t\t\tthousands = delimiters && delimiters.thousands,\n\t\t\t\tdecimal = delimiters && delimiters.decimal;\n\n\t\t\tif (thousands || decimal) {\n\t\t\t\tvar splitValue = formatted.split('.');\n\t\t\t\tformatted = thousands ? splitValue[0].replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, '$1' + thousands) : splitValue[0];\n\t\t\t\tdecimal = decimal || '.';\n\t\t\t\tif (splitValue.length > 1) {\n\t\t\t\t\tformatted = formatted + decimal + splitValue[1];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn formatted;\n\t\t},\n\n\t\t// @method readableArea(area, isMetric, precision): string\n\t\t// Returns a readable area string in yards or metric.\n\t\t// The value will be rounded as defined by the precision option object.\n\t\treadableArea: function (area, isMetric, precision) {\n\t\t\tvar areaStr,\n\t\t\t\tunits,\n\t\t\t\tprecision = L.Util.extend({}, defaultPrecision, precision);\n\n\t\t\tif (isMetric) {\n\t\t\t\tunits = ['ha', 'm'];\n\t\t\t\ttype = typeof isMetric;\n\t\t\t\tif (type === 'string') {\n\t\t\t\t\tunits = [isMetric];\n\t\t\t\t} else if (type !== 'boolean') {\n\t\t\t\t\tunits = isMetric;\n\t\t\t\t}\n\n\t\t\t\tif (area >= 1000000 && units.indexOf('km') !== -1) {\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area * 0.000001, precision['km']) + ' km²';\n\t\t\t\t} else if (area >= 10000 && units.indexOf('ha') !== -1) {\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area * 0.0001, precision['ha']) + ' ha';\n\t\t\t\t} else {\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area, precision['m']) + ' m²';\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tarea /= 0.836127; // Square yards in 1 meter\n\n\t\t\t\tif (area >= 3097600) { //3097600 square yards in 1 square mile\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area / 3097600, precision['mi']) + ' mi²';\n\t\t\t\t} else if (area >= 4840) { //4840 square yards in 1 acre\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area / 4840, precision['ac']) + ' acres';\n\t\t\t\t} else {\n\t\t\t\t\tareaStr = L.GeometryUtil.formattedNumber(area, precision['yd']) + ' yd²';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn areaStr;\n\t\t},\n\n\t\t// @method readableDistance(distance, units): string\n\t\t// Converts a metric distance to one of [ feet, nauticalMile, metric or yards ] string\n\t\t//\n\t\t// @alternative\n\t\t// @method readableDistance(distance, isMetric, useFeet, isNauticalMile, precision): string\n\t\t// Converts metric distance to distance string.\n\t\t// The value will be rounded as defined by the precision option object.\n\t\treadableDistance: function (distance, isMetric, isFeet, isNauticalMile, precision) {\n\t\t\tvar distanceStr,\n\t\t\t\tunits,\n\t\t\t\tprecision = L.Util.extend({}, defaultPrecision, precision);\n\n\t\t\tif (isMetric) {\n\t\t\t\tunits = typeof isMetric == 'string' ? isMetric : 'metric';\n\t\t\t} else if (isFeet) {\n\t\t\t\tunits = 'feet';\n\t\t\t} else if (isNauticalMile) {\n\t\t\t\tunits = 'nauticalMile';\n\t\t\t} else {\n\t\t\t\tunits = 'yards';\n\t\t\t}\n\n\t\t\tswitch (units) {\n\t\t\t\tcase 'metric':\n\t\t\t\t\t// show metres when distance is < 1km, then show km\n\t\t\t\t\tif (distance > 1000) {\n\t\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance / 1000, precision['km']) + ' km';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance, precision['m']) + ' m';\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'feet':\n\t\t\t\t\tdistance *= 1.09361 * 3;\n\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance, precision['ft']) + ' ft';\n\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'nauticalMile':\n\t\t\t\t\tdistance *= 0.53996;\n\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance / 1000, precision['nm']) + ' nm';\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'yards':\n\t\t\t\tdefault:\n\t\t\t\t\tdistance *= 1.09361;\n\n\t\t\t\t\tif (distance > 1760) {\n\t\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance / 1760, precision['mi']) + ' miles';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdistanceStr = L.GeometryUtil.formattedNumber(distance, precision['yd']) + ' yd';\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn distanceStr;\n\t\t},\n\n\t\t// @method isVersion07x(): boolean\n\t\t// Returns true if the Leaflet version is 0.7.x, false otherwise.\n\t\tisVersion07x: function () {\n\t\t\tvar version = L.version.split('.');\n\t\t\t//If Version is == 0.7.*\n\t\t\treturn parseInt(version[0], 10) === 0 && parseInt(version[1], 10) === 7;\n\t\t},\n\t});\n\n})();\n", "/**\n * @class L.LineUtil\n * @aka Util\n * @aka L.Utils\n */\nL.Util.extend(<PERSON><PERSON>Line<PERSON>til, {\n\n\t// @method segmentsIntersect(): boolean\n\t// Checks to see if two line segments intersect. Does not handle degenerate cases.\n\t// http://compgeom.cs.uiuc.edu/~jeffe/teaching/373/notes/x06-sweepline.pdf\n\tsegmentsIntersect: function (/*Point*/ p, /*Point*/ p1, /*Point*/ p2, /*Point*/ p3) {\n\t\treturn this._checkCounterclockwise(p, p2, p3) !==\n\t\t\tthis._checkCounterclockwise(p1, p2, p3) &&\n\t\t\tthis._checkCounterclockwise(p, p1, p2) !==\n\t\t\tthis._checkCounterclockwise(p, p1, p3);\n\t},\n\n\t// check to see if points are in counterclockwise order\n\t_checkCounterclockwise: function (/*Point*/ p, /*Point*/ p1, /*Point*/ p2) {\n\t\treturn (p2.y - p.y) * (p1.x - p.x) > (p1.y - p.y) * (p2.x - p.x);\n\t}\n});\n", "/**\n * @class L.Polyline\n * @aka Polyline\n */\nL.Polyline.include({\n\n\t// @method intersects(): boolean\n\t// Check to see if this polyline has any linesegments that intersect.\n\t// NOTE: does not support detecting intersection for degenerate cases.\n\tintersects: function () {\n\t\tvar points = this._getProjectedPoints(),\n\t\t\tlen = points ? points.length : 0,\n\t\t\ti, p, p1;\n\n\t\tif (this._tooFewPointsForIntersection()) {\n\t\t\treturn false;\n\t\t}\n\n\t\tfor (i = len - 1; i >= 3; i--) {\n\t\t\tp = points[i - 1];\n\t\t\tp1 = points[i];\n\n\n\t\t\tif (this._lineSegmentsIntersectsRange(p, p1, i - 2)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t},\n\n\t// @method newLatLngIntersects(): boolean\n\t// Check for intersection if new latlng was added to this polyline.\n\t// NOTE: does not support detecting intersection for degenerate cases.\n\tnewLatLngIntersects: function (latlng, skipFirst) {\n\t\t// Cannot check a polyline for intersecting lats/lngs when not added to the map\n\t\tif (!this._map) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this.newPointIntersects(this._map.latLngToLayerPoint(latlng), skipFirst);\n\t},\n\n\t// @method newPointIntersects(): boolean\n\t// Check for intersection if new point was added to this polyline.\n\t// newPoint must be a layer point.\n\t// NOTE: does not support detecting intersection for degenerate cases.\n\tnewPointIntersects: function (newPoint, skipFirst) {\n\t\tvar points = this._getProjectedPoints(),\n\t\t\tlen = points ? points.length : 0,\n\t\t\tlastPoint = points ? points[len - 1] : null,\n\t\t\t// The previous previous line segment. Previous line segment doesn't need testing.\n\t\t\tmaxIndex = len - 2;\n\n\t\tif (this._tooFewPointsForIntersection(1)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this._lineSegmentsIntersectsRange(lastPoint, newPoint, maxIndex, skipFirst ? 1 : 0);\n\t},\n\n\t// Polylines with 2 sides can only intersect in cases where points are collinear (we don't support detecting these).\n\t// Cannot have intersection when < 3 line segments (< 4 points)\n\t_tooFewPointsForIntersection: function (extraPoints) {\n\t\tvar points = this._getProjectedPoints(),\n\t\t\tlen = points ? points.length : 0;\n\t\t// Increment length by extraPoints if present\n\t\tlen += extraPoints || 0;\n\n\t\treturn !points || len <= 3;\n\t},\n\n\t// Checks a line segment intersections with any line segments before its predecessor.\n\t// Don't need to check the predecessor as will never intersect.\n\t_lineSegmentsIntersectsRange: function (p, p1, maxIndex, minIndex) {\n\t\tvar points = this._getProjectedPoints(),\n\t\t\tp2, p3;\n\n\t\tminIndex = minIndex || 0;\n\n\t\t// Check all previous line segments (beside the immediately previous) for intersections\n\t\tfor (var j = maxIndex; j > minIndex; j--) {\n\t\t\tp2 = points[j - 1];\n\t\t\tp3 = points[j];\n\n\t\t\tif (L.LineUtil.segmentsIntersect(p, p1, p2, p3)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t},\n\n\t_getProjectedPoints: function () {\n\t\tif (!this._defaultShape) {\n\t\t\treturn this._originalPoints;\n\t\t}\n\t\tvar points = [],\n\t\t\t_shape = this._defaultShape();\n\n\t\tfor (var i = 0; i < _shape.length; i++) {\n\t\t\tpoints.push(this._map.latLngToLayerPoint(_shape[i]));\n\t\t}\n\t\treturn points;\n\t}\n});\n", "/**\n * @class L.Polygon\n * @aka Polygon\n */\nL.Polygon.include({\n\n\t// @method intersects(): boolean\n\t// Checks a polygon for any intersecting line segments. Ignores holes.\n\tintersects: function () {\n\t\tvar polylineIntersects,\n\t\t\tpoints = this._getProjectedPoints(),\n\t\t\tlen, firstPoint, lastPoint, maxIndex;\n\n\t\tif (this._tooFewPointsForIntersection()) {\n\t\t\treturn false;\n\t\t}\n\n\t\tpolylineIntersects = L.Polyline.prototype.intersects.call(this);\n\n\t\t// If already found an intersection don't need to check for any more.\n\t\tif (polylineIntersects) {\n\t\t\treturn true;\n\t\t}\n\n\t\tlen = points.length;\n\t\tfirstPoint = points[0];\n\t\tlastPoint = points[len - 1];\n\t\tmaxIndex = len - 2;\n\n\t\t// Check the line segment between last and first point. Don't need to check the first line segment (minIndex = 1)\n\t\treturn this._lineSegmentsIntersectsRange(lastPoint, firstPoint, maxIndex, 1);\n\t}\n});\n", "/**\n * @class L.Control.Draw\n * @aka L.Draw\n */\nL.Control.Draw = L.Control.extend({\n\n\t// Options\n\toptions: {\n\t\tposition: 'topleft',\n\t\tdraw: {},\n\t\tedit: false\n\t},\n\n\t// @method initialize(): void\n\t// Initializes draw control, toolbars from the options\n\tinitialize: function (options) {\n\t\tif (L.version < '0.7') {\n\t\t\tthrow new Error('Leaflet.draw 0.2.3+ requires Leaflet 0.7.0+. Download latest from https://github.com/Leaflet/Leaflet/');\n\t\t}\n\n\t\tL.Control.prototype.initialize.call(this, options);\n\n\t\tvar toolbar;\n\n\t\tthis._toolbars = {};\n\n\t\t// Initialize toolbars\n\t\tif (L.DrawToolbar && this.options.draw) {\n\t\t\ttoolbar = new L.DrawToolbar(this.options.draw);\n\n\t\t\tthis._toolbars[L.DrawToolbar.TYPE] = toolbar;\n\n\t\t\t// Listen for when toolbar is enabled\n\t\t\tthis._toolbars[L.DrawToolbar.TYPE].on('enable', this._toolbarEnabled, this);\n\t\t}\n\n\t\tif (L.EditToolbar && this.options.edit) {\n\t\t\ttoolbar = new L.EditToolbar(this.options.edit);\n\n\t\t\tthis._toolbars[L.EditToolbar.TYPE] = toolbar;\n\n\t\t\t// Listen for when toolbar is enabled\n\t\t\tthis._toolbars[L.EditToolbar.TYPE].on('enable', this._toolbarEnabled, this);\n\t\t}\n\t\tL.toolbar = this; //set global var for editing the toolbar\n\t},\n\n\t// @method onAdd(): container\n\t// Adds the toolbar container to the map\n\tonAdd: function (map) {\n\t\tvar container = L.DomUtil.create('div', 'leaflet-draw'),\n\t\t\taddedTopClass = false,\n\t\t\ttopClassName = 'leaflet-draw-toolbar-top',\n\t\t\ttoolbarContainer;\n\n\t\tfor (var toolbarId in this._toolbars) {\n\t\t\tif (this._toolbars.hasOwnProperty(toolbarId)) {\n\t\t\t\ttoolbarContainer = this._toolbars[toolbarId].addToolbar(map);\n\n\t\t\t\tif (toolbarContainer) {\n\t\t\t\t\t// Add class to the first toolbar to remove the margin\n\t\t\t\t\tif (!addedTopClass) {\n\t\t\t\t\t\tif (!L.DomUtil.hasClass(toolbarContainer, topClassName)) {\n\t\t\t\t\t\t\tL.DomUtil.addClass(toolbarContainer.childNodes[0], topClassName);\n\t\t\t\t\t\t}\n\t\t\t\t\t\taddedTopClass = true;\n\t\t\t\t\t}\n\n\t\t\t\t\tcontainer.appendChild(toolbarContainer);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn container;\n\t},\n\n\t// @method onRemove(): void\n\t// Removes the toolbars from the map toolbar container\n\tonRemove: function () {\n\t\tfor (var toolbarId in this._toolbars) {\n\t\t\tif (this._toolbars.hasOwnProperty(toolbarId)) {\n\t\t\t\tthis._toolbars[toolbarId].removeToolbar();\n\t\t\t}\n\t\t}\n\t},\n\n\t// @method setDrawingOptions(options): void\n\t// Sets options to all toolbar instances\n\tsetDrawingOptions: function (options) {\n\t\tfor (var toolbarId in this._toolbars) {\n\t\t\tif (this._toolbars[toolbarId] instanceof L.DrawToolbar) {\n\t\t\t\tthis._toolbars[toolbarId].setOptions(options);\n\t\t\t}\n\t\t}\n\t},\n\n\t_toolbarEnabled: function (e) {\n\t\tvar enabledToolbar = e.target;\n\n\t\tfor (var toolbarId in this._toolbars) {\n\t\t\tif (this._toolbars[toolbarId] !== enabledToolbar) {\n\t\t\t\tthis._toolbars[toolbarId].disable();\n\t\t\t}\n\t\t}\n\t}\n});\n\nL.Map.mergeOptions({\n\tdrawControlTooltips: true,\n\tdrawControl: false\n});\n\nL.Map.addInitHook(function () {\n\tif (this.options.drawControl) {\n\t\tthis.drawControl = new L.Control.Draw();\n\t\tthis.addControl(this.drawControl);\n\t}\n});\n", "/**\n * @class L.Draw.Toolbar\n * @aka Toolbar\n *\n * The toolbar class of the API — it is used to create the ui\n * This will be depreciated\n *\n * @example\n *\n * ```js\n *    var toolbar = L.Toolbar();\n *    toolbar.addToolbar(map);\n * ```\n *\n * ### Disabling a toolbar\n *\n * If you do not want a particular toolbar in your app you can turn it off by setting the toolbar to false.\n *\n * ```js\n *      var drawControl = new L.Control.Draw({\n *          draw: false,\n *          edit: {\n *              featureGroup: editableLayers\n *          }\n *      });\n * ```\n *\n * ### Disabling a toolbar item\n *\n * If you want to turn off a particular toolbar item, set it to false. The following disables drawing polygons and\n * markers. It also turns off the ability to edit layers.\n *\n * ```js\n *      var drawControl = new L.Control.Draw({\n *          draw: {\n *              polygon: false,\n *              marker: false\n *          },\n *          edit: {\n *              featureGroup: editableLayers,\n *              edit: false\n *          }\n *      });\n * ```\n */\nL.Toolbar = L.Class.extend({\n\t// @section Methods for modifying the toolbar\n\n\t// @method initialize(options): void\n\t// Toolbar constructor\n\tinitialize: function (options) {\n\t\tL.setOptions(this, options);\n\n\t\tthis._modes = {};\n\t\tthis._actionButtons = [];\n\t\tthis._activeMode = null;\n\n\t\tvar version = L.version.split('.');\n\t\t//If Version is >= 1.2.0\n\t\tif (parseInt(version[0], 10) === 1 && parseInt(version[1], 10) >= 2) {\n\t\t\tL.Toolbar.include(L.Evented.prototype);\n\t\t} else {\n\t\t\tL.Toolbar.include(L.Mixin.Events);\n\t\t}\n\t},\n\n\t// @method enabled(): boolean\n\t// Gets a true/false of whether the toolbar is enabled\n\tenabled: function () {\n\t\treturn this._activeMode !== null;\n\t},\n\n\t// @method disable(): void\n\t// Disables the toolbar\n\tdisable: function () {\n\t\tif (!this.enabled()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._activeMode.handler.disable();\n\t},\n\n\t// @method addToolbar(map): L.DomUtil\n\t// Adds the toolbar to the map and returns the toolbar dom element\n\taddToolbar: function (map) {\n\t\tvar container = L.DomUtil.create('div', 'leaflet-draw-section'),\n\t\t\tbuttonIndex = 0,\n\t\t\tbuttonClassPrefix = this._toolbarClass || '',\n\t\t\tmodeHandlers = this.getModeHandlers(map),\n\t\t\ti;\n\n\t\tthis._toolbarContainer = L.DomUtil.create('div', 'leaflet-draw-toolbar leaflet-bar');\n\t\tthis._map = map;\n\n\t\tfor (i = 0; i < modeHandlers.length; i++) {\n\t\t\tif (modeHandlers[i].enabled) {\n\t\t\t\tthis._initModeHandler(\n\t\t\t\t\tmodeHandlers[i].handler,\n\t\t\t\t\tthis._toolbarContainer,\n\t\t\t\t\tbuttonIndex++,\n\t\t\t\t\tbuttonClassPrefix,\n\t\t\t\t\tmodeHandlers[i].title\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\t// if no buttons were added, do not add the toolbar\n\t\tif (!buttonIndex) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Save button index of the last button, -1 as we would have ++ after the last button\n\t\tthis._lastButtonIndex = --buttonIndex;\n\n\t\t// Create empty actions part of the toolbar\n\t\tthis._actionsContainer = L.DomUtil.create('ul', 'leaflet-draw-actions');\n\n\t\t// Add draw and cancel containers to the control container\n\t\tcontainer.appendChild(this._toolbarContainer);\n\t\tcontainer.appendChild(this._actionsContainer);\n\n\t\treturn container;\n\t},\n\n\t// @method removeToolbar(): void\n\t// Removes the toolbar and drops the handler event listeners\n\tremoveToolbar: function () {\n\t\t// Dispose each handler\n\t\tfor (var handlerId in this._modes) {\n\t\t\tif (this._modes.hasOwnProperty(handlerId)) {\n\t\t\t\t// Unbind handler button\n\t\t\t\tthis._disposeButton(\n\t\t\t\t\tthis._modes[handlerId].button,\n\t\t\t\t\tthis._modes[handlerId].handler.enable,\n\t\t\t\t\tthis._modes[handlerId].handler\n\t\t\t\t);\n\n\t\t\t\t// Make sure is disabled\n\t\t\t\tthis._modes[handlerId].handler.disable();\n\n\t\t\t\t// Unbind handler\n\t\t\t\tthis._modes[handlerId].handler\n\t\t\t\t\t.off('enabled', this._handlerActivated, this)\n\t\t\t\t\t.off('disabled', this._handlerDeactivated, this);\n\t\t\t}\n\t\t}\n\t\tthis._modes = {};\n\n\t\t// Dispose the actions toolbar\n\t\tfor (var i = 0, l = this._actionButtons.length; i < l; i++) {\n\t\t\tthis._disposeButton(\n\t\t\t\tthis._actionButtons[i].button,\n\t\t\t\tthis._actionButtons[i].callback,\n\t\t\t\tthis\n\t\t\t);\n\t\t}\n\t\tthis._actionButtons = [];\n\t\tthis._actionsContainer = null;\n\t},\n\n\t_initModeHandler: function (handler, container, buttonIndex, classNamePredix, buttonTitle) {\n\t\tvar type = handler.type;\n\n\t\tthis._modes[type] = {};\n\n\t\tthis._modes[type].handler = handler;\n\n\t\tthis._modes[type].button = this._createButton({\n\t\t\ttype: type,\n\t\t\ttitle: buttonTitle,\n\t\t\tclassName: classNamePredix + '-' + type,\n\t\t\tcontainer: container,\n\t\t\tcallback: this._modes[type].handler.enable,\n\t\t\tcontext: this._modes[type].handler\n\t\t});\n\n\t\tthis._modes[type].buttonIndex = buttonIndex;\n\n\t\tthis._modes[type].handler\n\t\t\t.on('enabled', this._handlerActivated, this)\n\t\t\t.on('disabled', this._handlerDeactivated, this);\n\t},\n\n\t/* Detect iOS based on browser User Agent, based on:\n\t * http://stackoverflow.com/a/9039885 */\n\t_detectIOS: function () {\n\t\tvar iOS = (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream);\n\t\treturn iOS;\n\t},\n\n\t_createButton: function (options) {\n\n\t\tvar link = L.DomUtil.create('a', options.className || '', options.container);\n\t\t// Screen reader tag\n\t\tvar sr = L.DomUtil.create('span', 'sr-only', options.container);\n\n\t\tlink.href = '#';\n\t\tlink.appendChild(sr);\n\n\t\tif (options.title) {\n\t\t\tlink.title = options.title;\n\t\t\tsr.innerHTML = options.title;\n\t\t}\n\n\t\tif (options.text) {\n\t\t\tlink.innerHTML = options.text;\n\t\t\tsr.innerHTML = options.text;\n\t\t}\n\n\t\t/* iOS does not use click events */\n\t\tvar buttonEvent = this._detectIOS() ? 'touchstart' : 'click';\n\n\t\tL.DomEvent\n\t\t\t.on(link, 'click', L.DomEvent.stopPropagation)\n\t\t\t.on(link, 'mousedown', L.DomEvent.stopPropagation)\n\t\t\t.on(link, 'dblclick', L.DomEvent.stopPropagation)\n\t\t\t.on(link, 'touchstart', L.DomEvent.stopPropagation)\n\t\t\t.on(link, 'click', L.DomEvent.preventDefault)\n\t\t\t.on(link, buttonEvent, options.callback, options.context);\n\n\t\treturn link;\n\t},\n\n\t_disposeButton: function (button, callback) {\n\t\t/* iOS does not use click events */\n\t\tvar buttonEvent = this._detectIOS() ? 'touchstart' : 'click';\n\n\t\tL.DomEvent\n\t\t\t.off(button, 'click', L.DomEvent.stopPropagation)\n\t\t\t.off(button, 'mousedown', L.DomEvent.stopPropagation)\n\t\t\t.off(button, 'dblclick', L.DomEvent.stopPropagation)\n\t\t\t.off(button, 'touchstart', L.DomEvent.stopPropagation)\n\t\t\t.off(button, 'click', L.DomEvent.preventDefault)\n\t\t\t.off(button, buttonEvent, callback);\n\t},\n\n\t_handlerActivated: function (e) {\n\t\t// Disable active mode (if present)\n\t\tthis.disable();\n\n\t\t// Cache new active feature\n\t\tthis._activeMode = this._modes[e.handler];\n\n\t\tL.DomUtil.addClass(this._activeMode.button, 'leaflet-draw-toolbar-button-enabled');\n\n\t\tthis._showActionsToolbar();\n\n\t\tthis.fire('enable');\n\t},\n\n\t_handlerDeactivated: function () {\n\t\tthis._hideActionsToolbar();\n\n\t\tL.DomUtil.removeClass(this._activeMode.button, 'leaflet-draw-toolbar-button-enabled');\n\n\t\tthis._activeMode = null;\n\n\t\tthis.fire('disable');\n\t},\n\n\t_createActions: function (handler) {\n\t\tvar container = this._actionsContainer,\n\t\t\tbuttons = this.getActions(handler),\n\t\t\tl = buttons.length,\n\t\t\tli, di, dl, button;\n\n\t\t// Dispose the actions toolbar (todo: dispose only not used buttons)\n\t\tfor (di = 0, dl = this._actionButtons.length; di < dl; di++) {\n\t\t\tthis._disposeButton(this._actionButtons[di].button, this._actionButtons[di].callback);\n\t\t}\n\t\tthis._actionButtons = [];\n\n\t\t// Remove all old buttons\n\t\twhile (container.firstChild) {\n\t\t\tcontainer.removeChild(container.firstChild);\n\t\t}\n\n\t\tfor (var i = 0; i < l; i++) {\n\t\t\tif ('enabled' in buttons[i] && !buttons[i].enabled) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tli = L.DomUtil.create('li', '', container);\n\n\t\t\tbutton = this._createButton({\n\t\t\t\ttitle: buttons[i].title,\n\t\t\t\ttext: buttons[i].text,\n\t\t\t\tcontainer: li,\n\t\t\t\tcallback: buttons[i].callback,\n\t\t\t\tcontext: buttons[i].context\n\t\t\t});\n\n\t\t\tthis._actionButtons.push({\n\t\t\t\tbutton: button,\n\t\t\t\tcallback: buttons[i].callback\n\t\t\t});\n\t\t}\n\t},\n\n\t_showActionsToolbar: function () {\n\t\tvar buttonIndex = this._activeMode.buttonIndex,\n\t\t\tlastButtonIndex = this._lastButtonIndex,\n\t\t\ttoolbarPosition = this._activeMode.button.offsetTop - 1;\n\n\t\t// Recreate action buttons on every click\n\t\tthis._createActions(this._activeMode.handler);\n\n\t\t// Correctly position the cancel button\n\t\tthis._actionsContainer.style.top = toolbarPosition + 'px';\n\n\t\tif (buttonIndex === 0) {\n\t\t\tL.DomUtil.addClass(this._toolbarContainer, 'leaflet-draw-toolbar-notop');\n\t\t\tL.DomUtil.addClass(this._actionsContainer, 'leaflet-draw-actions-top');\n\t\t}\n\n\t\tif (buttonIndex === lastButtonIndex) {\n\t\t\tL.DomUtil.addClass(this._toolbarContainer, 'leaflet-draw-toolbar-nobottom');\n\t\t\tL.DomUtil.addClass(this._actionsContainer, 'leaflet-draw-actions-bottom');\n\t\t}\n\n\t\tthis._actionsContainer.style.display = 'block';\n\t\tthis._map.fire(L.Draw.Event.TOOLBAROPENED);\n\t},\n\n\t_hideActionsToolbar: function () {\n\t\tthis._actionsContainer.style.display = 'none';\n\n\t\tL.DomUtil.removeClass(this._toolbarContainer, 'leaflet-draw-toolbar-notop');\n\t\tL.DomUtil.removeClass(this._toolbarContainer, 'leaflet-draw-toolbar-nobottom');\n\t\tL.DomUtil.removeClass(this._actionsContainer, 'leaflet-draw-actions-top');\n\t\tL.DomUtil.removeClass(this._actionsContainer, 'leaflet-draw-actions-bottom');\n\t\tthis._map.fire(L.Draw.Event.TOOLBARCLOSED);\n\t}\n});\n", "L.Draw = L.Draw || {};\n/**\n * @class L.Draw.Tooltip\n * @aka Tooltip\n *\n * The tooltip class — it is used to display the tooltip while drawing\n * This will be depreciated\n *\n * @example\n *\n * ```js\n *    var tooltip = L.Draw.Tooltip();\n * ```\n *\n */\nL.Draw.Tooltip = L.Class.extend({\n\n\t// @section Methods for modifying draw state\n\n\t// @method initialize(map): void\n\t// Tooltip constructor\n\tinitialize: function (map) {\n\t\tthis._map = map;\n\t\tthis._popupPane = map._panes.popupPane;\n\t\tthis._visible = false;\n\n\t\tthis._container = map.options.drawControlTooltips ?\n\t\t\tL.DomUtil.create('div', 'leaflet-draw-tooltip', this._popupPane) : null;\n\t\tthis._singleLineLabel = false;\n\n\t\tthis._map.on('mouseout', this._onMouseOut, this);\n\t},\n\n\t// @method dispose(): void\n\t// Remove Tooltip DOM and unbind events\n\tdispose: function () {\n\t\tthis._map.off('mouseout', this._onMouseOut, this);\n\n\t\tif (this._container) {\n\t\t\tthis._popupPane.removeChild(this._container);\n\t\t\tthis._container = null;\n\t\t}\n\t},\n\n\t// @method updateContent(labelText): this\n\t// Changes the tooltip text to string in function call\n\tupdateContent: function (labelText) {\n\t\tif (!this._container) {\n\t\t\treturn this;\n\t\t}\n\t\tlabelText.subtext = labelText.subtext || '';\n\n\t\t// update the vertical position (only if changed)\n\t\tif (labelText.subtext.length === 0 && !this._singleLineLabel) {\n\t\t\tL.DomUtil.addClass(this._container, 'leaflet-draw-tooltip-single');\n\t\t\tthis._singleLineLabel = true;\n\t\t}\n\t\telse if (labelText.subtext.length > 0 && this._singleLineLabel) {\n\t\t\tL.DomUtil.removeClass(this._container, 'leaflet-draw-tooltip-single');\n\t\t\tthis._singleLineLabel = false;\n\t\t}\n\n\t\tthis._container.innerHTML =\n\t\t\t(labelText.subtext.length > 0 ?\n\t\t\t\t'<span class=\"leaflet-draw-tooltip-subtext\">' + labelText.subtext + '</span>' + '<br />' : '') +\n\t\t\t'<span>' + labelText.text + '</span>';\n\n\t\tif (!labelText.text && !labelText.subtext) {\n\t\t\tthis._visible = false;\n\t\t\tthis._container.style.visibility = 'hidden';\n\t\t} else {\n\t\t\tthis._visible = true;\n\t\t\tthis._container.style.visibility = 'inherit';\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t// @method updatePosition(latlng): this\n\t// Changes the location of the tooltip\n\tupdatePosition: function (latlng) {\n\t\tvar pos = this._map.latLngToLayerPoint(latlng),\n\t\t\ttooltipContainer = this._container;\n\n\t\tif (this._container) {\n\t\t\tif (this._visible) {\n\t\t\t\ttooltipContainer.style.visibility = 'inherit';\n\t\t\t}\n\t\t\tL.DomUtil.setPosition(tooltipContainer, pos);\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t// @method showAsError(): this\n\t// Applies error class to tooltip\n\tshowAsError: function () {\n\t\tif (this._container) {\n\t\t\tL.DomUtil.addClass(this._container, 'leaflet-error-draw-tooltip');\n\t\t}\n\t\treturn this;\n\t},\n\n\t// @method removeError(): this\n\t// Removes the error class from the tooltip\n\tremoveError: function () {\n\t\tif (this._container) {\n\t\t\tL.DomUtil.removeClass(this._container, 'leaflet-error-draw-tooltip');\n\t\t}\n\t\treturn this;\n\t},\n\n\t_onMouseOut: function () {\n\t\tif (this._container) {\n\t\t\tthis._container.style.visibility = 'hidden';\n\t\t}\n\t}\n});\n", "/**\n * @class L.DrawToolbar\n * @aka Toolbar\n */\nL.DrawToolbar = L.Toolbar.extend({\n\n\tstatics: {\n\t\tTYPE: 'draw'\n\t},\n\n\toptions: {\n\t\tpolyline: {},\n\t\tpolygon: {},\n\t\trectangle: {},\n\t\tcircle: {},\n\t\tmarker: {},\n\t\tcirclemarker: {}\n\t},\n\n\t// @method initialize(): void\n\tinitialize: function (options) {\n\t\t// Ensure that the options are merged correctly since L.extend is only shallow\n\t\tfor (var type in this.options) {\n\t\t\tif (this.options.hasOwnProperty(type)) {\n\t\t\t\tif (options[type]) {\n\t\t\t\t\toptions[type] = L.extend({}, this.options[type], options[type]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis._toolbarClass = 'leaflet-draw-draw';\n\t\tL.Toolbar.prototype.initialize.call(this, options);\n\t},\n\n\t// @method getModeHandlers(): object\n\t// Get mode handlers information\n\tgetModeHandlers: function (map) {\n\t\treturn [\n\t\t\t{\n\t\t\t\tenabled: this.options.polyline,\n\t\t\t\thandler: new L.Draw.Polyline(map, this.options.polyline),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.polyline\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.polygon,\n\t\t\t\thandler: new L.Draw.Polygon(map, this.options.polygon),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.polygon\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.rectangle,\n\t\t\t\thandler: new L.Draw.Rectangle(map, this.options.rectangle),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.rectangle\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.circle,\n\t\t\t\thandler: new L.Draw.Circle(map, this.options.circle),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.circle\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.marker,\n\t\t\t\thandler: new L.Draw.Marker(map, this.options.marker),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.marker\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.circlemarker,\n\t\t\t\thandler: new L.Draw.CircleMarker(map, this.options.circlemarker),\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.buttons.circlemarker\n\t\t\t}\n\t\t];\n\t},\n\n\t// @method getActions(): object\n\t// Get action information\n\tgetActions: function (handler) {\n\t\treturn [\n\t\t\t{\n\t\t\t\tenabled: handler.completeShape,\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.finish.title,\n\t\t\t\ttext: L.drawLocal.draw.toolbar.finish.text,\n\t\t\t\tcallback: handler.completeShape,\n\t\t\t\tcontext: handler\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: handler.deleteLastVertex,\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.undo.title,\n\t\t\t\ttext: L.drawLocal.draw.toolbar.undo.text,\n\t\t\t\tcallback: handler.deleteLastVertex,\n\t\t\t\tcontext: handler\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: L.drawLocal.draw.toolbar.actions.title,\n\t\t\t\ttext: L.drawLocal.draw.toolbar.actions.text,\n\t\t\t\tcallback: this.disable,\n\t\t\t\tcontext: this\n\t\t\t}\n\t\t];\n\t},\n\n\t// @method setOptions(): void\n\t// Sets the options to the toolbar\n\tsetOptions: function (options) {\n\t\tL.setOptions(this, options);\n\n\t\tfor (var type in this._modes) {\n\t\t\tif (this._modes.hasOwnProperty(type) && options.hasOwnProperty(type)) {\n\t\t\t\tthis._modes[type].handler.setOptions(options[type]);\n\t\t\t}\n\t\t}\n\t}\n});\n", "/*L.Map.mergeOptions({\n editControl: true\n });*/\n/**\n * @class L.EditToolbar\n * @aka EditToolbar\n */\nL.EditToolbar = L.Toolbar.extend({\n\tstatics: {\n\t\tTYPE: 'edit'\n\t},\n\n\toptions: {\n\t\tedit: {\n\t\t\tselectedPathOptions: {\n\t\t\t\tdashArray: '10, 10',\n\n\t\t\t\tfill: true,\n\t\t\t\tfillColor: '#fe57a1',\n\t\t\t\tfillOpacity: 0.1,\n\n\t\t\t\t// Whether to user the existing layers color\n\t\t\t\tmaintainColor: false\n\t\t\t}\n\t\t},\n\t\tremove: {},\n\t\tpoly: null,\n\t\tfeatureGroup: null /* REQUIRED! TODO: perhaps if not set then all layers on the map are selectable? */\n\t},\n\n\t// @method intialize(): void\n\tinitialize: function (options) {\n\t\t// Need to set this manually since null is an acceptable value here\n\t\tif (options.edit) {\n\t\t\tif (typeof options.edit.selectedPathOptions === 'undefined') {\n\t\t\t\toptions.edit.selectedPathOptions = this.options.edit.selectedPathOptions;\n\t\t\t}\n\t\t\toptions.edit.selectedPathOptions = L.extend({}, this.options.edit.selectedPathOptions, options.edit.selectedPathOptions);\n\t\t}\n\n\t\tif (options.remove) {\n\t\t\toptions.remove = L.extend({}, this.options.remove, options.remove);\n\t\t}\n\n\t\tif (options.poly) {\n\t\t\toptions.poly = L.extend({}, this.options.poly, options.poly);\n\t\t}\n\n\t\tthis._toolbarClass = 'leaflet-draw-edit';\n\t\tL.Toolbar.prototype.initialize.call(this, options);\n\n\t\tthis._selectedFeatureCount = 0;\n\t},\n\n\t// @method getModeHandlers(): object\n\t// Get mode handlers information\n\tgetModeHandlers: function (map) {\n\t\tvar featureGroup = this.options.featureGroup;\n\t\treturn [\n\t\t\t{\n\t\t\t\tenabled: this.options.edit,\n\t\t\t\thandler: new L.EditToolbar.Edit(map, {\n\t\t\t\t\tfeatureGroup: featureGroup,\n\t\t\t\t\tselectedPathOptions: this.options.edit.selectedPathOptions,\n\t\t\t\t\tpoly: this.options.poly\n\t\t\t\t}),\n\t\t\t\ttitle: L.drawLocal.edit.toolbar.buttons.edit\n\t\t\t},\n\t\t\t{\n\t\t\t\tenabled: this.options.remove,\n\t\t\t\thandler: new L.EditToolbar.Delete(map, {\n\t\t\t\t\tfeatureGroup: featureGroup\n\t\t\t\t}),\n\t\t\t\ttitle: L.drawLocal.edit.toolbar.buttons.remove\n\t\t\t}\n\t\t];\n\t},\n\n\t// @method getActions(): object\n\t// Get actions information\n\tgetActions: function (handler) {\n\t\tvar actions = [\n\t\t\t{\n\t\t\t\ttitle: L.drawLocal.edit.toolbar.actions.save.title,\n\t\t\t\ttext: L.drawLocal.edit.toolbar.actions.save.text,\n\t\t\t\tcallback: this._save,\n\t\t\t\tcontext: this\n\t\t\t},\n\t\t\t{\n\t\t\t\ttitle: L.drawLocal.edit.toolbar.actions.cancel.title,\n\t\t\t\ttext: L.drawLocal.edit.toolbar.actions.cancel.text,\n\t\t\t\tcallback: this.disable,\n\t\t\t\tcontext: this\n\t\t\t}\n\t\t];\n\n\t\tif (handler.removeAllLayers) {\n\t\t\tactions.push({\n\t\t\t\ttitle: L.drawLocal.edit.toolbar.actions.clearAll.title,\n\t\t\t\ttext: L.drawLocal.edit.toolbar.actions.clearAll.text,\n\t\t\t\tcallback: this._clearAllLayers,\n\t\t\t\tcontext: this\n\t\t\t});\n\t\t}\n\n\t\treturn actions;\n\t},\n\n\t// @method addToolbar(map): L.DomUtil\n\t// Adds the toolbar to the map\n\taddToolbar: function (map) {\n\t\tvar container = L.Toolbar.prototype.addToolbar.call(this, map);\n\n\t\tthis._checkDisabled();\n\n\t\tthis.options.featureGroup.on('layeradd layerremove', this._checkDisabled, this);\n\n\t\treturn container;\n\t},\n\n\t// @method removeToolbar(): void\n\t// Removes the toolbar from the map\n\tremoveToolbar: function () {\n\t\tthis.options.featureGroup.off('layeradd layerremove', this._checkDisabled, this);\n\n\t\tL.Toolbar.prototype.removeToolbar.call(this);\n\t},\n\n\t// @method disable(): void\n\t// Disables the toolbar\n\tdisable: function () {\n\t\tif (!this.enabled()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._activeMode.handler.revertLayers();\n\n\t\tL.Toolbar.prototype.disable.call(this);\n\t},\n\n\t_save: function () {\n\t\tthis._activeMode.handler.save();\n\t\tif (this._activeMode) {\n\t\t\tthis._activeMode.handler.disable();\n\t\t}\n\t},\n\n\t_clearAllLayers: function () {\n\t\tthis._activeMode.handler.removeAllLayers();\n\t\tif (this._activeMode) {\n\t\t\tthis._activeMode.handler.disable();\n\t\t}\n\t},\n\n\t_checkDisabled: function () {\n\t\tvar featureGroup = this.options.featureGroup,\n\t\t\thasLayers = featureGroup.getLayers().length !== 0,\n\t\t\tbutton;\n\n\t\tif (this.options.edit) {\n\t\t\tbutton = this._modes[L.EditToolbar.Edit.TYPE].button;\n\n\t\t\tif (hasLayers) {\n\t\t\t\tL.DomUtil.removeClass(button, 'leaflet-disabled');\n\t\t\t} else {\n\t\t\t\tL.DomUtil.addClass(button, 'leaflet-disabled');\n\t\t\t}\n\n\t\t\tbutton.setAttribute(\n\t\t\t\t'title',\n\t\t\t\thasLayers ?\n\t\t\t\t\tL.drawLocal.edit.toolbar.buttons.edit\n\t\t\t\t\t: L.drawLocal.edit.toolbar.buttons.editDisabled\n\t\t\t);\n\t\t}\n\n\t\tif (this.options.remove) {\n\t\t\tbutton = this._modes[L.EditToolbar.Delete.TYPE].button;\n\n\t\t\tif (hasLayers) {\n\t\t\t\tL.DomUtil.removeClass(button, 'leaflet-disabled');\n\t\t\t} else {\n\t\t\t\tL.DomUtil.addClass(button, 'leaflet-disabled');\n\t\t\t}\n\n\t\t\tbutton.setAttribute(\n\t\t\t\t'title',\n\t\t\t\thasLayers ?\n\t\t\t\t\tL.drawLocal.edit.toolbar.buttons.remove\n\t\t\t\t\t: L.drawLocal.edit.toolbar.buttons.removeDisabled\n\t\t\t);\n\t\t}\n\t}\n});\n", "/**\n * @class L.EditToolbar.Edit\n * @aka EditToolbar.Edit\n */\nL.EditToolbar.Edit = L.Handler.extend({\n\tstatics: {\n\t\tTYPE: 'edit'\n\t},\n\n\t// @method intialize(): void\n\tinitialize: function (map, options) {\n\t\tL.Handler.prototype.initialize.call(this, map);\n\n\t\tL.setOptions(this, options);\n\n\t\t// Store the selectable layer group for ease of access\n\t\tthis._featureGroup = options.featureGroup;\n\n\t\tif (!(this._featureGroup instanceof L.FeatureGroup)) {\n\t\t\tthrow new Error('options.featureGroup must be a L.FeatureGroup');\n\t\t}\n\n\t\tthis._uneditedLayerProps = {};\n\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.EditToolbar.Edit.TYPE;\n\n\t\tvar version = L.version.split('.');\n\t\t//If Version is >= 1.2.0\n\t\tif (parseInt(version[0], 10) === 1 && parseInt(version[1], 10) >= 2) {\n\t\t\tL.EditToolbar.Edit.include(L.Evented.prototype);\n\t\t} else {\n\t\t\tL.EditToolbar.Edit.include(L.Mixin.Events);\n\t\t}\n\t},\n\n\t// @method enable(): void\n\t// Enable the edit toolbar\n\tenable: function () {\n\t\tif (this._enabled || !this._hasAvailableLayers()) {\n\t\t\treturn;\n\t\t}\n\t\tthis.fire('enabled', {handler: this.type});\n\t\t//this disable other handlers\n\n\t\tthis._map.fire(L.Draw.Event.EDITSTART, {handler: this.type});\n\t\t//allow drawLayer to be updated before beginning edition.\n\n\t\tL.Handler.prototype.enable.call(this);\n\t\tthis._featureGroup\n\t\t\t.on('layeradd', this._enableLayerEdit, this)\n\t\t\t.on('layerremove', this._disableLayerEdit, this);\n\t},\n\n\t// @method disable(): void\n\t// Disable the edit toolbar\n\tdisable: function () {\n\t\tif (!this._enabled) {\n\t\t\treturn;\n\t\t}\n\t\tthis._featureGroup\n\t\t\t.off('layeradd', this._enableLayerEdit, this)\n\t\t\t.off('layerremove', this._disableLayerEdit, this);\n\t\tL.Handler.prototype.disable.call(this);\n\t\tthis._map.fire(L.Draw.Event.EDITSTOP, {handler: this.type});\n\t\tthis.fire('disabled', {handler: this.type});\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks for this handler\n\taddHooks: function () {\n\t\tvar map = this._map;\n\n\t\tif (map) {\n\t\t\tmap.getContainer().focus();\n\n\t\t\tthis._featureGroup.eachLayer(this._enableLayerEdit, this);\n\n\t\t\tthis._tooltip = new L.Draw.Tooltip(this._map);\n\t\t\tthis._tooltip.updateContent({\n\t\t\t\ttext: L.drawLocal.edit.handlers.edit.tooltip.text,\n\t\t\t\tsubtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n\t\t\t});\n\n\t\t\t// Quickly access the tooltip to update for intersection checking\n\t\t\tmap._editTooltip = this._tooltip;\n\n\t\t\tthis._updateTooltip();\n\n\t\t\tthis._map\n\t\t\t\t.on('mousemove', this._onMouseMove, this)\n\t\t\t\t.on('touchmove', this._onMouseMove, this)\n\t\t\t\t.on('MSPointerMove', this._onMouseMove, this)\n\t\t\t\t.on(L.Draw.Event.EDITVERTEX, this._updateTooltip, this);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks for this handler\n\tremoveHooks: function () {\n\t\tif (this._map) {\n\t\t\t// Clean up selected layers.\n\t\t\tthis._featureGroup.eachLayer(this._disableLayerEdit, this);\n\n\t\t\t// Clear the backups of the original layers\n\t\t\tthis._uneditedLayerProps = {};\n\n\t\t\tthis._tooltip.dispose();\n\t\t\tthis._tooltip = null;\n\n\t\t\tthis._map\n\t\t\t\t.off('mousemove', this._onMouseMove, this)\n\t\t\t\t.off('touchmove', this._onMouseMove, this)\n\t\t\t\t.off('MSPointerMove', this._onMouseMove, this)\n\t\t\t\t.off(L.Draw.Event.EDITVERTEX, this._updateTooltip, this);\n\t\t}\n\t},\n\n\t// @method revertLayers(): void\n\t// Revert each layer's geometry changes\n\trevertLayers: function () {\n\t\tthis._featureGroup.eachLayer(function (layer) {\n\t\t\tthis._revertLayer(layer);\n\t\t}, this);\n\t},\n\n\t// @method save(): void\n\t// Save the layer geometries\n\tsave: function () {\n\t\tvar editedLayers = new L.LayerGroup();\n\t\tthis._featureGroup.eachLayer(function (layer) {\n\t\t\tif (layer.edited) {\n\t\t\t\teditedLayers.addLayer(layer);\n\t\t\t\tlayer.edited = false;\n\t\t\t}\n\t\t});\n\t\tthis._map.fire(L.Draw.Event.EDITED, {layers: editedLayers});\n\t},\n\n\t_backupLayer: function (layer) {\n\t\tvar id = L.Util.stamp(layer);\n\n\t\tif (!this._uneditedLayerProps[id]) {\n\t\t\t// Polyline, Polygon or Rectangle\n\t\t\tif (layer instanceof L.Polyline || layer instanceof L.Polygon || layer instanceof L.Rectangle) {\n\t\t\t\tthis._uneditedLayerProps[id] = {\n\t\t\t\t\tlatlngs: L.LatLngUtil.cloneLatLngs(layer.getLatLngs())\n\t\t\t\t};\n\t\t\t} else if (layer instanceof L.Circle) {\n\t\t\t\tthis._uneditedLayerProps[id] = {\n\t\t\t\t\tlatlng: L.LatLngUtil.cloneLatLng(layer.getLatLng()),\n\t\t\t\t\tradius: layer.getRadius()\n\t\t\t\t};\n\t\t\t} else if (layer instanceof L.Marker || layer instanceof L.CircleMarker) { // Marker\n\t\t\t\tthis._uneditedLayerProps[id] = {\n\t\t\t\t\tlatlng: L.LatLngUtil.cloneLatLng(layer.getLatLng())\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t},\n\n\t_getTooltipText: function () {\n\t\treturn ({\n\t\t\ttext: L.drawLocal.edit.handlers.edit.tooltip.text,\n\t\t\tsubtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n\t\t});\n\t},\n\n\t_updateTooltip: function () {\n\t\tthis._tooltip.updateContent(this._getTooltipText());\n\t},\n\n\t_revertLayer: function (layer) {\n\t\tvar id = L.Util.stamp(layer);\n\t\tlayer.edited = false;\n\t\tif (this._uneditedLayerProps.hasOwnProperty(id)) {\n\t\t\t// Polyline, Polygon or Rectangle\n\t\t\tif (layer instanceof L.Polyline || layer instanceof L.Polygon || layer instanceof L.Rectangle) {\n\t\t\t\tlayer.setLatLngs(this._uneditedLayerProps[id].latlngs);\n\t\t\t} else if (layer instanceof L.Circle) {\n\t\t\t\tlayer.setLatLng(this._uneditedLayerProps[id].latlng);\n\t\t\t\tlayer.setRadius(this._uneditedLayerProps[id].radius);\n\t\t\t} else if (layer instanceof L.Marker || layer instanceof L.CircleMarker) { // Marker or CircleMarker\n\t\t\t\tlayer.setLatLng(this._uneditedLayerProps[id].latlng);\n\t\t\t}\n\n\t\t\tlayer.fire('revert-edited', {layer: layer});\n\t\t}\n\t},\n\n\t_enableLayerEdit: function (e) {\n\t\tvar layer = e.layer || e.target || e,\n\t\t\tpathOptions, poly;\n\n\t\t// Back up this layer (if haven't before)\n\t\tthis._backupLayer(layer);\n\n\t\tif (this.options.poly) {\n\t\t\tpoly = L.Util.extend({}, this.options.poly);\n\t\t\tlayer.options.poly = poly;\n\t\t}\n\n\t\t// Set different style for editing mode\n\t\tif (this.options.selectedPathOptions) {\n\t\t\tpathOptions = L.Util.extend({}, this.options.selectedPathOptions);\n\n\t\t\t// Use the existing color of the layer\n\t\t\tif (pathOptions.maintainColor) {\n\t\t\t\tpathOptions.color = layer.options.color;\n\t\t\t\tpathOptions.fillColor = layer.options.fillColor;\n\t\t\t}\n\n\t\t\tlayer.options.original = L.extend({}, layer.options);\n\t\t\tlayer.options.editing = pathOptions;\n\n\t\t}\n\n\t\tif (layer instanceof L.Marker) {\n\t\t\tif (layer.editing) {\n\t\t\t\tlayer.editing.enable();\n\t\t\t}\n\t\t\tlayer.dragging.enable();\n\t\t\tlayer\n\t\t\t\t.on('dragend', this._onMarkerDragEnd)\n\t\t\t\t// #TODO: remove when leaflet finally fixes their draggable so it's touch friendly again.\n\t\t\t\t.on('touchmove', this._onTouchMove, this)\n\t\t\t\t.on('MSPointerMove', this._onTouchMove, this)\n\t\t\t\t.on('touchend', this._onMarkerDragEnd, this)\n\t\t\t\t.on('MSPointerUp', this._onMarkerDragEnd, this);\n\t\t} else {\n\t\t\tlayer.editing.enable();\n\t\t}\n\t},\n\n\t_disableLayerEdit: function (e) {\n\t\tvar layer = e.layer || e.target || e;\n\n\t\tlayer.edited = false;\n\t\tif (layer.editing) {\n\t\t\tlayer.editing.disable();\n\t\t}\n\n\t\tdelete layer.options.editing;\n\t\tdelete layer.options.original;\n\t\t// Reset layer styles to that of before select\n\t\tif (this._selectedPathOptions) {\n\t\t\tif (layer instanceof L.Marker) {\n\t\t\t\tthis._toggleMarkerHighlight(layer);\n\t\t\t} else {\n\t\t\t\t// reset the layer style to what is was before being selected\n\t\t\t\tlayer.setStyle(layer.options.previousOptions);\n\t\t\t\t// remove the cached options for the layer object\n\t\t\t\tdelete layer.options.previousOptions;\n\t\t\t}\n\t\t}\n\n\t\tif (layer instanceof L.Marker) {\n\t\t\tlayer.dragging.disable();\n\t\t\tlayer\n\t\t\t\t.off('dragend', this._onMarkerDragEnd, this)\n\t\t\t\t.off('touchmove', this._onTouchMove, this)\n\t\t\t\t.off('MSPointerMove', this._onTouchMove, this)\n\t\t\t\t.off('touchend', this._onMarkerDragEnd, this)\n\t\t\t\t.off('MSPointerUp', this._onMarkerDragEnd, this);\n\t\t} else {\n\t\t\tlayer.editing.disable();\n\t\t}\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tthis._tooltip.updatePosition(e.latlng);\n\t},\n\n\t_onMarkerDragEnd: function (e) {\n\t\tvar layer = e.target;\n\t\tlayer.edited = true;\n\t\tthis._map.fire(L.Draw.Event.EDITMOVE, {layer: layer});\n\t},\n\n\t_onTouchMove: function (e) {\n\t\tvar touchEvent = e.originalEvent.changedTouches[0],\n\t\t\tlayerPoint = this._map.mouseEventToLayerPoint(touchEvent),\n\t\t\tlatlng = this._map.layerPointToLatLng(layerPoint);\n\t\te.target.setLatLng(latlng);\n\t},\n\n\t_hasAvailableLayers: function () {\n\t\treturn this._featureGroup.getLayers().length !== 0;\n\t}\n});\n", "/**\n * @class L.EditToolbar.Delete\n * @aka EditToolbar.Delete\n */\nL.EditToolbar.Delete = L.Handler.extend({\n\tstatics: {\n\t\tTYPE: 'remove' // not delete as delete is reserved in js\n\t},\n\n\t// @method intialize(): void\n\tinitialize: function (map, options) {\n\t\tL.Handler.prototype.initialize.call(this, map);\n\n\t\tL.Util.setOptions(this, options);\n\n\t\t// Store the selectable layer group for ease of access\n\t\tthis._deletableLayers = this.options.featureGroup;\n\n\t\tif (!(this._deletableLayers instanceof L.FeatureGroup)) {\n\t\t\tthrow new Error('options.featureGroup must be a L.FeatureGroup');\n\t\t}\n\n\t\t// Save the type so super can fire, need to do this as cannot do this.TYPE :(\n\t\tthis.type = L.EditToolbar.Delete.TYPE;\n\n\t\tvar version = L.version.split('.');\n\t\t//If Version is >= 1.2.0\n\t\tif (parseInt(version[0], 10) === 1 && parseInt(version[1], 10) >= 2) {\n\t\t\tL.EditToolbar.Delete.include(L.Evented.prototype);\n\t\t} else {\n\t\t\tL.EditToolbar.Delete.include(L.Mixin.Events);\n\t\t}\n\n\t},\n\n\t// @method enable(): void\n\t// Enable the delete toolbar\n\tenable: function () {\n\t\tif (this._enabled || !this._hasAvailableLayers()) {\n\t\t\treturn;\n\t\t}\n\t\tthis.fire('enabled', {handler: this.type});\n\n\t\tthis._map.fire(L.Draw.Event.DELETESTART, {handler: this.type});\n\n\t\tL.Handler.prototype.enable.call(this);\n\n\t\tthis._deletableLayers\n\t\t\t.on('layeradd', this._enableLayerDelete, this)\n\t\t\t.on('layerremove', this._disableLayerDelete, this);\n\t},\n\n\t// @method disable(): void\n\t// Disable the delete toolbar\n\tdisable: function () {\n\t\tif (!this._enabled) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._deletableLayers\n\t\t\t.off('layeradd', this._enableLayerDelete, this)\n\t\t\t.off('layerremove', this._disableLayerDelete, this);\n\n\t\tL.Handler.prototype.disable.call(this);\n\n\t\tthis._map.fire(L.Draw.Event.DELETESTOP, {handler: this.type});\n\n\t\tthis.fire('disabled', {handler: this.type});\n\t},\n\n\t// @method addHooks(): void\n\t// Add listener hooks to this handler\n\taddHooks: function () {\n\t\tvar map = this._map;\n\n\t\tif (map) {\n\t\t\tmap.getContainer().focus();\n\n\t\t\tthis._deletableLayers.eachLayer(this._enableLayerDelete, this);\n\t\t\tthis._deletedLayers = new L.LayerGroup();\n\n\t\t\tthis._tooltip = new L.Draw.Tooltip(this._map);\n\t\t\tthis._tooltip.updateContent({text: L.drawLocal.edit.handlers.remove.tooltip.text});\n\n\t\t\tthis._map.on('mousemove', this._onMouseMove, this);\n\t\t}\n\t},\n\n\t// @method removeHooks(): void\n\t// Remove listener hooks from this handler\n\tremoveHooks: function () {\n\t\tif (this._map) {\n\t\t\tthis._deletableLayers.eachLayer(this._disableLayerDelete, this);\n\t\t\tthis._deletedLayers = null;\n\n\t\t\tthis._tooltip.dispose();\n\t\t\tthis._tooltip = null;\n\n\t\t\tthis._map.off('mousemove', this._onMouseMove, this);\n\t\t}\n\t},\n\n\t// @method revertLayers(): void\n\t// Revert the deleted layers back to their prior state.\n\trevertLayers: function () {\n\t\t// Iterate of the deleted layers and add them back into the featureGroup\n\t\tthis._deletedLayers.eachLayer(function (layer) {\n\t\t\tthis._deletableLayers.addLayer(layer);\n\t\t\tlayer.fire('revert-deleted', {layer: layer});\n\t\t}, this);\n\t},\n\n\t// @method save(): void\n\t// Save deleted layers\n\tsave: function () {\n\t\tthis._map.fire(L.Draw.Event.DELETED, {layers: this._deletedLayers});\n\t},\n\n\t// @method removeAllLayers(): void\n\t// Remove all delateable layers\n\tremoveAllLayers: function () {\n\t\t// Iterate of the delateable layers and add remove them\n\t\tthis._deletableLayers.eachLayer(function (layer) {\n\t\t\tthis._removeLayer({layer: layer});\n\t\t}, this);\n\t\tthis.save();\n\t},\n\n\t_enableLayerDelete: function (e) {\n\t\tvar layer = e.layer || e.target || e;\n\n\t\tlayer.on('click', this._removeLayer, this);\n\t},\n\n\t_disableLayerDelete: function (e) {\n\t\tvar layer = e.layer || e.target || e;\n\n\t\tlayer.off('click', this._removeLayer, this);\n\n\t\t// Remove from the deleted layers so we can't accidentally revert if the user presses cancel\n\t\tthis._deletedLayers.removeLayer(layer);\n\t},\n\n\t_removeLayer: function (e) {\n\t\tvar layer = e.layer || e.target || e;\n\n\t\tthis._deletableLayers.removeLayer(layer);\n\n\t\tthis._deletedLayers.addLayer(layer);\n\n\t\tlayer.fire('deleted');\n\t},\n\n\t_onMouseMove: function (e) {\n\t\tthis._tooltip.updatePosition(e.latlng);\n\t},\n\n\t_hasAvailableLayers: function () {\n\t\treturn this._deletableLayers.getLayers().length !== 0;\n\t}\n});\n"]}