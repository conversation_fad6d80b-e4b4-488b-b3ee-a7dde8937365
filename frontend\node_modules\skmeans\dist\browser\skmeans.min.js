/*! skmeans 2017-08-03 */

"use strict";!function r(t,n,o){function a(i,f){if(!n[i]){if(!t[i]){var s="function"==typeof require&&require;if(!f&&s)return s(i,!0);if(e)return e(i,!0);var u=new Error("Cannot find module '"+i+"'");throw u.code="MODULE_NOT_FOUND",u}var v=n[i]={exports:{}};t[i][0].call(v.exports,function(r){var n=t[i][1][r];return a(n||r)},v,v.exports,r,t,n,o)}return n[i].exports}for(var e="function"==typeof require&&require,i=0;i<o.length;i++)a(o[i]);return a}({1:[function(r,t,n){(function(){this.skmeans;var o=r("./main.js");void 0!==n&&(void 0!==t&&t.exports&&(n=t.exports=o),n.skmeans=o),"undefined"!=typeof window&&(window.skmeans=o)}).call(this)},{"./main.js":4}],2:[function(r,t,n){t.exports={eudist:function(r,t,n){for(var o=r.length,a=0,e=0;e<o;e++){var i=(r[e]||0)-(t[e]||0);a+=i*i}return n?Math.sqrt(a):a},mandist:function(r,t,n){for(var o=r.length,a=0,e=0;e<o;e++)a+=Math.abs((r[e]||0)-(t[e]||0));return n?Math.sqrt(a):a},dist:function(r,t,n){var o=Math.abs(r-t);return n?o:o*o}}},{}],3:[function(r,t,n){var o=r("./distance.js"),a=o.eudist,e=o.dist;t.exports={kmrand:function(r,t){for(var n={},o=[],a=t<<2,e=r.length,i=r[0].length>0;o.length<t&&a-- >0;){var f=r[Math.floor(Math.random()*e)],s=i?f.join("_"):""+f;n[s]||(n[s]=!0,o.push(f))}if(o.length<t)throw new Error("Error initializating clusters");return o},kmpp:function(r,t){var n=r[0].length?a:e,o=[],i=r.length,f=r[0].length>0,s={},u=r[Math.floor(Math.random()*i)],v=f?u.join("_"):""+u;for(o.push(u),s[v]=!0;o.length<t;){for(var h=[],c=o.length,d=0,l=[],p=0;p<i;p++){for(var m=1/0,g=0;g<c;g++){var k=n(r[p],o[g]);k<=m&&(m=k)}h[p]=m}for(var M=0;M<i;M++)d+=h[M];for(var j=0;j<i;j++)l[j]={i:j,v:r[j],pr:h[j]/d,cs:0};l.sort(function(r,t){return r.pr-t.pr}),l[0].cs=l[0].pr;for(var x=1;x<i;x++)l[x].cs=l[x-1].cs+l[x].pr;for(var w=Math.random(),q=0;q<i-1&&l[q++].cs<w;);o.push(l[q-1].v)}return o}}},{"./distance.js":2}],4:[function(r,t,n){function o(r,t,n){n=n||[];for(var o=0;o<r;o++)n[o]=t;return n}var a=r("./distance.js"),e=r("./kinit.js"),i=a.eudist,f=(a.mandist,a.dist,e.kmrand),s=e.kmpp,u=1e4;t.exports=function(r,t,n,a){var e=[],v=[],h=[],c=[],d=!1,l=a||u,p=r.length,m=r[0].length,g=m>0,k=[];if(n)e="kmrand"==n?f(r,t):"kmpp"==n?s(r,t):n;else for(var M={};e.length<t;){var j=Math.floor(Math.random()*p);M[j]||(M[j]=!0,e.push(r[j]))}do{o(t,0,k);for(var x=0;x<p;x++){for(var w=1/0,q=0,b=0;b<t;b++)(c=g?i(r[x],e[b]):Math.abs(r[x]-e[b]))<=w&&(w=c,q=b);h[x]=q,k[q]++}for(var E=[],v=[],_=0;_<t;_++)E[_]=g?o(m,0,E[_]):0,v[_]=e[_];if(g){for(var y=0;y<t;y++)e[y]=[];for(var O=0;O<p;O++)for(var D=E[h[O]],N=r[O],U=0;U<m;U++)D[U]+=N[U];d=!0;for(var z=0;z<t;z++){for(var C=e[z],F=E[z],L=v[z],T=k[z],A=0;A<m;A++)C[A]=F[A]/T||0;if(d)for(var B=0;B<m;B++)if(L[B]!=C[B]){d=!1;break}}}else{for(var G=0;G<p;G++)E[h[G]]+=r[G];for(var H=0;H<t;H++)e[H]=E[H]/k[H]||0;d=!0;for(var I=0;I<t;I++)if(v[I]!=e[I]){d=!1;break}}d=d||--l<=0}while(!d);return{it:u-l,k:t,idxs:h,centroids:e}}},{"./distance.js":2,"./kinit.js":3}]},{},[1]);