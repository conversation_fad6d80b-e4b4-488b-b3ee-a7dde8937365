/**
 * Sélecteur de couches pour OpenLayers
 * Module cartographique C2-EW
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
    Layers, 
    Eye, 
    EyeOff, 
    ChevronDown, 
    ChevronRight,
    Settings,
    Trash2
} from 'lucide-react';

/**
 * Composant de sélection et gestion des couches
 */
export const LayerSwitcher = ({
    layerManager,
    className = ''
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [layers, setLayers] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState(new Set(['base', 'vector', 'equipment']));
    
    /**
     * Mise à jour de la liste des couches
     */
    const updateLayerList = useCallback(() => {
        if (!layerManager) return;
        
        const allLayers = layerManager.getAllLayers();
        const layerGroups = {
            base: [],
            vector: [],
            equipment: [],
            analysis: [],
            other: []
        };
        
        allLayers.forEach(layer => {
            const name = layer.get('name') || 'Sans nom';
            const type = layer.get('type') || 'other';
            const visible = layer.getVisible();
            const opacity = layer.getOpacity();
            const zIndex = layer.getZIndex() || 0;
            
            const layerInfo = {
                name,
                type,
                visible,
                opacity,
                zIndex,
                layer
            };
            
            // Grouper les couches
            if (name.includes('equipment_')) {
                layerGroups.equipment.push(layerInfo);
            } else if (name === 'morocco_territory' || name.includes('territory')) {
                layerGroups.vector.push(layerInfo);
            } else if (name.includes('visibility') || name.includes('measurement')) {
                layerGroups.analysis.push(layerInfo);
            } else if (type === 'base' || name.includes('OSM') || name.includes('satellite')) {
                layerGroups.base.push(layerInfo);
            } else {
                layerGroups.other.push(layerInfo);
            }
        });
        
        // Trier par zIndex
        Object.keys(layerGroups).forEach(group => {
            layerGroups[group].sort((a, b) => b.zIndex - a.zIndex);
        });
        
        setLayers(layerGroups);
    }, [layerManager]);
    
    /**
     * Écouter les changements de couches
     */
    useEffect(() => {
        if (!layerManager) return;
        
        updateLayerList();
        
        const handleLayerChange = () => {
            updateLayerList();
        };
        
        layerManager.on('layerAdded', handleLayerChange);
        layerManager.on('layerRemoved', handleLayerChange);
        layerManager.on('layerVisibilityChanged', handleLayerChange);
        layerManager.on('layerOpacityChanged', handleLayerChange);
        
        return () => {
            layerManager.off('layerAdded', handleLayerChange);
            layerManager.off('layerRemoved', handleLayerChange);
            layerManager.off('layerVisibilityChanged', handleLayerChange);
            layerManager.off('layerOpacityChanged', handleLayerChange);
        };
    }, [layerManager, updateLayerList]);
    
    /**
     * Basculer la visibilité d'une couche
     */
    const toggleLayerVisibility = useCallback((layerName) => {
        if (layerManager) {
            const layer = layerManager.getLayer(layerName);
            if (layer) {
                const newVisibility = !layer.getVisible();
                layerManager.setLayerVisible(layerName, newVisibility);
            }
        }
    }, [layerManager]);
    
    /**
     * Changer l'opacité d'une couche
     */
    const changeLayerOpacity = useCallback((layerName, opacity) => {
        if (layerManager) {
            layerManager.setLayerOpacity(layerName, opacity);
        }
    }, [layerManager]);
    
    /**
     * Supprimer une couche
     */
    const removeLayer = useCallback((layerName) => {
        if (layerManager && window.confirm(`Supprimer la couche "${layerName}" ?`)) {
            layerManager.removeLayer(layerName);
        }
    }, [layerManager]);
    
    /**
     * Basculer l'expansion d'un groupe
     */
    const toggleGroupExpansion = useCallback((groupName) => {
        setExpandedGroups(prev => {
            const newSet = new Set(prev);
            if (newSet.has(groupName)) {
                newSet.delete(groupName);
            } else {
                newSet.add(groupName);
            }
            return newSet;
        });
    }, []);
    
    /**
     * Obtenir l'icône pour un type d'équipement
     */
    const getEquipmentIcon = useCallback((layerName) => {
        if (layerName.includes('comint')) return '📡';
        if (layerName.includes('elint')) return '📊';
        if (layerName.includes('anti-drone')) return '🛡️';
        if (layerName.includes('jammer')) return '📵';
        if (layerName.includes('sensor')) return '🔍';
        return '📍';
    }, []);
    
    /**
     * Rendu d'un groupe de couches
     */
    const renderLayerGroup = useCallback((groupName, groupLayers, title) => {
        if (groupLayers.length === 0) return null;
        
        const isExpanded = expandedGroups.has(groupName);
        
        return (
            <div key={groupName} className="mb-3">
                <button
                    onClick={() => toggleGroupExpansion(groupName)}
                    className="flex items-center w-full text-left p-2 hover:bg-gray-50 rounded"
                >
                    {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                    <span className="ml-2 font-medium text-sm">{title}</span>
                    <span className="ml-auto text-xs text-gray-500">({groupLayers.length})</span>
                </button>
                
                {isExpanded && (
                    <div className="ml-4 space-y-1">
                        {groupLayers.map(layerInfo => (
                            <div key={layerInfo.name} className="flex items-center p-2 hover:bg-gray-50 rounded">
                                <button
                                    onClick={() => toggleLayerVisibility(layerInfo.name)}
                                    className="flex-shrink-0 mr-2"
                                    title={layerInfo.visible ? 'Masquer' : 'Afficher'}
                                >
                                    {layerInfo.visible ? 
                                        <Eye size={16} className="text-blue-600" /> : 
                                        <EyeOff size={16} className="text-gray-400" />
                                    }
                                </button>
                                
                                <div className="flex-grow min-w-0">
                                    <div className="flex items-center">
                                        {groupName === 'equipment' && (
                                            <span className="mr-2 text-sm">
                                                {getEquipmentIcon(layerInfo.name)}
                                            </span>
                                        )}
                                        <span className="text-sm truncate">
                                            {layerInfo.name.replace('equipment_', '').replace('_', ' ')}
                                        </span>
                                    </div>
                                    
                                    {/* Contrôle d'opacité */}
                                    <div className="mt-1">
                                        <input
                                            type="range"
                                            min="0"
                                            max="1"
                                            step="0.1"
                                            value={layerInfo.opacity}
                                            onChange={(e) => changeLayerOpacity(layerInfo.name, parseFloat(e.target.value))}
                                            className="w-full h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                                            title={`Opacité: ${Math.round(layerInfo.opacity * 100)}%`}
                                        />
                                    </div>
                                </div>
                                
                                {/* Actions */}
                                <div className="flex-shrink-0 ml-2 flex space-x-1">
                                    <button
                                        onClick={() => removeLayer(layerInfo.name)}
                                        className="p-1 hover:bg-red-100 rounded"
                                        title="Supprimer la couche"
                                    >
                                        <Trash2 size={14} className="text-red-600" />
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    }, [expandedGroups, toggleGroupExpansion, toggleLayerVisibility, changeLayerOpacity, removeLayer, getEquipmentIcon]);
    
    if (!layerManager) {
        return null;
    }
    
    return (
        <div className={`relative ${className}`}>
            {/* Bouton d'ouverture */}
            <button
                onClick={() => setIsOpen(!isOpen)}
                className={`bg-white rounded-lg shadow-lg p-3 hover:bg-gray-50 transition-colors ${
                    isOpen ? 'bg-blue-50 text-blue-600' : ''
                }`}
                title="Gestionnaire de couches"
            >
                <Layers size={20} />
            </button>
            
            {/* Panel des couches */}
            {isOpen && (
                <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border max-h-96 overflow-y-auto">
                    <div className="p-4">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold">Gestionnaire de couches</h3>
                            <button
                                onClick={() => setIsOpen(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                ×
                            </button>
                        </div>
                        
                        <div className="space-y-2">
                            {renderLayerGroup('base', layers.base || [], 'Cartes de base')}
                            {renderLayerGroup('vector', layers.vector || [], 'Couches vectorielles')}
                            {renderLayerGroup('equipment', layers.equipment || [], 'Équipements')}
                            {renderLayerGroup('analysis', layers.analysis || [], 'Analyses')}
                            {renderLayerGroup('other', layers.other || [], 'Autres')}
                        </div>
                        
                        {Object.values(layers).every(group => group.length === 0) && (
                            <div className="text-center text-gray-500 py-8">
                                <Layers size={48} className="mx-auto mb-2 opacity-50" />
                                <p>Aucune couche disponible</p>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default LayerSwitcher;
