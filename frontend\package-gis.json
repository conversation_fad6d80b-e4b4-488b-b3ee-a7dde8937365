{
  "name": "c2ew-gis-dependencies",
  "description": "Dépendances SIG pour le module cartographique C2-EW",
  "dependencies": {
    "ol": "^8.2.0",
    "ol-ext": "^4.0.13",
    "ol-layerswitcher": "^4.1.1",
    "ol-geocoder": "^4.3.2",
    "proj4": "^2.9.2",
    "geojson": "^0.5.0",
    "turf": "^3.0.14",
    "@turf/turf": "^6.5.0",
    "sql.js": "^1.8.0",
    "better-sqlite3": "^9.2.2",
    "spatialite": "^0.1.0",
    "mbtiles": "^0.12.1",
    "pbf": "^3.2.1",
    "vector-tile": "^1.3.1",
    "mapbox-gl": "^2.15.0",
    "d3": "^7.8.5",
    "d3-geo": "^3.1.0",
    "topojson": "^3.0.2",
    "web-worker": "^1.2.0",
    "comlink": "^4.4.1",
    "idb": "^7.1.1",
    "localforage": "^1.10.0",
    "dexie": "^3.2.4",
    "rxjs": "^7.8.1",
    "lodash": "^4.17.21",
    "uuid": "^9.0.1",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "@types/geojson": "^7946.0.13",
    "@types/d3": "^7.4.3",
    "@types/d3-geo": "^3.1.0",
    "@types/topojson": "^3.2.6",
    "@types/uuid": "^9.0.7",
    "@types/lodash": "^4.14.202",
    "jest": "^29.7.0",
    "@testing-library/jest-dom": "^6.1.5",
    "cypress": "^13.6.2"
  },
  "peerDependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not dead",
    "not ie 11"
  ],
  "notes": {
    "ol": "OpenLayers 8+ - Framework cartographique principal",
    "ol-ext": "Extensions OpenLayers pour fonctionnalités avancées",
    "ol-layerswitcher": "Gestionnaire de couches",
    "ol-geocoder": "Géocodage et recherche",
    "proj4": "Projections cartographiques",
    "turf": "Calculs géospatiaux côté client",
    "sql.js": "SQLite en WebAssembly pour le navigateur",
    "better-sqlite3": "SQLite natif pour Node.js",
    "spatialite": "Extension spatiale pour SQLite",
    "mbtiles": "Support format MBTiles",
    "pbf": "Protocol Buffers pour tuiles vectorielles",
    "vector-tile": "Décodage tuiles vectorielles",
    "mapbox-gl": "Rendu vectoriel haute performance",
    "d3": "Visualisation de données géospatiales",
    "web-worker": "Traitement en arrière-plan",
    "comlink": "Communication avec Web Workers",
    "idb": "IndexedDB pour stockage local",
    "localforage": "Stockage local unifié",
    "dexie": "Base de données IndexedDB",
    "rxjs": "Programmation réactive pour événements"
  },
  "installation": {
    "commands": [
      "npm install ol ol-ext ol-layerswitcher ol-geocoder",
      "npm install proj4 geojson @turf/turf",
      "npm install sql.js better-sqlite3 spatialite",
      "npm install mbtiles pbf vector-tile",
      "npm install d3 d3-geo topojson",
      "npm install web-worker comlink",
      "npm install idb localforage dexie",
      "npm install rxjs lodash uuid date-fns"
    ],
    "systemDependencies": {
      "linux": [
        "sudo apt-get install libsqlite3-dev",
        "sudo apt-get install spatialite-bin libspatialite7"
      ],
      "macos": [
        "brew install sqlite3",
        "brew install spatialite-tools"
      ],
      "windows": [
        "Installer SQLite depuis https://sqlite.org/download.html",
        "Installer SpatiaLite depuis https://www.gaia-gis.it/fossil/libspatialite"
      ]
    }
  },
  "configuration": {
    "webpack": {
      "resolve": {
        "fallback": {
          "fs": false,
          "path": require.resolve("path-browserify"),
          "crypto": require.resolve("crypto-browserify"),
          "stream": require.resolve("stream-browserify"),
          "buffer": require.resolve("buffer")
        }
      },
      "plugins": [
        "new webpack.ProvidePlugin({ Buffer: ['buffer', 'Buffer'] })"
      ]
    },
    "vite": {
      "define": {
        "global": "globalThis"
      },
      "optimizeDeps": {
        "include": ["ol", "ol-ext", "@turf/turf", "d3", "sql.js"]
      }
    }
  },
  "performance": {
    "bundleSize": {
      "ol": "~1.2MB (minified)",
      "turf": "~500KB (tree-shaking recommandé)",
      "d3": "~300KB (modules sélectifs)",
      "sql.js": "~1.5MB (WebAssembly)",
      "total": "~3.5MB (optimisé)"
    },
    "optimization": [
      "Utiliser le tree-shaking pour Turf.js",
      "Charger SQL.js de manière asynchrone",
      "Utiliser les Web Workers pour les calculs lourds",
      "Implémenter le lazy loading des couches",
      "Optimiser les bundles avec code splitting"
    ]
  }
}
