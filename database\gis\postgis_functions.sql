-- ===================================
-- Fonctions PostGIS Spécialisées
-- Module SIG C2-EW - Calculs Géospatiaux Avancés
-- ===================================

-- ===================================
-- Fonctions de Validation Territoriale
-- ===================================

-- Fonction pour valider l'intégrité territoriale du Maroc
CREATE OR REPLACE FUNCTION validate_morocco_territory(
    input_geometry GEOMETRY
) RETURNS BOOLEAN AS $$
DECLARE
    official_bounds GEOMETRY;
    is_within BOOLEAN;
BEGIN
    -- Récupérer les limites officielles du Royaume du Maroc
    SELECT ST_Union(geometry) INTO official_bounds
    FROM morocco_territory 
    WHERE region_type IN ('mainland', 'sahara') 
    AND is_official = TRUE;
    
    -- Vérifier si la géométrie est dans les limites officielles
    SELECT ST_Within(input_geometry, official_bounds) INTO is_within;
    
    RETURN is_within;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction pour obtenir la région administrative d'un point
CREATE OR REPLACE FUNCTION get_morocco_region(
    input_point GEOMETRY
) RETURNS TABLE(
    region_id UUID,
    region_name VARCHAR,
    region_name_ar VARCHAR,
    region_type VARCHAR,
    administrative_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mt.id,
        mt.region_name,
        mt.region_name_ar,
        mt.region_type,
        mt.administrative_level
    FROM morocco_territory mt
    WHERE ST_Contains(mt.geometry, input_point)
    AND mt.is_official = TRUE
    ORDER BY mt.administrative_level DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql STABLE;

-- ===================================
-- Fonctions d'Analyse de Visibilité
-- ===================================

-- Fonction principale d'analyse de visibilité
CREATE OR REPLACE FUNCTION calculate_visibility(
    observer_point GEOMETRY,
    observer_height FLOAT DEFAULT 1.75,
    max_distance FLOAT DEFAULT 10000.0,
    target_height FLOAT DEFAULT 0.0,
    grid_resolution FLOAT DEFAULT 100.0,
    earth_curvature BOOLEAN DEFAULT TRUE
) RETURNS GEOMETRY AS $$
DECLARE
    visible_area GEOMETRY;
    dem_raster RASTER;
    analysis_bounds GEOMETRY;
BEGIN
    -- Créer la zone d'analyse (cercle autour de l'observateur)
    analysis_bounds := ST_Buffer(observer_point::GEOGRAPHY, max_distance)::GEOMETRY;
    
    -- TODO: Intégrer avec un MNT réel
    -- Pour l'instant, simulation d'une analyse de visibilité basique
    
    -- Créer une grille de points pour l'analyse
    WITH grid_points AS (
        SELECT (ST_PixelAsCentroids(
            ST_AsRaster(analysis_bounds, grid_resolution, grid_resolution)
        )).geom AS point
    ),
    visibility_points AS (
        SELECT 
            gp.point,
            CASE 
                WHEN ST_Distance(observer_point::GEOGRAPHY, gp.point::GEOGRAPHY) <= max_distance
                THEN calculate_line_of_sight(observer_point, gp.point, observer_height, target_height, earth_curvature)
                ELSE FALSE
            END AS is_visible
        FROM grid_points gp
    )
    SELECT ST_Union(
        ST_Buffer(vp.point::GEOGRAPHY, grid_resolution/2)::GEOMETRY
    ) INTO visible_area
    FROM visibility_points vp
    WHERE vp.is_visible = TRUE;
    
    RETURN visible_area;
END;
$$ LANGUAGE plpgsql STABLE;

-- Fonction de calcul de ligne de vue
CREATE OR REPLACE FUNCTION calculate_line_of_sight(
    observer_point GEOMETRY,
    target_point GEOMETRY,
    observer_height FLOAT DEFAULT 1.75,
    target_height FLOAT DEFAULT 0.0,
    earth_curvature BOOLEAN DEFAULT TRUE
) RETURNS BOOLEAN AS $$
DECLARE
    distance_m FLOAT;
    earth_curvature_correction FLOAT := 0;
    line_geometry GEOMETRY;
    elevation_profile FLOAT[];
    is_visible BOOLEAN := TRUE;
BEGIN
    -- Calculer la distance
    distance_m := ST_Distance(observer_point::GEOGRAPHY, target_point::GEOGRAPHY);
    
    -- Correction de courbure terrestre si activée
    IF earth_curvature THEN
        -- Formule: h = d²/(2*R) où R = 6371000m (rayon terrestre)
        earth_curvature_correction := (distance_m * distance_m) / (2 * 6371000);
    END IF;
    
    -- Créer la ligne entre observateur et cible
    line_geometry := ST_MakeLine(observer_point, target_point);
    
    -- TODO: Analyser le profil d'élévation le long de la ligne
    -- Pour l'instant, simulation basique basée sur la distance
    
    -- Règle simple: visible si distance < seuil et pas d'obstacles majeurs
    IF distance_m > 50000 THEN -- > 50km
        is_visible := FALSE;
    ELSIF distance_m > 20000 THEN -- 20-50km
        is_visible := (random() > 0.3); -- 70% de chance d'être visible
    ELSIF distance_m > 5000 THEN -- 5-20km
        is_visible := (random() > 0.1); -- 90% de chance d'être visible
    END IF;
    
    RETURN is_visible;
END;
$$ LANGUAGE plpgsql STABLE;

-- ===================================
-- Fonctions de Calculs Géométriques
-- ===================================

-- Fonction de calcul de distance géodésique précise
CREATE OR REPLACE FUNCTION calculate_precise_distance(
    point1 GEOMETRY,
    point2 GEOMETRY,
    unit TEXT DEFAULT 'meters'
) RETURNS FLOAT AS $$
DECLARE
    distance_m FLOAT;
    result FLOAT;
BEGIN
    -- Calcul de distance géodésique (prend en compte la courbure terrestre)
    distance_m := ST_Distance(point1::GEOGRAPHY, point2::GEOGRAPHY);
    
    -- Conversion d'unité
    CASE unit
        WHEN 'meters', 'm' THEN result := distance_m;
        WHEN 'kilometers', 'km' THEN result := distance_m / 1000.0;
        WHEN 'miles', 'mi' THEN result := distance_m / 1609.344;
        WHEN 'nautical_miles', 'nm' THEN result := distance_m / 1852.0;
        ELSE result := distance_m; -- défaut: mètres
    END CASE;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de calcul d'azimut
CREATE OR REPLACE FUNCTION calculate_azimuth(
    point1 GEOMETRY,
    point2 GEOMETRY,
    unit TEXT DEFAULT 'degrees'
) RETURNS FLOAT AS $$
DECLARE
    azimuth_rad FLOAT;
    result FLOAT;
BEGIN
    -- Calcul d'azimut en radians
    azimuth_rad := ST_Azimuth(point1, point2);
    
    -- Conversion d'unité
    CASE unit
        WHEN 'degrees', 'deg' THEN result := degrees(azimuth_rad);
        WHEN 'radians', 'rad' THEN result := azimuth_rad;
        WHEN 'mils' THEN result := azimuth_rad * 3200 / PI(); -- Mils militaires
        ELSE result := degrees(azimuth_rad); -- défaut: degrés
    END CASE;
    
    -- Normaliser entre 0 et 360 degrés (ou équivalent)
    IF unit IN ('degrees', 'deg') THEN
        result := CASE WHEN result < 0 THEN result + 360 ELSE result END;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de calcul de surface précise
CREATE OR REPLACE FUNCTION calculate_precise_area(
    polygon_geom GEOMETRY,
    unit TEXT DEFAULT 'square_meters'
) RETURNS FLOAT AS $$
DECLARE
    area_m2 FLOAT;
    result FLOAT;
BEGIN
    -- Calcul de surface géodésique
    area_m2 := ST_Area(polygon_geom::GEOGRAPHY);
    
    -- Conversion d'unité
    CASE unit
        WHEN 'square_meters', 'm2' THEN result := area_m2;
        WHEN 'square_kilometers', 'km2' THEN result := area_m2 / 1000000.0;
        WHEN 'hectares', 'ha' THEN result := area_m2 / 10000.0;
        WHEN 'acres' THEN result := area_m2 / 4046.856;
        ELSE result := area_m2; -- défaut: mètres carrés
    END CASE;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ===================================
-- Fonctions de Requêtes Spatiales
-- ===================================

-- Fonction de recherche spatiale optimisée
CREATE OR REPLACE FUNCTION spatial_search(
    search_geometry GEOMETRY,
    search_radius FLOAT DEFAULT 1000.0,
    layer_types TEXT[] DEFAULT ARRAY['vector'],
    feature_types TEXT[] DEFAULT NULL
) RETURNS TABLE(
    feature_id UUID,
    layer_name VARCHAR,
    feature_name VARCHAR,
    feature_type VARCHAR,
    distance_m FLOAT,
    geometry GEOMETRY
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sf.id,
        sl.name,
        sf.name,
        sf.feature_type,
        ST_Distance(search_geometry::GEOGRAPHY, sf.geometry::GEOGRAPHY) AS distance_m,
        sf.geometry
    FROM spatial_features sf
    JOIN spatial_layers sl ON sf.layer_id = sl.id
    WHERE 
        sl.layer_type = ANY(layer_types)
        AND sl.visible = TRUE
        AND (feature_types IS NULL OR sf.feature_type = ANY(feature_types))
        AND ST_DWithin(
            search_geometry::GEOGRAPHY, 
            sf.geometry::GEOGRAPHY, 
            search_radius
        )
    ORDER BY distance_m ASC;
END;
$$ LANGUAGE plpgsql STABLE;

-- Fonction de recherche dans le territoire marocain
CREATE OR REPLACE FUNCTION search_morocco_territory(
    search_text TEXT,
    region_type TEXT DEFAULT NULL,
    admin_level INTEGER DEFAULT NULL
) RETURNS TABLE(
    territory_id UUID,
    region_name VARCHAR,
    region_name_ar VARCHAR,
    region_type VARCHAR,
    administrative_level INTEGER,
    similarity_score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mt.id,
        mt.region_name,
        mt.region_name_ar,
        mt.region_type,
        mt.administrative_level,
        GREATEST(
            similarity(mt.region_name, search_text),
            COALESCE(similarity(mt.region_name_ar, search_text), 0)
        ) AS similarity_score
    FROM morocco_territory mt
    WHERE 
        mt.is_official = TRUE
        AND (region_type IS NULL OR mt.region_type = region_type)
        AND (admin_level IS NULL OR mt.administrative_level = admin_level)
        AND (
            mt.region_name ILIKE '%' || search_text || '%'
            OR mt.region_name_ar ILIKE '%' || search_text || '%'
            OR similarity(mt.region_name, search_text) > 0.3
            OR similarity(mt.region_name_ar, search_text) > 0.3
        )
    ORDER BY similarity_score DESC, mt.administrative_level ASC;
END;
$$ LANGUAGE plpgsql STABLE;

-- ===================================
-- Fonctions d'Optimisation
-- ===================================

-- Fonction de simplification géométrique adaptative
CREATE OR REPLACE FUNCTION adaptive_simplify(
    input_geometry GEOMETRY,
    target_zoom INTEGER DEFAULT 10
) RETURNS GEOMETRY AS $$
DECLARE
    tolerance FLOAT;
    simplified_geom GEOMETRY;
BEGIN
    -- Calculer la tolerance basée sur le niveau de zoom
    tolerance := CASE 
        WHEN target_zoom >= 15 THEN 0.1      -- Très détaillé
        WHEN target_zoom >= 12 THEN 1.0      -- Détaillé
        WHEN target_zoom >= 9 THEN 10.0      -- Moyen
        WHEN target_zoom >= 6 THEN 100.0     -- Grossier
        ELSE 1000.0                          -- Très grossier
    END;
    
    -- Simplifier la géométrie
    simplified_geom := ST_Simplify(input_geometry, tolerance);
    
    -- Vérifier que la géométrie reste valide
    IF NOT ST_IsValid(simplified_geom) THEN
        simplified_geom := ST_MakeValid(simplified_geom);
    END IF;
    
    RETURN simplified_geom;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de clustering spatial pour optimisation d'affichage
CREATE OR REPLACE FUNCTION spatial_cluster(
    layer_id UUID,
    cluster_distance FLOAT DEFAULT 1000.0,
    min_zoom INTEGER DEFAULT 1,
    max_zoom INTEGER DEFAULT 10
) RETURNS TABLE(
    cluster_id INTEGER,
    cluster_center GEOMETRY,
    feature_count INTEGER,
    zoom_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH clustered_features AS (
        SELECT 
            sf.geometry,
            ST_ClusterKMeans(sf.geometry, 
                CASE 
                    WHEN COUNT(*) OVER() > 1000 THEN 50
                    WHEN COUNT(*) OVER() > 100 THEN 20
                    ELSE 10
                END
            ) OVER() AS cluster_id
        FROM spatial_features sf
        WHERE sf.layer_id = spatial_cluster.layer_id
    )
    SELECT 
        cf.cluster_id::INTEGER,
        ST_Centroid(ST_Union(cf.geometry)) AS cluster_center,
        COUNT(*)::INTEGER AS feature_count,
        generate_series(min_zoom, max_zoom) AS zoom_level
    FROM clustered_features cf
    GROUP BY cf.cluster_id;
END;
$$ LANGUAGE plpgsql STABLE;

-- ===================================
-- Fonctions de Maintenance
-- ===================================

-- Fonction de nettoyage et optimisation des géométries
CREATE OR REPLACE FUNCTION cleanup_geometries(
    table_name TEXT,
    geometry_column TEXT DEFAULT 'geometry'
) RETURNS INTEGER AS $$
DECLARE
    fixed_count INTEGER := 0;
    sql_query TEXT;
BEGIN
    -- Construire la requête de nettoyage
    sql_query := format('
        UPDATE %I 
        SET %I = ST_MakeValid(%I)
        WHERE NOT ST_IsValid(%I)',
        table_name, geometry_column, geometry_column, geometry_column
    );
    
    -- Exécuter le nettoyage
    EXECUTE sql_query;
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    
    -- Mettre à jour les statistiques spatiales
    EXECUTE format('ANALYZE %I', table_name);
    
    RETURN fixed_count;
END;
$$ LANGUAGE plpgsql;

-- Fonction de mise à jour des statistiques spatiales
CREATE OR REPLACE FUNCTION update_spatial_statistics() RETURNS VOID AS $$
BEGIN
    -- Mettre à jour les statistiques pour toutes les tables spatiales
    ANALYZE spatial_layers;
    ANALYZE spatial_features;
    ANALYZE morocco_territory;
    ANALYZE mbtiles_datasets;
    ANALYZE visibility_analyses;
    ANALYZE spatial_measurements;
    
    -- Recalculer les index spatiaux si nécessaire
    REINDEX INDEX CONCURRENTLY idx_spatial_feature_geometry;
    REINDEX INDEX CONCURRENTLY idx_morocco_territory_geometry;
    
    RAISE NOTICE 'Statistiques spatiales mises à jour avec succès';
END;
$$ LANGUAGE plpgsql;
