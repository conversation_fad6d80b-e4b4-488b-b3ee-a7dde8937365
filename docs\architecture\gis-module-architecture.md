# 🗺️ Architecture du Module SIG C2-EW

## Vue d'ensemble

Le module cartographique SIG de la plateforme C2-EW est conçu comme un système robuste et performant fonctionnant entièrement hors ligne, intégré harmonieusement dans l'architecture applicative globale.

## 🏗️ Architecture Technique

### Stack Technologique

#### Frontend SIG
- **Framework cartographique**: OpenLayers 8+
- **Format de données**: MBTiles (SQLite) + GeoJSON
- **Projections**: Web Mercator, UTM, projections personnalisées
- **Performance**: Virtualisation des couches + Web Workers
- **Interface**: Menu contextuel + contrôles avancés

#### Backend Géospatial
- **API**: FastAPI avec endpoints géospatiaux async
- **ORM**: SQLAlchemy + GeoAlchemy2
- **Calculs**: PostGIS + Shapely + GDAL/OGR
- **Cache**: Redis pour requêtes spatiales fréquentes
- **Synchronisation**: WebSocket temps réel

#### Base de Données Hybride
- **Serveur**: PostgreSQL 15+ avec PostGIS 3.3+
- **Cache local**: SQLite avec SpatiaLite
- **Synchronisation**: Bidirectionnelle intelligente
- **Index**: Spatial GIST + R-tree optimisés

## 🎯 Architecture des Composants

### 1. Couche de Présentation (Frontend)

```
frontend/src/components/gis/
├── core/
│   ├── GISEngine.js              # Moteur OpenLayers principal
│   ├── LayerManager.js           # Gestionnaire de couches
│   ├── ProjectionManager.js      # Gestion des projections
│   └── PerformanceManager.js     # Optimisation performance
├── controls/
│   ├── NavigationControls.jsx    # Zoom, pan, rotation
│   ├── MeasurementTools.jsx      # Outils de mesure
│   ├── SelectionTools.jsx        # Sélection géométrique
│   └── ContextMenu.jsx           # Menu contextuel
├── analysis/
│   ├── VisibilityAnalysis.jsx    # Analyse de visibilité
│   ├── GeometricCalculations.js  # Calculs géométriques
│   └── SpatialQueries.js         # Requêtes spatiales
└── layers/
    ├── MBTilesLayer.js           # Couche MBTiles
    ├── VectorLayer.js            # Couches vectorielles
    └── RasterLayer.js            # Couches raster
```

### 2. Couche API (Backend)

```
backend/app/gis/
├── routers/
│   ├── spatial.py                # Endpoints spatiaux
│   ├── visibility.py             # Analyse de visibilité
│   ├── tiles.py                  # Gestion des tuiles
│   └── sync.py                   # Synchronisation
├── services/
│   ├── spatial_service.py        # Logique spatiale
│   ├── visibility_service.py     # Calculs de visibilité
│   ├── tile_service.py           # Service de tuiles
│   └── sync_service.py           # Service de sync
├── models/
│   ├── spatial_models.py         # Modèles géospatiaux
│   ├── tile_models.py            # Modèles de tuiles
│   └── cache_models.py           # Modèles de cache
└── utils/
    ├── spatial_utils.py          # Utilitaires spatiaux
    ├── projection_utils.py       # Utilitaires de projection
    └── performance_utils.py      # Optimisation
```

### 3. Couche de Données

```
database/
├── postgis/
│   ├── spatial_schema.sql        # Schéma spatial PostGIS
│   ├── indexes.sql               # Index spatiaux
│   └── functions.sql             # Fonctions PostGIS
├── sqlite/
│   ├── cache_schema.sql          # Schéma cache SQLite
│   ├── spatialite_init.sql       # Initialisation SpatiaLite
│   └── sync_triggers.sql         # Triggers de synchronisation
└── migrations/
    ├── 001_spatial_tables.py     # Migration tables spatiales
    ├── 002_visibility_tables.py  # Tables analyse visibilité
    └── 003_cache_tables.py       # Tables de cache
```

## 🔄 Architecture de Synchronisation

### Stratégie Hybride PostGIS/SQLite

1. **PostGIS (Source de vérité)**
   - Données géospatiales complètes
   - Calculs spatiaux complexes
   - Gestion des utilisateurs multiples
   - Sauvegarde et versioning

2. **SQLite/SpatiaLite (Cache local)**
   - Données fréquemment utilisées
   - Fonctionnement hors ligne
   - Performance optimisée
   - Synchronisation intelligente

3. **Mécanisme de Synchronisation**
   - Synchronisation par zones géographiques
   - Détection de conflits automatique
   - Résolution de conflits configurable
   - Synchronisation incrémentale

## 🚀 Fonctionnalités SIG Avancées

### Analyse de Visibilité
- Algorithme de ligne de vue (Line of Sight)
- Prise en compte du MNT haute résolution
- Calcul des zones visibles/masquées
- Optimisation avec Web Workers

### Calculs Géométriques
- Distances géodésiques précises
- Calculs de surfaces
- Azimuts et relèvements
- Intersections géométriques

### Outils de Mesure
- Mesure de distances
- Mesure de surfaces
- Profils altimétriques
- Calculs de volumes

## 🎛️ Interface Utilisateur

### Menu Contextuel
- Navigation (Zoom, Pan, Rotation)
- Sélection (Rectangle, Polygone, Cercle)
- Mesure (Distance, Surface, Profil)
- Analyse (Visibilité, Intersection)
- Couches (Affichage, Transparence, Style)

### Contrôles Avancés
- Gestionnaire de couches hiérarchique
- Sélecteur de projections
- Outils de symbologie
- Export de cartes et données

## 📊 Performance et Optimisation

### Stratégies de Performance
- Virtualisation des couches volumineuses
- Cache adaptatif intelligent
- Indexation spatiale R-tree
- Traitement asynchrone en Web Workers

### Gestion Mémoire
- Libération automatique des ressources
- Pool de connexions optimisé
- Cache LRU pour les tuiles
- Compression des données

## 🔒 Sécurité et Intégration

### Sécurité
- Authentification JWT intégrée
- Contrôle d'accès par couches
- Chiffrement des données sensibles
- Audit des actions spatiales

### Intégration Application
- API modulaire standardisée
- État partagé avec Zustand
- Communication par événements
- Thématique UI cohérente

## 🧪 Tests et Validation

### Tests Unitaires
- Tests des calculs géométriques
- Tests des algorithmes de visibilité
- Tests de synchronisation
- Tests de performance

### Tests d'Intégration
- Tests de l'API géospatiale
- Tests de synchronisation PostGIS/SQLite
- Tests de l'interface utilisateur
- Tests de charge et stress

## 📈 Métriques et Monitoring

### Métriques Performance
- Temps de réponse des requêtes spatiales
- Utilisation mémoire des couches
- Taux de cache hit/miss
- Performance de synchronisation

### Monitoring
- Surveillance des connexions PostGIS
- Monitoring de l'espace disque SQLite
- Alertes de performance
- Logs d'audit géospatial
