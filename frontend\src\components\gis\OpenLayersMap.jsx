/**
 * Composant React OpenLayers pour le module SIG C2-EW
 * Remplacement de React-Leaflet par OpenLayers 8+
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { GISEngine } from './core/GISEngine';
import { LayerManager } from './core/LayerManager';
import { ProjectionManager } from './core/ProjectionManager';
import { PerformanceManager } from './core/PerformanceManager';
import { MapControls } from './controls/MapControls';
import { LayerSwitcher } from './controls/LayerSwitcher';
import { MeasurementTools } from './tools/MeasurementTools';
import { ContextMenu } from './controls/ContextMenu';
import { useMapStore } from '../../store/mapStore';
import { useEquipmentStore } from '../../store/equipmentStore';

/**
 * Composant principal de la carte OpenLayers
 */
export const OpenLayersMap = ({
    className = '',
    height = '100%',
    width = '100%',
    enableOffline = true,
    enableControls = true,
    enableMeasurement = true,
    onMapReady = null,
    ...props
}) => {
    const mapRef = useRef(null);
    const [gisEngine, setGisEngine] = useState(null);
    const [layerManager, setLayerManager] = useState(null);
    const [projectionManager, setProjectionManager] = useState(null);
    const [performanceManager, setPerformanceManager] = useState(null);
    const [isInitialized, setIsInitialized] = useState(false);
    const [contextMenu, setContextMenu] = useState(null);
    
    // Stores Zustand
    const {
        center,
        zoom,
        projection,
        offlineMode,
        selectedBaseLayer,
        visibleLayers,
        setMapInstance,
        updateView,
        setOfflineMode: setStoreOfflineMode
    } = useMapStore();
    
    const { equipment, selectedEquipment } = useEquipmentStore();
    
    /**
     * Initialisation de la carte
     */
    useEffect(() => {
        if (!mapRef.current) return;
        
        const initializeMap = async () => {
            try {
                // Créer le moteur SIG
                const engine = new GISEngine({
                    target: mapRef.current,
                    center: center || [-7.6, 33.6], // Casablanca par défaut
                    zoom: zoom || 6,
                    projection: projection || 'EPSG:3857',
                    enableOffline: enableOffline
                });
                
                // Attendre l'initialisation
                await new Promise((resolve) => {
                    engine.once('initialized', resolve);
                });
                
                // Créer les gestionnaires
                const layerMgr = new LayerManager(engine);
                const projMgr = new ProjectionManager(engine);
                const perfMgr = new PerformanceManager(engine);
                
                // Configurer les gestionnaires d'événements
                setupEventHandlers(engine, layerMgr, projMgr, perfMgr);
                
                // Créer les couches par défaut
                await createDefaultLayers(layerMgr);
                
                // Mettre à jour les états
                setGisEngine(engine);
                setLayerManager(layerMgr);
                setProjectionManager(projMgr);
                setPerformanceManager(perfMgr);
                setIsInitialized(true);
                
                // Mettre à jour le store
                setMapInstance(engine);
                
                // Callback de prêt
                if (onMapReady) {
                    onMapReady({
                        engine,
                        layerManager: layerMgr,
                        projectionManager: projMgr,
                        performanceManager: perfMgr
                    });
                }
                
                console.log('✅ Carte OpenLayers initialisée avec succès');
                
            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation de la carte:', error);
            }
        };
        
        initializeMap();
        
        // Nettoyage
        return () => {
            if (gisEngine) {
                gisEngine.destroy();
            }
            if (layerManager) {
                layerManager.destroy();
            }
            if (projectionManager) {
                projectionManager.destroy();
            }
            if (performanceManager) {
                performanceManager.destroy();
            }
        };
    }, []);
    
    /**
     * Configuration des gestionnaires d'événements
     */
    const setupEventHandlers = useCallback((engine, layerMgr, projMgr, perfMgr) => {
        // Événements de la carte
        engine.on('viewChanged', ({ center, zoom, extent }) => {
            updateView({ center, zoom, extent });
        });
        
        engine.on('mapClick', ({ coordinate, features, pixel }) => {
            handleMapClick(coordinate, features, pixel);
        });
        
        engine.on('pointerMove', ({ coordinate, features, pixel }) => {
            handlePointerMove(coordinate, features, pixel);
        });
        
        // Événements de performance
        perfMgr.on('performanceDegraded', (metrics) => {
            console.warn('⚠️ Performance dégradée:', metrics);
        });
        
        perfMgr.on('memoryThresholdExceeded', (metrics) => {
            console.warn('⚠️ Seuil mémoire dépassé:', metrics);
        });
        
        // Événements des couches
        layerMgr.on('layerAdded', (layer) => {
            console.log('➕ Couche ajoutée:', layer.get('name'));
        });
        
        layerMgr.on('layerRemoved', (layer) => {
            console.log('➖ Couche supprimée:', layer.get('name'));
        });
        
        // Événements de projection
        projMgr.on('projectionChanged', ({ newProjection }) => {
            console.log('🗺️ Projection changée:', newProjection);
        });
        
    }, [updateView]);
    
    /**
     * Création des couches par défaut
     */
    const createDefaultLayers = useCallback(async (layerMgr) => {
        try {
            // Couche du territoire marocain
            const moroccoLayer = layerMgr.createMoroccoTerritoryLayer();
            layerMgr.addLayer(moroccoLayer, 'vector');
            
            // Couches d'équipements
            const equipmentTypes = ['comint', 'elint', 'anti-drone', 'jammer', 'sensor'];
            equipmentTypes.forEach(type => {
                const equipmentLayer = layerMgr.createEquipmentLayer(type);
                layerMgr.addLayer(equipmentLayer, 'equipment');
            });
            
            console.log('✅ Couches par défaut créées');
            
        } catch (error) {
            console.error('❌ Erreur lors de la création des couches:', error);
        }
    }, []);
    
    /**
     * Gestionnaire de clic sur la carte
     */
    const handleMapClick = useCallback((coordinate, features, pixel) => {
        // Gestion de la sélection d'équipements
        if (features && features.length > 0) {
            const feature = features[0];
            const equipmentId = feature.get('equipmentId');
            
            if (equipmentId) {
                // Sélectionner l'équipement dans le store
                // selectedEquipment sera mis à jour par le store
            }
        }
        
        // Fermer le menu contextuel s'il est ouvert
        setContextMenu(null);
    }, []);
    
    /**
     * Gestionnaire de mouvement de pointeur
     */
    const handlePointerMove = useCallback((coordinate, features, pixel) => {
        // Changer le curseur si survol d'un équipement
        if (mapRef.current) {
            const cursor = features && features.length > 0 ? 'pointer' : 'default';
            mapRef.current.style.cursor = cursor;
        }
    }, []);
    
    /**
     * Gestionnaire de menu contextuel
     */
    const handleContextMenu = useCallback((event) => {
        event.preventDefault();
        
        if (!gisEngine) return;
        
        const pixel = gisEngine.getMap().getEventPixel(event);
        const coordinate = gisEngine.getMap().getCoordinateFromPixel(pixel);
        const features = gisEngine.getMap().getFeaturesAtPixel(pixel);
        
        setContextMenu({
            x: event.clientX,
            y: event.clientY,
            coordinate,
            features
        });
    }, [gisEngine]);
    
    /**
     * Synchronisation avec le mode hors ligne
     */
    useEffect(() => {
        if (gisEngine && offlineMode !== undefined) {
            gisEngine.setOfflineMode(offlineMode);
        }
    }, [gisEngine, offlineMode]);
    
    /**
     * Synchronisation avec la couche de base sélectionnée
     */
    useEffect(() => {
        if (gisEngine && selectedBaseLayer) {
            gisEngine.setBaseLayer(selectedBaseLayer);
        }
    }, [gisEngine, selectedBaseLayer]);
    
    /**
     * Synchronisation avec les équipements
     */
    useEffect(() => {
        if (layerManager && equipment) {
            updateEquipmentLayers();
        }
    }, [layerManager, equipment]);
    
    /**
     * Mise à jour des couches d'équipements
     */
    const updateEquipmentLayers = useCallback(() => {
        if (!layerManager || !equipment) return;
        
        // Grouper les équipements par type
        const equipmentByType = equipment.reduce((acc, eq) => {
            const type = eq.type.toLowerCase();
            if (!acc[type]) acc[type] = [];
            acc[type].push(eq);
            return acc;
        }, {});
        
        // Mettre à jour chaque couche d'équipement
        Object.entries(equipmentByType).forEach(([type, equipmentList]) => {
            const layer = layerManager.getLayer(`equipment_${type}`);
            if (layer) {
                const source = layer.getSource();
                
                // Vider la source
                source.clear();
                
                // Ajouter les nouvelles features
                const features = equipmentList.map(eq => {
                    const feature = new ol.Feature({
                        geometry: new ol.geom.Point(ol.proj.fromLonLat([eq.longitude, eq.latitude])),
                        equipmentId: eq.id,
                        name: eq.name,
                        type: eq.type,
                        status: eq.status
                    });
                    
                    return feature;
                });
                
                source.addFeatures(features);
            }
        });
    }, [layerManager, equipment]);
    
    return (
        <div 
            className={`relative ${className}`}
            style={{ height, width }}
            onContextMenu={handleContextMenu}
        >
            {/* Conteneur de la carte */}
            <div
                ref={mapRef}
                className="w-full h-full bg-gray-100"
                style={{ minHeight: '400px' }}
            />
            
            {/* Contrôles de carte */}
            {enableControls && isInitialized && (
                <>
                    <MapControls
                        gisEngine={gisEngine}
                        layerManager={layerManager}
                        projectionManager={projectionManager}
                        performanceManager={performanceManager}
                    />
                    
                    <LayerSwitcher
                        layerManager={layerManager}
                        className="absolute top-4 right-4"
                    />
                </>
            )}
            
            {/* Outils de mesure */}
            {enableMeasurement && isInitialized && (
                <MeasurementTools
                    gisEngine={gisEngine}
                    layerManager={layerManager}
                    className="absolute bottom-4 left-4"
                />
            )}
            
            {/* Menu contextuel */}
            {contextMenu && (
                <ContextMenu
                    x={contextMenu.x}
                    y={contextMenu.y}
                    coordinate={contextMenu.coordinate}
                    features={contextMenu.features}
                    gisEngine={gisEngine}
                    layerManager={layerManager}
                    onClose={() => setContextMenu(null)}
                />
            )}
            
            {/* Indicateur de chargement */}
            {!isInitialized && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Initialisation de la carte...</p>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OpenLayersMap;
