/**
 * Service Worker pour C2-EW - Fonctionnalité Hors Ligne Complète
 */

const CACHE_NAME = 'c2ew-offline-v1';
const TILE_CACHE_NAME = 'c2ew-tiles-v1';

// Installation du Service Worker
self.addEventListener('install', (event) => {
    console.log('🔧 Installation du Service Worker C2-EW Hors Ligne');
    event.waitUntil(self.skipWaiting());
});

// Activation du Service Worker
self.addEventListener('activate', (event) => {
    console.log('🚀 Activation du Service Worker C2-EW Hors Ligne');
    event.waitUntil(self.clients.claim());
});

// Interception des requêtes
self.addEventListener('fetch', (event) => {
    const url = new URL(event.request.url);
    
    // Gestion des tuiles de carte
    if (url.hostname.includes('openstreetmap.org') ||
        url.hostname.includes('tile.openstreetmap.org') ||
        url.pathname.includes('/offline-tiles/')) {
        
        event.respondWith(handleTileRequest(event.request));
        return;
    }
    
    // Gestion des autres ressources
    event.respondWith(
        fetch(event.request).catch(() => {
            // En cas d'échec réseau, retourner une réponse par défaut
            if (event.request.destination === 'document') {
                return new Response(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>C2-EW - Mode Hors Ligne</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            .offline { color: #f59e0b; }
                        </style>
                    </head>
                    <body>
                        <h1>🛡️ C2-EW Platform</h1>
                        <h2 class="offline">📱 Mode Hors Ligne</h2>
                        <p>L'application fonctionne en mode hors ligne avec les données en cache.</p>
                    </body>
                    </html>
                `, {
                    headers: { 'Content-Type': 'text/html' }
                });
            }
            
            return new Response('Ressource non disponible hors ligne', {
                status: 503,
                statusText: 'Service Unavailable'
            });
        })
    );
});

// Gestion spéciale des tuiles de carte
async function handleTileRequest(request) {
    try {
        // Essayer d'abord le cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Si pas en cache, essayer le réseau
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Mettre en cache la tuile
            const cache = await caches.open(TILE_CACHE_NAME);
            cache.put(request, networkResponse.clone());
            return networkResponse;
        }
        
        throw new Error('Network response not ok');
        
    } catch (error) {
        console.log('📱 Mode hors ligne - Tuile non disponible');
        
        // Retourner une tuile par défaut
        return createDefaultTileResponse();
    }
}

// Créer une réponse de tuile par défaut
function createDefaultTileResponse() {
    const svg = `
        <svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
            <rect width="256" height="256" fill="#f0f0f0" stroke="#ccc"/>
            <text x="128" y="120" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">Mode</text>
            <text x="128" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">Hors Ligne</text>
            <text x="128" y="100" text-anchor="middle" font-size="24">📱</text>
        </svg>
    `;
    
    return new Response(svg, {
        headers: {
            'Content-Type': 'image/svg+xml',
            'Cache-Control': 'max-age=86400'
        }
    });
}

console.log('🛡️ Service Worker C2-EW Hors Ligne chargé');
