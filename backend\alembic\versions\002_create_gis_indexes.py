"""Create GIS indexes and functions

Revision ID: 002_gis_indexes
Revises: 001_gis_tables
Create Date: 2024-01-15 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '002_gis_indexes'
down_revision = '001_gis_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Create GIS indexes and functions"""
    
    # ===================================
    # Create Indexes for spatial_layers
    # ===================================
    
    op.create_index('idx_spatial_layer_name', 'spatial_layers', ['name'])
    op.create_index('idx_spatial_layer_type', 'spatial_layers', ['layer_type'])
    op.create_index('idx_spatial_layer_srid', 'spatial_layers', ['srid'])
    op.create_index('idx_spatial_layer_visible', 'spatial_layers', ['visible'])
    op.create_index('idx_spatial_layer_created', 'spatial_layers', ['created_at'])
    op.create_index('idx_spatial_layer_type_visible', 'spatial_layers', ['layer_type', 'visible'])
    
    # ===================================
    # Create Indexes for spatial_features
    # ===================================
    
    op.create_index('idx_spatial_feature_layer', 'spatial_features', ['layer_id'])
    op.create_index('idx_spatial_feature_type', 'spatial_features', ['feature_type'])
    op.create_index('idx_spatial_feature_name', 'spatial_features', ['name'])
    op.create_index('idx_spatial_feature_layer_type', 'spatial_features', ['layer_id', 'feature_type'])
    
    # GIN index for JSON properties
    op.execute("CREATE INDEX idx_spatial_feature_properties ON spatial_features USING GIN(properties)")
    
    # ===================================
    # Create Indexes for morocco_territory
    # ===================================
    
    op.create_index('idx_morocco_region_name', 'morocco_territory', ['region_name'])
    op.create_index('idx_morocco_region_type', 'morocco_territory', ['region_type'])
    op.create_index('idx_morocco_admin_level', 'morocco_territory', ['administrative_level'])
    op.create_index('idx_morocco_official_code', 'morocco_territory', ['official_code'])
    op.create_index('idx_morocco_parent_region', 'morocco_territory', ['parent_region_id'])
    op.create_index('idx_morocco_admin_parent', 'morocco_territory', ['administrative_level', 'parent_region_id'])
    op.create_index('idx_morocco_type_level', 'morocco_territory', ['region_type', 'administrative_level'])
    
    # Trigram indexes for text search
    op.execute("CREATE INDEX idx_morocco_region_name_trgm ON morocco_territory USING GIN(region_name gin_trgm_ops)")
    op.execute("CREATE INDEX idx_morocco_region_name_ar_trgm ON morocco_territory USING GIN(region_name_ar gin_trgm_ops)")
    
    # ===================================
    # Create Indexes for mbtiles_datasets
    # ===================================
    
    op.create_index('idx_mbtiles_name', 'mbtiles_datasets', ['name'])
    op.create_index('idx_mbtiles_format', 'mbtiles_datasets', ['format'])
    op.create_index('idx_mbtiles_active', 'mbtiles_datasets', ['is_active'])
    op.create_index('idx_mbtiles_accessed', 'mbtiles_datasets', ['last_accessed'])
    op.create_index('idx_mbtiles_zoom_range', 'mbtiles_datasets', ['min_zoom', 'max_zoom'])
    op.create_index('idx_mbtiles_active_format', 'mbtiles_datasets', ['is_active', 'format'])
    
    # GIN index for metadata
    op.execute("CREATE INDEX idx_mbtiles_metadata ON mbtiles_datasets USING GIN(metadata)")
    
    # ===================================
    # Create Indexes for visibility_analyses
    # ===================================
    
    op.create_index('idx_visibility_name', 'visibility_analyses', ['name'])
    op.create_index('idx_visibility_max_distance', 'visibility_analyses', ['max_distance'])
    op.create_index('idx_visibility_created', 'visibility_analyses', ['created_at'])
    op.create_index('idx_visibility_distance_height', 'visibility_analyses', ['max_distance', 'observer_height'])
    
    # GIN index for parameters
    op.execute("CREATE INDEX idx_visibility_parameters ON visibility_analyses USING GIN(parameters)")
    
    # ===================================
    # Create Indexes for spatial_measurements
    # ===================================
    
    op.create_index('idx_measurement_type', 'spatial_measurements', ['measurement_type'])
    op.create_index('idx_measurement_value', 'spatial_measurements', ['value'])
    op.create_index('idx_measurement_unit', 'spatial_measurements', ['unit'])
    op.create_index('idx_measurement_name', 'spatial_measurements', ['name'])
    op.create_index('idx_measurement_type_unit', 'spatial_measurements', ['measurement_type', 'unit'])
    
    # GIN index for properties
    op.execute("CREATE INDEX idx_measurement_properties ON spatial_measurements USING GIN(properties)")
    
    # ===================================
    # Create Indexes for spatial_cache
    # ===================================
    
    op.create_index('idx_cache_key', 'spatial_cache', ['cache_key'])
    op.create_index('idx_cache_type', 'spatial_cache', ['cache_type'])
    op.create_index('idx_cache_expires', 'spatial_cache', ['expires_at'])
    op.create_index('idx_cache_accessed', 'spatial_cache', ['last_accessed'])
    op.create_index('idx_cache_source', 'spatial_cache', ['source_id'])
    op.create_index('idx_cache_priority_accessed', 'spatial_cache', ['priority', 'last_accessed'])
    op.create_index('idx_cache_type_expires', 'spatial_cache', ['cache_type', 'expires_at'])
    
    # GIN index for data
    op.execute("CREATE INDEX idx_cache_data ON spatial_cache USING GIN(data)")
    
    # ===================================
    # Create Indexes for sync_logs
    # ===================================
    
    op.create_index('idx_sync_type', 'sync_logs', ['sync_type'])
    op.create_index('idx_sync_status', 'sync_logs', ['status'])
    op.create_index('idx_sync_table', 'sync_logs', ['table_name'])
    op.create_index('idx_sync_record', 'sync_logs', ['record_id'])
    op.create_index('idx_sync_session', 'sync_logs', ['sync_session_id'])
    op.create_index('idx_sync_created', 'sync_logs', ['created_at'])
    op.create_index('idx_sync_table_status', 'sync_logs', ['table_name', 'status'])
    op.create_index('idx_sync_session_status', 'sync_logs', ['sync_session_id', 'status'])
    op.create_index('idx_sync_type_created', 'sync_logs', ['sync_type', 'created_at'])
    
    # GIN indexes for JSON data
    op.execute("CREATE INDEX idx_sync_local_data ON sync_logs USING GIN(local_data)")
    op.execute("CREATE INDEX idx_sync_remote_data ON sync_logs USING GIN(remote_data)")
    
    # ===================================
    # Create Partial Indexes for Performance
    # ===================================
    
    # Partial indexes for active data only
    op.execute("CREATE INDEX idx_spatial_layer_active ON spatial_layers(name, layer_type) WHERE visible = TRUE")
    op.execute("CREATE INDEX idx_mbtiles_active_only ON mbtiles_datasets(name, format) WHERE is_active = TRUE")
    op.execute("CREATE INDEX idx_morocco_official_only ON morocco_territory(region_name, region_type) WHERE is_official = TRUE")
    
    # Partial index for valid cache
    op.execute("CREATE INDEX idx_cache_valid ON spatial_cache(cache_key, cache_type) WHERE expires_at IS NULL OR expires_at > NOW()")
    
    # Partial index for sync errors
    op.execute("CREATE INDEX idx_sync_errors ON sync_logs(table_name, created_at) WHERE status IN ('error', 'conflict')")
    
    # ===================================
    # Create PostGIS Functions
    # ===================================
    
    # Function to validate Morocco territory
    op.execute("""
    CREATE OR REPLACE FUNCTION validate_morocco_territory(
        input_geometry GEOMETRY
    ) RETURNS BOOLEAN AS $$
    DECLARE
        official_bounds GEOMETRY;
        is_within BOOLEAN;
    BEGIN
        -- Get official Morocco bounds
        SELECT ST_Union(geometry) INTO official_bounds
        FROM morocco_territory 
        WHERE region_type IN ('mainland', 'sahara') 
        AND is_official = TRUE;
        
        -- Check if geometry is within official bounds
        SELECT ST_Within(input_geometry, official_bounds) INTO is_within;
        
        RETURN is_within;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
    """)
    
    # Function to get Morocco region from point
    op.execute("""
    CREATE OR REPLACE FUNCTION get_morocco_region(
        input_point GEOMETRY
    ) RETURNS TABLE(
        region_id UUID,
        region_name VARCHAR,
        region_name_ar VARCHAR,
        region_type VARCHAR,
        administrative_level INTEGER
    ) AS $$
    BEGIN
        RETURN QUERY
        SELECT 
            mt.id,
            mt.region_name,
            mt.region_name_ar,
            mt.region_type,
            mt.administrative_level
        FROM morocco_territory mt
        WHERE ST_Contains(mt.geometry, input_point)
        AND mt.is_official = TRUE
        ORDER BY mt.administrative_level DESC
        LIMIT 1;
    END;
    $$ LANGUAGE plpgsql STABLE;
    """)
    
    # Function for precise distance calculation
    op.execute("""
    CREATE OR REPLACE FUNCTION calculate_precise_distance(
        point1 GEOMETRY,
        point2 GEOMETRY,
        unit TEXT DEFAULT 'meters'
    ) RETURNS FLOAT AS $$
    DECLARE
        distance_m FLOAT;
        result FLOAT;
    BEGIN
        -- Calculate geodesic distance
        distance_m := ST_Distance(point1::GEOGRAPHY, point2::GEOGRAPHY);
        
        -- Unit conversion
        CASE unit
            WHEN 'meters', 'm' THEN result := distance_m;
            WHEN 'kilometers', 'km' THEN result := distance_m / 1000.0;
            WHEN 'miles', 'mi' THEN result := distance_m / 1609.344;
            WHEN 'nautical_miles', 'nm' THEN result := distance_m / 1852.0;
            ELSE result := distance_m;
        END CASE;
        
        RETURN result;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
    """)
    
    # Function for azimuth calculation
    op.execute("""
    CREATE OR REPLACE FUNCTION calculate_azimuth(
        point1 GEOMETRY,
        point2 GEOMETRY,
        unit TEXT DEFAULT 'degrees'
    ) RETURNS FLOAT AS $$
    DECLARE
        azimuth_rad FLOAT;
        result FLOAT;
    BEGIN
        -- Calculate azimuth in radians
        azimuth_rad := ST_Azimuth(point1, point2);
        
        -- Unit conversion
        CASE unit
            WHEN 'degrees', 'deg' THEN result := degrees(azimuth_rad);
            WHEN 'radians', 'rad' THEN result := azimuth_rad;
            WHEN 'mils' THEN result := azimuth_rad * 3200 / PI();
            ELSE result := degrees(azimuth_rad);
        END CASE;
        
        -- Normalize to 0-360 degrees
        IF unit IN ('degrees', 'deg') THEN
            result := CASE WHEN result < 0 THEN result + 360 ELSE result END;
        END IF;
        
        RETURN result;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
    """)
    
    # Function for precise area calculation
    op.execute("""
    CREATE OR REPLACE FUNCTION calculate_precise_area(
        polygon_geom GEOMETRY,
        unit TEXT DEFAULT 'square_meters'
    ) RETURNS FLOAT AS $$
    DECLARE
        area_m2 FLOAT;
        result FLOAT;
    BEGIN
        -- Calculate geodesic area
        area_m2 := ST_Area(polygon_geom::GEOGRAPHY);
        
        -- Unit conversion
        CASE unit
            WHEN 'square_meters', 'm2' THEN result := area_m2;
            WHEN 'square_kilometers', 'km2' THEN result := area_m2 / 1000000.0;
            WHEN 'hectares', 'ha' THEN result := area_m2 / 10000.0;
            WHEN 'acres' THEN result := area_m2 / 4046.856;
            ELSE result := area_m2;
        END CASE;
        
        RETURN result;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
    """)
    
    # ===================================
    # Update Statistics
    # ===================================
    
    op.execute("ANALYZE spatial_layers")
    op.execute("ANALYZE spatial_features")
    op.execute("ANALYZE morocco_territory")
    op.execute("ANALYZE mbtiles_datasets")
    op.execute("ANALYZE visibility_analyses")
    op.execute("ANALYZE spatial_measurements")
    op.execute("ANALYZE spatial_cache")
    op.execute("ANALYZE sync_logs")


def downgrade():
    """Drop GIS indexes and functions"""
    
    # Drop functions
    op.execute("DROP FUNCTION IF EXISTS validate_morocco_territory(GEOMETRY)")
    op.execute("DROP FUNCTION IF EXISTS get_morocco_region(GEOMETRY)")
    op.execute("DROP FUNCTION IF EXISTS calculate_precise_distance(GEOMETRY, GEOMETRY, TEXT)")
    op.execute("DROP FUNCTION IF EXISTS calculate_azimuth(GEOMETRY, GEOMETRY, TEXT)")
    op.execute("DROP FUNCTION IF EXISTS calculate_precise_area(GEOMETRY, TEXT)")
    
    # Drop indexes (Alembic will handle this automatically when dropping tables)
    pass
