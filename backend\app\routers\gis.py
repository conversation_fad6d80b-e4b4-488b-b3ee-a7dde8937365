"""
Endpoints FastAPI pour l'API géospatiale
Module SIG C2-EW
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
from datetime import datetime

from ..services.spatial_service import spatial_service
from ..services.sync_service import sync_service
from ..models.gis_models import (
    SpatialLayer, SpatialFeature, MoroccoTerritory,
    VisibilityAnalysis, SpatialMeasurement
)
from ..schemas.gis_schemas import (
    SpatialSearchRequest, VisibilityAnalysisRequest, MeasurementRequest,
    SyncRequest, SpatialSearchResponse, VisibilityAnalysisResponse,
    MeasurementResponse, SyncResponse
)
from ..core.auth import get_current_user
from ..core.database import get_async_session

router = APIRouter(prefix="/api/v1/gis", tags=["GIS"])

# ===================================
# Endpoints de recherche spatiale
# ===================================

@router.post("/spatial-search", response_model=SpatialSearchResponse)
async def spatial_search(
    request: SpatialSearchRequest,
    current_user = Depends(get_current_user)
):
    """Recherche spatiale autour d'un point"""
    try:
        results = await spatial_service.spatial_search(
            search_point=(request.longitude, request.latitude),
            search_radius=request.radius,
            layer_types=request.layer_types,
            feature_types=request.feature_types,
            limit=request.limit
        )
        
        return SpatialSearchResponse(
            status="success",
            results=results,
            total_found=len(results),
            search_parameters={
                "point": [request.longitude, request.latitude],
                "radius": request.radius,
                "layer_types": request.layer_types,
                "feature_types": request.feature_types
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur recherche spatiale: {str(e)}")

@router.get("/morocco-territory/validate")
async def validate_morocco_territory(
    longitude: float = Query(..., description="Longitude"),
    latitude: float = Query(..., description="Latitude"),
    current_user = Depends(get_current_user)
):
    """Valider qu'un point est dans le territoire marocain officiel"""
    try:
        # Créer un point
        point_wkt = f"POINT({longitude} {latitude})"
        
        is_valid = await spatial_service.validate_morocco_territory(point_wkt)
        region_info = await spatial_service.get_morocco_region(longitude, latitude)
        
        return {
            "is_in_morocco": is_valid,
            "coordinates": [longitude, latitude],
            "region_info": region_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur validation territoire: {str(e)}")

@router.get("/morocco-territory/geojson")
async def get_morocco_territory_geojson(
    region_type: Optional[str] = Query(None, description="Type de région (mainland, sahara, maritime)"),
    admin_level: Optional[int] = Query(None, description="Niveau administratif (1, 2, 3)"),
    current_user = Depends(get_current_user)
):
    """Obtenir le territoire marocain en format GeoJSON"""
    try:
        async with get_async_session() as session:
            # Construire la requête
            query = session.query(MoroccoTerritory).filter(
                MoroccoTerritory.is_official == True
            )
            
            if region_type:
                query = query.filter(MoroccoTerritory.region_type == region_type)
            
            if admin_level:
                query = query.filter(MoroccoTerritory.administrative_level == admin_level)
            
            territories = await query.all()
            
            # Convertir en GeoJSON
            features = []
            for territory in territories:
                # Convertir la géométrie en GeoJSON
                geometry_geojson = await spatial_service._wkt_to_geojson(territory.geometry)
                
                feature = {
                    "type": "Feature",
                    "properties": {
                        "id": str(territory.id),
                        "region_name": territory.region_name,
                        "region_name_ar": territory.region_name_ar,
                        "region_type": territory.region_type,
                        "administrative_level": territory.administrative_level,
                        "official_code": territory.official_code,
                        "population": territory.population,
                        "area_km2": territory.area_km2,
                        "source_authority": territory.source_authority
                    },
                    "geometry": geometry_geojson
                }
                features.append(feature)
            
            geojson = {
                "type": "FeatureCollection",
                "features": features,
                "metadata": {
                    "total_features": len(features),
                    "generated_at": datetime.utcnow().isoformat(),
                    "filters": {
                        "region_type": region_type,
                        "admin_level": admin_level
                    }
                }
            }
            
            return JSONResponse(content=geojson)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur obtention territoire: {str(e)}")

# ===================================
# Endpoints de calculs géométriques
# ===================================

@router.get("/calculate/distance")
async def calculate_distance(
    lon1: float = Query(..., description="Longitude point 1"),
    lat1: float = Query(..., description="Latitude point 1"),
    lon2: float = Query(..., description="Longitude point 2"),
    lat2: float = Query(..., description="Latitude point 2"),
    unit: str = Query("meters", description="Unité (meters, kilometers, miles, nautical_miles)"),
    current_user = Depends(get_current_user)
):
    """Calculer la distance géodésique entre deux points"""
    try:
        distance = await spatial_service.calculate_precise_distance(
            (lon1, lat1), (lon2, lat2), unit
        )
        
        return {
            "distance": distance,
            "unit": unit,
            "points": {
                "point1": [lon1, lat1],
                "point2": [lon2, lat2]
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur calcul distance: {str(e)}")

@router.get("/calculate/azimuth")
async def calculate_azimuth(
    lon1: float = Query(..., description="Longitude point 1"),
    lat1: float = Query(..., description="Latitude point 1"),
    lon2: float = Query(..., description="Longitude point 2"),
    lat2: float = Query(..., description="Latitude point 2"),
    unit: str = Query("degrees", description="Unité (degrees, radians, mils)"),
    current_user = Depends(get_current_user)
):
    """Calculer l'azimut entre deux points"""
    try:
        azimuth = await spatial_service.calculate_azimuth(
            (lon1, lat1), (lon2, lat2), unit
        )
        
        return {
            "azimuth": azimuth,
            "unit": unit,
            "points": {
                "from": [lon1, lat1],
                "to": [lon2, lat2]
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur calcul azimut: {str(e)}")

# ===================================
# Endpoints d'analyse de visibilité
# ===================================

@router.post("/visibility-analysis", response_model=VisibilityAnalysisResponse)
async def create_visibility_analysis(
    request: VisibilityAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user)
):
    """Créer une analyse de visibilité"""
    try:
        # Valider que le point est au Maroc
        is_in_morocco = await spatial_service.validate_morocco_territory(
            f"POINT({request.observer_longitude} {request.observer_latitude})"
        )
        
        if not is_in_morocco:
            raise HTTPException(
                status_code=400, 
                detail="Le point d'observation doit être dans le territoire marocain"
            )
        
        # Lancer l'analyse
        analysis_result = await spatial_service.calculate_visibility_analysis(
            observer_point=(request.observer_longitude, request.observer_latitude),
            observer_height=request.observer_height,
            max_distance=request.max_distance,
            target_height=request.target_height,
            grid_resolution=request.grid_resolution,
            earth_curvature=request.earth_curvature
        )
        
        # Enregistrer en base si demandé
        if request.save_analysis:
            background_tasks.add_task(
                save_visibility_analysis,
                request, analysis_result, current_user.id
            )
        
        return VisibilityAnalysisResponse(
            status="success",
            analysis_id=None,  # Sera défini si sauvegardé
            observer_point={
                "longitude": request.observer_longitude,
                "latitude": request.observer_latitude,
                "height": request.observer_height
            },
            parameters={
                "max_distance": request.max_distance,
                "target_height": request.target_height,
                "grid_resolution": request.grid_resolution,
                "earth_curvature": request.earth_curvature
            },
            results=analysis_result["results"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur analyse de visibilité: {str(e)}")

async def save_visibility_analysis(
    request: VisibilityAnalysisRequest,
    analysis_result: Dict[str, Any],
    user_id: UUID
):
    """Sauvegarder une analyse de visibilité en arrière-plan"""
    try:
        async with get_async_session() as session:
            # Convertir la zone visible en WKT si disponible
            visible_area_wkt = None
            if analysis_result["results"].get("visible_area_geojson"):
                from shapely.geometry import shape
                geom = shape(analysis_result["results"]["visible_area_geojson"])
                visible_area_wkt = geom.wkt
            
            analysis = VisibilityAnalysis(
                name=request.name or f"Analyse {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                description=request.description,
                observer_point=f"POINT({request.observer_longitude} {request.observer_latitude})",
                observer_height=request.observer_height,
                max_distance=request.max_distance,
                target_height=request.target_height,
                earth_curvature=request.earth_curvature,
                grid_resolution=request.grid_resolution,
                visible_area=visible_area_wkt,
                total_area=analysis_result["results"].get("total_area_m2"),
                visible_percentage=analysis_result["results"].get("visible_percentage"),
                calculation_time=analysis_result["results"].get("calculation_time_seconds"),
                parameters=analysis_result.get("parameters"),
                created_by=user_id
            )
            
            session.add(analysis)
            await session.commit()
            
    except Exception as e:
        logger.error(f"❌ Erreur sauvegarde analyse de visibilité: {e}")

# ===================================
# Endpoints de mesures spatiales
# ===================================

@router.post("/measurements", response_model=MeasurementResponse)
async def create_measurement(
    request: MeasurementRequest,
    current_user = Depends(get_current_user)
):
    """Créer une mesure spatiale"""
    try:
        measurement = await spatial_service.create_measurement(
            geometry=request.geometry,
            measurement_type=request.measurement_type,
            name=request.name,
            notes=request.notes
        )
        
        return MeasurementResponse(
            status="success",
            measurement=measurement
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur création mesure: {str(e)}")

@router.get("/measurements")
async def get_measurements(
    measurement_type: Optional[str] = Query(None, description="Type de mesure"),
    limit: int = Query(100, description="Nombre maximum de résultats"),
    current_user = Depends(get_current_user)
):
    """Obtenir la liste des mesures spatiales"""
    try:
        async with get_async_session() as session:
            query = session.query(SpatialMeasurement)
            
            if measurement_type:
                query = query.filter(SpatialMeasurement.measurement_type == measurement_type)
            
            query = query.order_by(SpatialMeasurement.created_at.desc()).limit(limit)
            measurements = await query.all()
            
            results = []
            for measurement in measurements:
                # Convertir la géométrie en GeoJSON
                geometry_geojson = await spatial_service._wkt_to_geojson(measurement.geometry)
                
                result = {
                    "id": str(measurement.id),
                    "name": measurement.name,
                    "measurement_type": measurement.measurement_type,
                    "value": measurement.value,
                    "unit": measurement.unit,
                    "geometry_geojson": geometry_geojson,
                    "notes": measurement.notes,
                    "created_at": measurement.created_at.isoformat()
                }
                results.append(result)
            
            return {
                "status": "success",
                "measurements": results,
                "total": len(results)
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur obtention mesures: {str(e)}")

# ===================================
# Endpoints de synchronisation
# ===================================

@router.post("/sync/download", response_model=SyncResponse)
async def sync_download(
    request: SyncRequest,
    current_user = Depends(get_current_user)
):
    """Synchroniser les données PostGIS vers le client SQLite"""
    try:
        result = await sync_service.sync_to_client(
            client_id=request.client_id,
            strategy=request.strategy,
            tables=request.tables,
            extent=request.extent
        )
        
        return SyncResponse(**result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur synchronisation download: {str(e)}")

@router.post("/sync/upload")
async def sync_upload(
    client_id: str,
    changes: List[Dict[str, Any]],
    current_user = Depends(get_current_user)
):
    """Synchroniser les modifications du client vers PostGIS"""
    try:
        result = await sync_service.sync_from_client(
            client_id=client_id,
            changes=changes
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur synchronisation upload: {str(e)}")

@router.get("/sync/status/{client_id}")
async def get_sync_status(
    client_id: str,
    current_user = Depends(get_current_user)
):
    """Obtenir le statut de synchronisation d'un client"""
    try:
        status = await sync_service.get_sync_status(client_id)
        return status
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur obtention statut sync: {str(e)}")

# ===================================
# Endpoints utilitaires
# ===================================

@router.get("/health")
async def health_check():
    """Vérification de santé de l'API GIS"""
    try:
        # Tester la connexion PostGIS
        async with spatial_service.postgis_pool.acquire() as conn:
            version = await conn.fetchval("SELECT PostGIS_Version()")
        
        return {
            "status": "healthy",
            "postgis_version": version,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
