"""
Service spatial pour calculs géospatiaux avancés
Module SIG C2-EW - Utilise PostGIS et Shapely
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from uuid import UUID
from datetime import datetime

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select
from geoalchemy2.functions import ST_AsGeoJSON, ST_GeomFromText, ST_Transform
from shapely.geometry import Point, LineString, Polygon, MultiPolygon, shape
from shapely.ops import transform
from shapely import wkt, wkb
import pyproj
from geopy.distance import geodesic
import numpy as np

from ..models.gis_models import (
    SpatialFeature, MoroccoTerritory, VisibilityAnalysis, 
    SpatialMeasurement, SpatialLayer
)
from ..core.database import get_async_session
from ..core.config import settings

logger = logging.getLogger(__name__)

class SpatialService:
    """Service pour les calculs géospatiaux avancés"""
    
    def __init__(self):
        self.postgis_pool = None
        self.transformers = {}  # Cache des transformateurs de projection
        
    async def initialize(self):
        """Initialisation du service spatial"""
        try:
            # Créer le pool de connexions PostGIS
            self.postgis_pool = await asyncpg.create_pool(
                host=settings.DATABASE_HOST,
                port=settings.DATABASE_PORT,
                user=settings.DATABASE_USER,
                password=settings.DATABASE_PASSWORD,
                database=settings.DATABASE_NAME,
                min_size=2,
                max_size=10
            )
            
            logger.info("✅ Service spatial initialisé")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation du service spatial: {e}")
            raise
    
    async def validate_morocco_territory(
        self, 
        geometry: Union[str, Dict[str, Any]]
    ) -> bool:
        """Valider qu'une géométrie est dans le territoire marocain officiel"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                # Convertir la géométrie si nécessaire
                if isinstance(geometry, dict):
                    geometry_wkt = shape(geometry).wkt
                else:
                    geometry_wkt = geometry
                
                # Utiliser la fonction PostGIS personnalisée
                result = await conn.fetchval("""
                    SELECT validate_morocco_territory(ST_GeomFromText($1, 4326))
                """, geometry_wkt)
                
                return bool(result)
                
        except Exception as e:
            logger.error(f"❌ Erreur validation territoire marocain: {e}")
            return False
    
    async def get_morocco_region(
        self, 
        longitude: float, 
        latitude: float
    ) -> Optional[Dict[str, Any]]:
        """Obtenir la région administrative marocaine pour un point"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    SELECT * FROM get_morocco_region(ST_Point($1, $2, 4326))
                """, longitude, latitude)
                
                if result:
                    return {
                        'region_id': str(result['region_id']),
                        'region_name': result['region_name'],
                        'region_name_ar': result['region_name_ar'],
                        'region_type': result['region_type'],
                        'administrative_level': result['administrative_level']
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Erreur obtention région marocaine: {e}")
            return None
    
    async def calculate_precise_distance(
        self,
        point1: Tuple[float, float],
        point2: Tuple[float, float],
        unit: str = 'meters'
    ) -> float:
        """Calculer la distance géodésique précise entre deux points"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                distance = await conn.fetchval("""
                    SELECT calculate_precise_distance(
                        ST_Point($1, $2, 4326),
                        ST_Point($3, $4, 4326),
                        $5
                    )
                """, point1[0], point1[1], point2[0], point2[1], unit)
                
                return float(distance)
                
        except Exception as e:
            logger.error(f"❌ Erreur calcul distance: {e}")
            # Fallback avec Shapely/geopy
            return self._calculate_distance_fallback(point1, point2, unit)
    
    def _calculate_distance_fallback(
        self,
        point1: Tuple[float, float],
        point2: Tuple[float, float],
        unit: str = 'meters'
    ) -> float:
        """Calcul de distance de secours avec geopy"""
        try:
            # Inverser pour geopy (lat, lon)
            distance_m = geodesic((point1[1], point1[0]), (point2[1], point2[0])).meters
            
            # Conversion d'unité
            conversions = {
                'meters': 1.0,
                'kilometers': 0.001,
                'miles': 0.000621371,
                'nautical_miles': 0.000539957
            }
            
            return distance_m * conversions.get(unit, 1.0)
            
        except Exception as e:
            logger.error(f"❌ Erreur calcul distance fallback: {e}")
            return 0.0
    
    async def calculate_azimuth(
        self,
        point1: Tuple[float, float],
        point2: Tuple[float, float],
        unit: str = 'degrees'
    ) -> float:
        """Calculer l'azimut entre deux points"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                azimuth = await conn.fetchval("""
                    SELECT calculate_azimuth(
                        ST_Point($1, $2, 4326),
                        ST_Point($3, $4, 4326),
                        $5
                    )
                """, point1[0], point1[1], point2[0], point2[1], unit)
                
                return float(azimuth) if azimuth is not None else 0.0
                
        except Exception as e:
            logger.error(f"❌ Erreur calcul azimut: {e}")
            return 0.0
    
    async def calculate_precise_area(
        self,
        geometry: Union[str, Dict[str, Any]],
        unit: str = 'square_meters'
    ) -> float:
        """Calculer la surface géodésique précise d'un polygone"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                # Convertir la géométrie si nécessaire
                if isinstance(geometry, dict):
                    geometry_wkt = shape(geometry).wkt
                else:
                    geometry_wkt = geometry
                
                area = await conn.fetchval("""
                    SELECT calculate_precise_area(ST_GeomFromText($1, 4326), $2)
                """, geometry_wkt, unit)
                
                return float(area) if area is not None else 0.0
                
        except Exception as e:
            logger.error(f"❌ Erreur calcul surface: {e}")
            return 0.0
    
    async def spatial_search(
        self,
        search_point: Tuple[float, float],
        search_radius: float = 1000.0,
        layer_types: Optional[List[str]] = None,
        feature_types: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Recherche spatiale autour d'un point"""
        
        try:
            async with self.postgis_pool.acquire() as conn:
                # Préparer les paramètres
                layer_types_param = layer_types or ['vector']
                feature_types_param = feature_types
                
                # Construire la requête
                query = """
                    SELECT * FROM spatial_search(
                        ST_Point($1, $2, 4326),
                        $3,
                        $4::text[],
                        $5::text[]
                    )
                    LIMIT $6
                """
                
                results = await conn.fetch(
                    query,
                    search_point[0], search_point[1],
                    search_radius,
                    layer_types_param,
                    feature_types_param,
                    limit
                )
                
                # Convertir les résultats
                features = []
                for row in results:
                    feature = {
                        'feature_id': str(row['feature_id']),
                        'layer_name': row['layer_name'],
                        'feature_name': row['feature_name'],
                        'feature_type': row['feature_type'],
                        'distance_m': float(row['distance_m']),
                        'geometry': await self._geometry_to_geojson(row['geometry'])
                    }
                    features.append(feature)
                
                return features
                
        except Exception as e:
            logger.error(f"❌ Erreur recherche spatiale: {e}")
            return []
    
    async def calculate_visibility_analysis(
        self,
        observer_point: Tuple[float, float],
        observer_height: float = 1.75,
        max_distance: float = 10000.0,
        target_height: float = 0.0,
        grid_resolution: float = 100.0,
        earth_curvature: bool = True
    ) -> Dict[str, Any]:
        """Calculer une analyse de visibilité"""
        
        try:
            start_time = datetime.utcnow()
            
            async with self.postgis_pool.acquire() as conn:
                # Utiliser la fonction PostGIS d'analyse de visibilité
                visible_area_wkt = await conn.fetchval("""
                    SELECT ST_AsText(calculate_visibility(
                        ST_Point($1, $2, 4326),
                        $3, $4, $5, $6, $7
                    ))
                """, 
                observer_point[0], observer_point[1],
                observer_height, max_distance, target_height,
                grid_resolution, earth_curvature)
                
                calculation_time = (datetime.utcnow() - start_time).total_seconds()
                
                # Calculer les statistiques
                if visible_area_wkt:
                    visible_area_geojson = await self._wkt_to_geojson(visible_area_wkt)
                    total_area = await self.calculate_precise_area(visible_area_wkt, 'square_meters')
                    
                    # Calculer l'aire totale analysée (cercle)
                    analyzed_area = np.pi * (max_distance ** 2)
                    visible_percentage = (total_area / analyzed_area) * 100 if analyzed_area > 0 else 0
                    
                else:
                    visible_area_geojson = None
                    total_area = 0.0
                    visible_percentage = 0.0
                
                return {
                    'observer_point': {
                        'longitude': observer_point[0],
                        'latitude': observer_point[1],
                        'height': observer_height
                    },
                    'parameters': {
                        'max_distance': max_distance,
                        'target_height': target_height,
                        'grid_resolution': grid_resolution,
                        'earth_curvature': earth_curvature
                    },
                    'results': {
                        'visible_area_geojson': visible_area_geojson,
                        'total_area_m2': total_area,
                        'visible_percentage': visible_percentage,
                        'calculation_time_seconds': calculation_time
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Erreur analyse de visibilité: {e}")
            return {
                'error': str(e),
                'observer_point': {
                    'longitude': observer_point[0],
                    'latitude': observer_point[1]
                }
            }
    
    async def create_measurement(
        self,
        geometry: Union[str, Dict[str, Any]],
        measurement_type: str,
        name: Optional[str] = None,
        notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """Créer une mesure spatiale"""
        
        try:
            # Convertir la géométrie
            if isinstance(geometry, dict):
                geometry_wkt = shape(geometry).wkt
                geometry_geojson = geometry
            else:
                geometry_wkt = geometry
                geometry_geojson = await self._wkt_to_geojson(geometry_wkt)
            
            # Calculer selon le type de mesure
            if measurement_type == 'distance':
                value = await self._calculate_line_length(geometry_wkt)
                unit = 'meters'
                
            elif measurement_type == 'area':
                value = await self.calculate_precise_area(geometry_wkt, 'square_meters')
                unit = 'square_meters'
                
            elif measurement_type == 'perimeter':
                value = await self._calculate_perimeter(geometry_wkt)
                unit = 'meters'
                
            else:
                raise ValueError(f"Type de mesure non supporté: {measurement_type}")
            
            # Enregistrer en base
            async with get_async_session() as session:
                measurement = SpatialMeasurement(
                    name=name or f"Mesure {measurement_type}",
                    measurement_type=measurement_type,
                    geometry=geometry_wkt,
                    value=value,
                    unit=unit,
                    notes=notes,
                    properties={'geometry_geojson': geometry_geojson}
                )
                
                session.add(measurement)
                await session.commit()
                await session.refresh(measurement)
                
                return {
                    'id': str(measurement.id),
                    'name': measurement.name,
                    'type': measurement_type,
                    'value': value,
                    'unit': unit,
                    'geometry_geojson': geometry_geojson,
                    'notes': notes,
                    'created_at': measurement.created_at.isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Erreur création mesure: {e}")
            raise
    
    async def _calculate_line_length(self, geometry_wkt: str) -> float:
        """Calculer la longueur d'une ligne"""
        try:
            async with self.postgis_pool.acquire() as conn:
                length = await conn.fetchval("""
                    SELECT ST_Length(ST_GeomFromText($1, 4326)::geography)
                """, geometry_wkt)
                
                return float(length) if length is not None else 0.0
                
        except Exception as e:
            logger.error(f"❌ Erreur calcul longueur: {e}")
            return 0.0
    
    async def _calculate_perimeter(self, geometry_wkt: str) -> float:
        """Calculer le périmètre d'un polygone"""
        try:
            async with self.postgis_pool.acquire() as conn:
                perimeter = await conn.fetchval("""
                    SELECT ST_Perimeter(ST_GeomFromText($1, 4326)::geography)
                """, geometry_wkt)
                
                return float(perimeter) if perimeter is not None else 0.0
                
        except Exception as e:
            logger.error(f"❌ Erreur calcul périmètre: {e}")
            return 0.0
    
    async def _geometry_to_geojson(self, geometry) -> Optional[Dict[str, Any]]:
        """Convertir une géométrie PostGIS en GeoJSON"""
        try:
            if geometry is None:
                return None
                
            async with self.postgis_pool.acquire() as conn:
                geojson_str = await conn.fetchval("""
                    SELECT ST_AsGeoJSON($1)
                """, geometry)
                
                return json.loads(geojson_str) if geojson_str else None
                
        except Exception as e:
            logger.error(f"❌ Erreur conversion vers GeoJSON: {e}")
            return None
    
    async def _wkt_to_geojson(self, wkt_str: str) -> Optional[Dict[str, Any]]:
        """Convertir WKT en GeoJSON"""
        try:
            async with self.postgis_pool.acquire() as conn:
                geojson_str = await conn.fetchval("""
                    SELECT ST_AsGeoJSON(ST_GeomFromText($1, 4326))
                """, wkt_str)
                
                return json.loads(geojson_str) if geojson_str else None
                
        except Exception as e:
            logger.error(f"❌ Erreur conversion WKT vers GeoJSON: {e}")
            return None
    
    async def get_transformer(self, from_crs: str, to_crs: str):
        """Obtenir un transformateur de projection (avec cache)"""
        key = f"{from_crs}_{to_crs}"
        
        if key not in self.transformers:
            self.transformers[key] = pyproj.Transformer.from_crs(
                from_crs, to_crs, always_xy=True
            )
        
        return self.transformers[key]
    
    async def close(self):
        """Fermer les connexions"""
        try:
            if self.postgis_pool:
                await self.postgis_pool.close()
            
            self.transformers.clear()
            logger.info("✅ Service spatial fermé")
            
        except Exception as e:
            logger.error(f"❌ Erreur fermeture service spatial: {e}")

# Instance globale du service
spatial_service = SpatialService()
