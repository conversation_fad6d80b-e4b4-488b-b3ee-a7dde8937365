// https://github.com/topojson/topojson-server v3.0.1 Copyright 2019 Mike <PERSON>
!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((r=r||self).topojson=r.topojson||{})}(this,function(r){"use strict";var n=Object.prototype.hasOwnProperty;function t(r,n,t,e,o,i){3===arguments.length&&(e=i=Array,o=null);for(var a=new e(r=1<<Math.max(4,Math.ceil(Math.log(r)/Math.LN2))),u=new i(r),f=r-1,c=0;c<r;++c)a[c]=o;return{set:function(e,i){for(var c=n(e)&f,l=a[c],s=0;l!=o;){if(t(l,e))return u[c]=i;if(++s>=r)throw new Error("full hashmap");l=a[c=c+1&f]}return a[c]=e,u[c]=i,i},maybeSet:function(e,i){for(var c=n(e)&f,l=a[c],s=0;l!=o;){if(t(l,e))return u[c];if(++s>=r)throw new Error("full hashmap");l=a[c=c+1&f]}return a[c]=e,u[c]=i,i},get:function(e,i){for(var c=n(e)&f,l=a[c],s=0;l!=o;){if(t(l,e))return u[c];if(++s>=r)break;l=a[c=c+1&f]}return i},keys:function(){for(var r=[],n=0,t=a.length;n<t;++n){var e=a[n];e!=o&&r.push(e)}return r}}}function e(r,n){return r[0]===n[0]&&r[1]===n[1]}var o=new ArrayBuffer(16),i=new Float64Array(o),a=new Uint32Array(o);function u(r){i[0]=r[0],i[1]=r[1];var n=a[0]^a[1];return 2147483647&(n=n<<5^n>>7^a[2]^a[3])}function f(r){var n,o,i,a,f=r.coordinates,c=r.lines,l=r.rings,s=function(){for(var r=t(1.4*f.length,A,E,Int32Array,-1,Int32Array),n=new Int32Array(f.length),e=0,o=f.length;e<o;++e)n[e]=r.maybeSet(e,e);return n}(),h=new Int32Array(f.length),g=new Int32Array(f.length),v=new Int32Array(f.length),p=new Int8Array(f.length),y=0;for(n=0,o=f.length;n<o;++n)h[n]=g[n]=v[n]=-1;for(n=0,o=c.length;n<o;++n){var d=c[n],m=d[0],b=d[1];for(i=s[m],a=s[++m],++y,p[i]=1;++m<=b;)P(n,i,i=a,a=s[m]);++y,p[a]=1}for(n=0,o=f.length;n<o;++n)h[n]=-1;for(n=0,o=l.length;n<o;++n){var M=l[n],x=M[0]+1,w=M[1];for(P(n,s[w-1],i=s[x-1],a=s[x]);++x<=w;)P(n,i,i=a,a=s[x])}function P(r,n,t,e){if(h[t]!==r){h[t]=r;var o=g[t];if(o>=0){var i=v[t];o===n&&i===e||o===e&&i===n||(++y,p[t]=1)}else g[t]=n,v[t]=e}}function A(r){return u(f[r])}function E(r,n){return e(f[r],f[n])}h=g=v=null;var L,S=function(r,n,t,e,o){3===arguments.length&&(e=Array,o=null);for(var i=new e(r=1<<Math.max(4,Math.ceil(Math.log(r)/Math.LN2))),a=r-1,u=0;u<r;++u)i[u]=o;return{add:function(e){for(var u=n(e)&a,f=i[u],c=0;f!=o;){if(t(f,e))return!0;if(++c>=r)throw new Error("full hashset");f=i[u=u+1&a]}return i[u]=e,!0},has:function(e){for(var u=n(e)&a,f=i[u],c=0;f!=o;){if(t(f,e))return!0;if(++c>=r)break;f=i[u=u+1&a]}return!1},values:function(){for(var r=[],n=0,t=i.length;n<t;++n){var e=i[n];e!=o&&r.push(e)}return r}}}(1.4*y,u,e);for(n=0,o=f.length;n<o;++n)p[L=s[n]]&&S.add(f[L]);return S}function c(r,n,t){for(var e,o=n+(t---n>>1);n<o;++n,--t)e=r[n],r[n]=r[t],r[t]=e}function l(r){var n,t=s(r.geometry);for(n in null!=r.id&&(t.id=r.id),null!=r.bbox&&(t.bbox=r.bbox),r.properties){t.properties=r.properties;break}return t}function s(r){if(null==r)return{type:null};var n="GeometryCollection"===r.type?{type:"GeometryCollection",geometries:r.geometries.map(s)}:"Point"===r.type||"MultiPoint"===r.type?{type:r.type,coordinates:r.coordinates}:{type:r.type,arcs:r.coordinates};return null!=r.bbox&&(n.bbox=r.bbox),n}function h(r){var n,t=r[0],e=r[1];return e<t&&(n=t,t=e,e=n),t+31*e}function g(r,n){var t,e=r[0],o=r[1],i=n[0],a=n[1];return o<e&&(t=e,e=o,o=t),a<i&&(t=i,i=a,a=t),e===i&&o===a}r.topology=function(r,o){var i=function(r){var t=1/0,e=1/0,o=-1/0,i=-1/0;function a(r){null!=r&&n.call(u,r.type)&&u[r.type](r)}var u={GeometryCollection:function(r){r.geometries.forEach(a)},Point:function(r){f(r.coordinates)},MultiPoint:function(r){r.coordinates.forEach(f)},LineString:function(r){c(r.arcs)},MultiLineString:function(r){r.arcs.forEach(c)},Polygon:function(r){r.arcs.forEach(c)},MultiPolygon:function(r){r.arcs.forEach(l)}};function f(r){var n=r[0],a=r[1];n<t&&(t=n),n>o&&(o=n),a<e&&(e=a),a>i&&(i=a)}function c(r){r.forEach(f)}function l(r){r.forEach(c)}for(var s in r)a(r[s]);return o>=t&&i>=e?[t,e,o,i]:void 0}(r=function(r){var n,t,e={};for(n in r)e[n]=null==(t=r[n])?{type:null}:("FeatureCollection"===t.type?function(r){var n={type:"GeometryCollection",geometries:r.features.map(l)};return null!=r.bbox&&(n.bbox=r.bbox),n}:"Feature"===t.type?l:s)(t);return e}(r)),a=o>0&&i&&function(r,t,e){var o=t[0],i=t[1],a=t[2],u=t[3],f=a-o?(e-1)/(a-o):1,c=u-i?(e-1)/(u-i):1;function l(r){return[Math.round((r[0]-o)*f),Math.round((r[1]-i)*c)]}function s(r,n){for(var t,e,a,u,l,s=-1,h=0,g=r.length,v=new Array(g);++s<g;)t=r[s],u=Math.round((t[0]-o)*f),l=Math.round((t[1]-i)*c),u===e&&l===a||(v[h++]=[e=u,a=l]);for(v.length=h;h<n;)h=v.push([v[0][0],v[0][1]]);return v}function h(r){return s(r,2)}function g(r){return s(r,4)}function v(r){return r.map(g)}function p(r){null!=r&&n.call(y,r.type)&&y[r.type](r)}var y={GeometryCollection:function(r){r.geometries.forEach(p)},Point:function(r){r.coordinates=l(r.coordinates)},MultiPoint:function(r){r.coordinates=r.coordinates.map(l)},LineString:function(r){r.arcs=h(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(h)},Polygon:function(r){r.arcs=v(r.arcs)},MultiPolygon:function(r){r.arcs=r.arcs.map(v)}};for(var d in r)p(r[d]);return{scale:[1/f,1/c],translate:[o,i]}}(r,i,o),v=function(r){var n,o,i,a,f=r.coordinates,c=r.lines,l=r.rings,s=c.length+l.length;for(delete r.lines,delete r.rings,i=0,a=c.length;i<a;++i)for(n=c[i];n=n.next;)++s;for(i=0,a=l.length;i<a;++i)for(o=l[i];o=o.next;)++s;var h=t(2*s*1.4,u,e),g=r.arcs=[];for(i=0,a=c.length;i<a;++i){n=c[i];do{v(n)}while(n=n.next)}for(i=0,a=l.length;i<a;++i)if((o=l[i]).next)do{v(o)}while(o=o.next);else p(o);function v(r){var n,t,e,o,i,a,u,c;if(e=h.get(n=f[r[0]]))for(u=0,c=e.length;u<c;++u)if(y(o=e[u],r))return r[0]=o[0],void(r[1]=o[1]);if(i=h.get(t=f[r[1]]))for(u=0,c=i.length;u<c;++u)if(d(a=i[u],r))return r[1]=a[0],void(r[0]=a[1]);e?e.push(r):h.set(n,[r]),i?i.push(r):h.set(t,[r]),g.push(r)}function p(r){var n,t,e,o,i;if(t=h.get(f[r[0]]))for(o=0,i=t.length;o<i;++o){if(m(e=t[o],r))return r[0]=e[0],void(r[1]=e[1]);if(b(e,r))return r[0]=e[1],void(r[1]=e[0])}if(t=h.get(n=f[r[0]+M(r)]))for(o=0,i=t.length;o<i;++o){if(m(e=t[o],r))return r[0]=e[0],void(r[1]=e[1]);if(b(e,r))return r[0]=e[1],void(r[1]=e[0])}t?t.push(r):h.set(n,[r]),g.push(r)}function y(r,n){var t=r[0],o=n[0],i=r[1];if(t-i!=o-n[1])return!1;for(;t<=i;++t,++o)if(!e(f[t],f[o]))return!1;return!0}function d(r,n){var t=r[0],o=n[0],i=r[1],a=n[1];if(t-i!=o-a)return!1;for(;t<=i;++t,--a)if(!e(f[t],f[a]))return!1;return!0}function m(r,n){var t=r[0],o=n[0],i=r[1]-t;if(i!==n[1]-o)return!1;for(var a=M(r),u=M(n),c=0;c<i;++c)if(!e(f[t+(c+a)%i],f[o+(c+u)%i]))return!1;return!0}function b(r,n){var t=r[0],o=n[0],i=r[1],a=n[1],u=i-t;if(u!==a-o)return!1;for(var c=M(r),l=u-M(n),s=0;s<u;++s)if(!e(f[t+(s+c)%u],f[a-(s+l)%u]))return!1;return!0}function M(r){for(var n=r[0],t=r[1],e=n,o=e,i=f[e];++e<t;){var a=f[e];(a[0]<i[0]||a[0]===i[0]&&a[1]<i[1])&&(o=e,i=a)}return o-n}return r}(function(r){var n,t,e,o,i,a,u,l=f(r),s=r.coordinates,h=r.lines,g=r.rings;for(t=0,e=h.length;t<e;++t)for(var v=h[t],p=v[0],y=v[1];++p<y;)l.has(s[p])&&(n={0:p,1:v[1]},v[1]=p,v=v.next=n);for(t=0,e=g.length;t<e;++t)for(var d=g[t],m=d[0],b=m,M=d[1],x=l.has(s[m]);++b<M;)l.has(s[b])&&(x?(n={0:b,1:d[1]},d[1]=b,d=d.next=n):(u=M-b,c(o=s,i=m,a=M),c(o,i,i+u),c(o,i+u,a),s[M]=s[m],x=!0,b=m));return r}(function(r){var t=-1,e=[],o=[],i=[];function a(r){r&&n.call(u,r.type)&&u[r.type](r)}var u={GeometryCollection:function(r){r.geometries.forEach(a)},LineString:function(r){r.arcs=f(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(f)},Polygon:function(r){r.arcs=r.arcs.map(c)},MultiPolygon:function(r){r.arcs=r.arcs.map(l)}};function f(r){for(var n=0,o=r.length;n<o;++n)i[++t]=r[n];var a={0:t-o+1,1:t};return e.push(a),a}function c(r){for(var n=0,e=r.length;n<e;++n)i[++t]=r[n];var a={0:t-e+1,1:t};return o.push(a),a}function l(r){return r.map(c)}for(var s in r)a(r[s]);return{type:"Topology",coordinates:i,lines:e,rings:o,objects:r}}(r))),p=v.coordinates,y=t(1.4*v.arcs.length,h,g);function d(r){r&&n.call(m,r.type)&&m[r.type](r)}r=v.objects,v.bbox=i,v.arcs=v.arcs.map(function(r,n){return y.set(r,n),p.slice(r[0],r[1]+1)}),delete v.coordinates,p=null;var m={GeometryCollection:function(r){r.geometries.forEach(d)},LineString:function(r){r.arcs=b(r.arcs)},MultiLineString:function(r){r.arcs=r.arcs.map(b)},Polygon:function(r){r.arcs=r.arcs.map(b)},MultiPolygon:function(r){r.arcs=r.arcs.map(M)}};function b(r){var n=[];do{var t=y.get(r);n.push(r[0]<r[1]?t:~t)}while(r=r.next);return n}function M(r){return r.map(b)}for(var x in r)d(r[x]);return a&&(v.transform=a,v.arcs=function(r){for(var n=-1,t=r.length;++n<t;){for(var e,o,i=r[n],a=0,u=1,f=i.length,c=i[0],l=c[0],s=c[1];++a<f;)e=(c=i[a])[0],o=c[1],e===l&&o===s||(i[u++]=[e-l,o-s],l=e,s=o);1===u&&(i[u++]=[0,0]),i.length=u}return r}(v.arcs)),v},Object.defineProperty(r,"__esModule",{value:!0})});
