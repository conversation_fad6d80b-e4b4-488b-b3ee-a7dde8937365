# Script de démarrage complet C2-EW
# PowerShell Script pour Windows

param(
    [string]$Mode = "dev"
)

Write-Host "🚀 Démarrage de l'application C2-EW" -ForegroundColor Green
Write-Host "Mode: $Mode" -ForegroundColor Yellow
Write-Host ""

# Fonction pour vérifier si un port est utilisé
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Fonction pour attendre qu'un service soit prêt
function Wait-ForService {
    param(
        [string]$Name,
        [string]$Url,
        [int]$MaxAttempts = 30
    )
    
    Write-Host "⏳ Attente de $Name..." -ForegroundColor Yellow
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $Name est prêt!" -ForegroundColor Green
                return $true
            }
        } catch {
            # Service pas encore prêt
        }
        
        Write-Host "." -NoNewline -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Write-Host "❌ $Name n'a pas pu démarrer dans les temps" -ForegroundColor Red
    return $false
}

# Vérifier les prérequis
Write-Host "📋 Vérification des prérequis..." -ForegroundColor Blue

# Vérifier Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js non trouvé. Installez-le depuis https://nodejs.org" -ForegroundColor Red
    exit 1
}

# Vérifier Python
try {
    $pythonVersion = python --version
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python non trouvé. Installez-le depuis https://python.org" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Démarrer le backend
Write-Host "🐍 Démarrage du backend FastAPI..." -ForegroundColor Blue

if (Test-Port -Port 8000) {
    Write-Host "⚠️ Le port 8000 est déjà utilisé. Arrêt du processus existant..." -ForegroundColor Yellow
    # Tenter d'arrêter le processus existant
    Get-Process | Where-Object {$_.ProcessName -eq "python"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
}

# Démarrer le backend en arrière-plan
$backendJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Desktop\C2-EW carte en ligne\backend"
    & ".\venv\Scripts\Activate.ps1"
    python simple_server.py
}

Write-Host "Backend démarré (Job ID: $($backendJob.Id))" -ForegroundColor Green

# Attendre que le backend soit prêt
if (-not (Wait-ForService -Name "Backend API" -Url "http://localhost:8000/health")) {
    Write-Host "❌ Impossible de démarrer le backend" -ForegroundColor Red
    Stop-Job -Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""

# Démarrer le frontend
Write-Host "⚛️ Démarrage du frontend React..." -ForegroundColor Blue

if (Test-Port -Port 3001) {
    Write-Host "⚠️ Le port 3001 est déjà utilisé. Arrêt du processus existant..." -ForegroundColor Yellow
    # Tenter d'arrêter le processus existant
    Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
}

# Démarrer le frontend en arrière-plan
$frontendJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Desktop\C2-EW carte en ligne\frontend"
    npx vite --port 3001 --host 0.0.0.0
}

Write-Host "Frontend démarré (Job ID: $($frontendJob.Id))" -ForegroundColor Green

# Attendre que le frontend soit prêt
if (-not (Wait-ForService -Name "Frontend" -Url "http://localhost:3001")) {
    Write-Host "❌ Impossible de démarrer le frontend" -ForegroundColor Red
    Stop-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""

# Afficher les informations de déploiement
Write-Host "🎉 Application C2-EW déployée avec succès!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Services disponibles:" -ForegroundColor Yellow
Write-Host "  🌐 Application Web:  http://localhost:3001" -ForegroundColor Cyan
Write-Host "  📡 API Backend:      http://localhost:8000" -ForegroundColor Cyan
Write-Host "  📖 Documentation:    http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "  🏥 Health Check:     http://localhost:8000/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "👤 Comptes de test:" -ForegroundColor Yellow
Write-Host "  👨‍💼 Administrateur:   admin / admin123" -ForegroundColor Cyan
Write-Host "  🔧 Opérateur:        operator / op123" -ForegroundColor Cyan
Write-Host "  📊 Visualiseur:      viewer / view123" -ForegroundColor Cyan
Write-Host ""
Write-Host "🗺️ Fonctionnalités SIG:" -ForegroundColor Yellow
Write-Host "  📍 Carte OpenLayers avec projections marocaines" -ForegroundColor Cyan
Write-Host "  📏 Outils de mesure géodésique" -ForegroundColor Cyan
Write-Host "  👁️ Analyse de visibilité" -ForegroundColor Cyan
Write-Host "  🛡️ Gestion d'équipements C2-EW" -ForegroundColor Cyan
Write-Host "  🌍 Support territoire marocain" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔧 Commandes utiles:" -ForegroundColor Yellow
Write-Host "  Arrêter:             Ctrl+C dans cette fenêtre" -ForegroundColor Cyan
Write-Host "  Logs backend:        Get-Job | Receive-Job" -ForegroundColor Cyan
Write-Host "  Redémarrer:          .\start-c2ew.ps1" -ForegroundColor Cyan
Write-Host ""

# Ouvrir automatiquement le navigateur
Write-Host "🌐 Ouverture du navigateur..." -ForegroundColor Blue
Start-Process "http://localhost:3001"

Write-Host ""
Write-Host "✨ L'application C2-EW est maintenant prête à être utilisée!" -ForegroundColor Green
Write-Host "Appuyez sur Ctrl+C pour arrêter tous les services." -ForegroundColor Yellow
Write-Host ""

# Attendre l'interruption de l'utilisateur
try {
    while ($true) {
        Start-Sleep -Seconds 1
        
        # Vérifier si les jobs sont toujours en cours
        if ($backendJob.State -eq "Failed" -or $frontendJob.State -eq "Failed") {
            Write-Host "❌ Un des services a échoué" -ForegroundColor Red
            break
        }
    }
} catch {
    Write-Host ""
    Write-Host "🛑 Arrêt des services..." -ForegroundColor Yellow
} finally {
    # Nettoyer les jobs
    Write-Host "🧹 Nettoyage..." -ForegroundColor Blue
    Stop-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    
    Write-Host "✅ Services arrêtés" -ForegroundColor Green
    Write-Host "👋 Au revoir!" -ForegroundColor Blue
}
