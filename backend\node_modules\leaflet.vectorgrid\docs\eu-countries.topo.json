{"type": "Topology", "objects": {"eu-countries": {"type": "GeometryCollection", "geometries": [{"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Albania", "sov_a3": "ALB", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Albania", "adm0_a3": "ALB", "geou_dif": 0, "geounit": "Albania", "gu_a3": "ALB", "su_dif": 0, "subunit": "Albania", "su_a3": "ALB", "brk_diff": 0, "name": "Albania", "name_long": "Albania", "brk_a3": "ALB", "brk_name": "Albania", "brk_group": null, "abbrev": "Alb.", "postal": "AL", "formal_en": "Republic of Albania", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Albania", "name_alt": null, "mapcolor7": 1, "mapcolor8": 4, "mapcolor9": 1, "mapcolor13": 6, "pop_est": 3639453, "gdp_md_est": 21810, "pop_year": -99, "lastcensus": 2001, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "4. Lower middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "AL", "iso_a3": "ALB", "iso_n3": "008", "un_a3": "008", "wb_a2": "AL", "wb_a3": "ALB", "woe_id": -99, "adm0_a3_is": "ALB", "adm0_a3_us": "ALB", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[0, 1, 2, 3, 4]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 4, "sovereignt": "Austria", "sov_a3": "AUT", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Austria", "adm0_a3": "AUT", "geou_dif": 0, "geounit": "Austria", "gu_a3": "AUT", "su_dif": 0, "subunit": "Austria", "su_a3": "AUT", "brk_diff": 0, "name": "Austria", "name_long": "Austria", "brk_a3": "AUT", "brk_name": "Austria", "brk_group": null, "abbrev": "Aust.", "postal": "A", "formal_en": "Republic of Austria", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Austria", "name_alt": null, "mapcolor7": 3, "mapcolor8": 1, "mapcolor9": 3, "mapcolor13": 4, "pop_est": 8210281, "gdp_md_est": 329500, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "AT", "iso_a3": "AUT", "iso_n3": "040", "un_a3": "040", "wb_a2": "AT", "wb_a3": "AUT", "woe_id": -99, "adm0_a3_is": "AUT", "adm0_a3_us": "AUT", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[5, 6, 7, 8, 9, 10, 11]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Belgium", "sov_a3": "BEL", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Belgium", "adm0_a3": "BEL", "geou_dif": 0, "geounit": "Belgium", "gu_a3": "BEL", "su_dif": 0, "subunit": "Belgium", "su_a3": "BEL", "brk_diff": 0, "name": "Belgium", "name_long": "Belgium", "brk_a3": "BEL", "brk_name": "Belgium", "brk_group": null, "abbrev": "Belg.", "postal": "B", "formal_en": "Kingdom of Belgium", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Belgium", "name_alt": null, "mapcolor7": 3, "mapcolor8": 2, "mapcolor9": 1, "mapcolor13": 8, "pop_est": 10414336, "gdp_md_est": 389300, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "BE", "iso_a3": "BEL", "iso_n3": "056", "un_a3": "056", "wb_a2": "BE", "wb_a3": "BEL", "woe_id": -99, "adm0_a3_is": "BEL", "adm0_a3_us": "BEL", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[12, 13, 14, 15, 16]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 4, "sovereignt": "Bulgaria", "sov_a3": "BGR", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Bulgaria", "adm0_a3": "BGR", "geou_dif": 0, "geounit": "Bulgaria", "gu_a3": "BGR", "su_dif": 0, "subunit": "Bulgaria", "su_a3": "BGR", "brk_diff": 0, "name": "Bulgaria", "name_long": "Bulgaria", "brk_a3": "BGR", "brk_name": "Bulgaria", "brk_group": null, "abbrev": "Bulg.", "postal": "BG", "formal_en": "Republic of Bulgaria", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Bulgaria", "name_alt": null, "mapcolor7": 4, "mapcolor8": 5, "mapcolor9": 1, "mapcolor13": 8, "pop_est": 7204687, "gdp_md_est": 93750, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "BG", "iso_a3": "BGR", "iso_n3": "100", "un_a3": "100", "wb_a2": "BG", "wb_a3": "BGR", "woe_id": -99, "adm0_a3_is": "BGR", "adm0_a3_us": "BGR", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 8, "long_len": 8, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[17, 18, 19, 20, 21]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Bosnia and Herzegovina", "sov_a3": "BIH", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Bosnia and Herzegovina", "adm0_a3": "BIH", "geou_dif": 0, "geounit": "Bosnia and Herzegovina", "gu_a3": "BIH", "su_dif": 0, "subunit": "Bosnia and Herzegovina", "su_a3": "BIH", "brk_diff": 0, "name": "Bosnia and Herz.", "name_long": "Bosnia and Herzegovina", "brk_a3": "BIH", "brk_name": "Bosnia and Herz.", "brk_group": null, "abbrev": "B.H.", "postal": "BiH", "formal_en": "Bosnia and Herzegovina", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Bosnia and Herzegovina", "name_alt": null, "mapcolor7": 1, "mapcolor8": 1, "mapcolor9": 1, "mapcolor13": 2, "pop_est": 4613414, "gdp_md_est": 29700, "pop_year": -99, "lastcensus": 1991, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "BA", "iso_a3": "BIH", "iso_n3": "070", "un_a3": "070", "wb_a2": "BA", "wb_a3": "BIH", "woe_id": -99, "adm0_a3_is": "BIH", "adm0_a3_us": "BIH", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 16, "long_len": 22, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[22, 23, 24]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 4, "sovereignt": "Belarus", "sov_a3": "BLR", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Belarus", "adm0_a3": "BLR", "geou_dif": 0, "geounit": "Belarus", "gu_a3": "BLR", "su_dif": 0, "subunit": "Belarus", "su_a3": "BLR", "brk_diff": 0, "name": "Belarus", "name_long": "Belarus", "brk_a3": "BLR", "brk_name": "Belarus", "brk_group": null, "abbrev": "Bela.", "postal": "BY", "formal_en": "Republic of Belarus", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Belarus", "name_alt": null, "mapcolor7": 1, "mapcolor8": 1, "mapcolor9": 5, "mapcolor13": 11, "pop_est": 9648533, "gdp_md_est": 114100, "pop_year": -99, "lastcensus": 2009, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "BY", "iso_a3": "BLR", "iso_n3": "112", "un_a3": "112", "wb_a2": "BY", "wb_a3": "BLR", "woe_id": -99, "adm0_a3_is": "BLR", "adm0_a3_us": "BLR", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[25, 26, 27, 28, 29]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 4, "sovereignt": "Switzerland", "sov_a3": "CHE", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Switzerland", "adm0_a3": "CHE", "geou_dif": 0, "geounit": "Switzerland", "gu_a3": "CHE", "su_dif": 0, "subunit": "Switzerland", "su_a3": "CHE", "brk_diff": 0, "name": "Switzerland", "name_long": "Switzerland", "brk_a3": "CHE", "brk_name": "Switzerland", "brk_group": null, "abbrev": "Switz.", "postal": "CH", "formal_en": "Swiss Confederation", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Switzerland", "name_alt": null, "mapcolor7": 5, "mapcolor8": 2, "mapcolor9": 7, "mapcolor13": 3, "pop_est": 7604467, "gdp_md_est": 316700, "pop_year": -99, "lastcensus": 2010, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "CH", "iso_a3": "CHE", "iso_n3": "756", "un_a3": "756", "wb_a2": "CH", "wb_a3": "CHE", "woe_id": -99, "adm0_a3_is": "CHE", "adm0_a3_us": "CHE", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 11, "long_len": 11, "abbrev_len": 6, "tiny": -99, "homepart": 1}, "arcs": [[-9, 30, 31, 32]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Czech Republic", "sov_a3": "CZE", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Czech Republic", "adm0_a3": "CZE", "geou_dif": 0, "geounit": "Czech Republic", "gu_a3": "CZE", "su_dif": 0, "subunit": "Czech Republic", "su_a3": "CZE", "brk_diff": 0, "name": "Czech Rep.", "name_long": "Czech Republic", "brk_a3": "CZE", "brk_name": "Czech Rep.", "brk_group": null, "abbrev": "Cz. Rep.", "postal": "CZ", "formal_en": "Czech Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Czech Republic", "name_alt": null, "mapcolor7": 1, "mapcolor8": 1, "mapcolor9": 2, "mapcolor13": 6, "pop_est": 10211904, "gdp_md_est": 265200, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "CZ", "iso_a3": "CZE", "iso_n3": "203", "un_a3": "203", "wb_a2": "CZ", "wb_a3": "CZE", "woe_id": -99, "adm0_a3_is": "CZE", "adm0_a3_us": "CZE", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 10, "long_len": 14, "abbrev_len": 8, "tiny": -99, "homepart": 1}, "arcs": [[-11, 33, 34, 35]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 4, "sovereignt": "Denmark", "sov_a3": "DN1", "adm0_dif": 1, "level": 2, "type": "Country", "admin": "Denmark", "adm0_a3": "DNK", "geou_dif": 0, "geounit": "Denmark", "gu_a3": "DNK", "su_dif": 0, "subunit": "Denmark", "su_a3": "DNK", "brk_diff": 0, "name": "Denmark", "name_long": "Denmark", "brk_a3": "DNK", "brk_name": "Denmark", "brk_group": null, "abbrev": "Den.", "postal": "DK", "formal_en": "Kingdom of Denmark", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Denmark", "name_alt": null, "mapcolor7": 4, "mapcolor8": 1, "mapcolor9": 3, "mapcolor13": 12, "pop_est": 5500510, "gdp_md_est": 203600, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "DK", "iso_a3": "DNK", "iso_n3": "208", "un_a3": "208", "wb_a2": "DK", "wb_a3": "DNK", "woe_id": -99, "adm0_a3_is": "DNK", "adm0_a3_us": "DNK", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[[36]], [[37, 38]]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Germany", "sov_a3": "DEU", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Germany", "adm0_a3": "DEU", "geou_dif": 0, "geounit": "Germany", "gu_a3": "DEU", "su_dif": 0, "subunit": "Germany", "su_a3": "DEU", "brk_diff": 0, "name": "Germany", "name_long": "Germany", "brk_a3": "DEU", "brk_name": "Germany", "brk_group": null, "abbrev": "<PERSON>er.", "postal": "D", "formal_en": "Federal Republic of Germany", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Germany", "name_alt": null, "mapcolor7": 2, "mapcolor8": 5, "mapcolor9": 5, "mapcolor13": 1, "pop_est": 82329758, "gdp_md_est": 2918000, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "1. Developed region: G7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "DE", "iso_a3": "DEU", "iso_n3": "276", "un_a3": "276", "wb_a2": "DE", "wb_a3": "DEU", "woe_id": -99, "adm0_a3_is": "DEU", "adm0_a3_us": "DEU", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[39, 40, -34, -10, -33, 41, 42, -14, 43, 44, -38]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Spain", "sov_a3": "ESP", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Spain", "adm0_a3": "ESP", "geou_dif": 0, "geounit": "Spain", "gu_a3": "ESP", "su_dif": 0, "subunit": "Spain", "su_a3": "ESP", "brk_diff": 0, "name": "Spain", "name_long": "Spain", "brk_a3": "ESP", "brk_name": "Spain", "brk_group": null, "abbrev": "Sp.", "postal": "E", "formal_en": "Kingdom of Spain", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Spain", "name_alt": null, "mapcolor7": 4, "mapcolor8": 5, "mapcolor9": 5, "mapcolor13": 5, "pop_est": 40525002, "gdp_md_est": 1403000, "pop_year": -99, "lastcensus": 2001, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "ES", "iso_a3": "ESP", "iso_n3": "724", "un_a3": "724", "wb_a2": "ES", "wb_a3": "ESP", "woe_id": -99, "adm0_a3_is": "ESP", "adm0_a3_us": "ESP", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 5, "long_len": 5, "abbrev_len": 3, "tiny": -99, "homepart": 1}, "arcs": [[45, 46, 47, 48]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Estonia", "sov_a3": "EST", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Estonia", "adm0_a3": "EST", "geou_dif": 0, "geounit": "Estonia", "gu_a3": "EST", "su_dif": 0, "subunit": "Estonia", "su_a3": "EST", "brk_diff": 0, "name": "Estonia", "name_long": "Estonia", "brk_a3": "EST", "brk_name": "Estonia", "brk_group": null, "abbrev": "Est.", "postal": "EST", "formal_en": "Republic of Estonia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Estonia", "name_alt": null, "mapcolor7": 3, "mapcolor8": 2, "mapcolor9": 1, "mapcolor13": 10, "pop_est": 1299371, "gdp_md_est": 27410, "pop_year": -99, "lastcensus": 2000, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "EE", "iso_a3": "EST", "iso_n3": "233", "un_a3": "233", "wb_a2": "EE", "wb_a3": "EST", "woe_id": -99, "adm0_a3_is": "EST", "adm0_a3_us": "EST", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[49, 50, 51]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Finland", "sov_a3": "FI1", "adm0_dif": 1, "level": 2, "type": "Country", "admin": "Finland", "adm0_a3": "FIN", "geou_dif": 0, "geounit": "Finland", "gu_a3": "FIN", "su_dif": 0, "subunit": "Finland", "su_a3": "FIN", "brk_diff": 0, "name": "Finland", "name_long": "Finland", "brk_a3": "FIN", "brk_name": "Finland", "brk_group": null, "abbrev": "Fin.", "postal": "FIN", "formal_en": "Republic of Finland", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Finland", "name_alt": null, "mapcolor7": 4, "mapcolor8": 1, "mapcolor9": 4, "mapcolor13": 6, "pop_est": 5250275, "gdp_md_est": 193500, "pop_year": -99, "lastcensus": 2010, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "FI", "iso_a3": "FIN", "iso_n3": "246", "un_a3": "246", "wb_a2": "FI", "wb_a3": "FIN", "woe_id": -99, "adm0_a3_is": "FIN", "adm0_a3_us": "FIN", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[52, 53, 54, 55]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "France", "sov_a3": "FR1", "adm0_dif": 1, "level": 2, "type": "Country", "admin": "France", "adm0_a3": "FRA", "geou_dif": 0, "geounit": "France", "gu_a3": "FRA", "su_dif": 0, "subunit": "France", "su_a3": "FRA", "brk_diff": 0, "name": "France", "name_long": "France", "brk_a3": "FRA", "brk_name": "France", "brk_group": null, "abbrev": "<PERSON>.", "postal": "F", "formal_en": "French Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "France", "name_alt": null, "mapcolor7": 7, "mapcolor8": 5, "mapcolor9": 9, "mapcolor13": 11, "pop_est": 64057792, "gdp_md_est": 2128000, "pop_year": -99, "lastcensus": -99, "gdp_year": -99, "economy": "1. Developed region: G7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "FR", "iso_a3": "FRA", "iso_n3": "250", "un_a3": "250", "wb_a2": "FR", "wb_a3": "FRA", "woe_id": -99, "adm0_a3_is": "FRA", "adm0_a3_us": "FRA", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 3, "tiny": -99, "homepart": 1}, "arcs": [[[56]], [[57]], [[58, -42, -32, 59, 60, -47, 61, -16]]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "United Kingdom", "sov_a3": "GB1", "adm0_dif": 1, "level": 2, "type": "Country", "admin": "United Kingdom", "adm0_a3": "GBR", "geou_dif": 0, "geounit": "United Kingdom", "gu_a3": "GBR", "su_dif": 0, "subunit": "United Kingdom", "su_a3": "GBR", "brk_diff": 0, "name": "United Kingdom", "name_long": "United Kingdom", "brk_a3": "GBR", "brk_name": "United Kingdom", "brk_group": null, "abbrev": "U.K.", "postal": "GB", "formal_en": "United Kingdom of Great Britain and Northern Ireland", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "United Kingdom", "name_alt": null, "mapcolor7": 6, "mapcolor8": 6, "mapcolor9": 6, "mapcolor13": 3, "pop_est": 62262000, "gdp_md_est": 1977704, "pop_year": 0, "lastcensus": 2011, "gdp_year": 2009, "economy": "1. Developed region: G7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "GB", "iso_a3": "GBR", "iso_n3": "826", "un_a3": "826", "wb_a2": "GB", "wb_a3": "GBR", "woe_id": -99, "adm0_a3_is": "GBR", "adm0_a3_us": "GBR", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 14, "long_len": 14, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[[62, 63]], [[64]]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Greece", "sov_a3": "GRC", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Greece", "adm0_a3": "GRC", "geou_dif": 0, "geounit": "Greece", "gu_a3": "GRC", "su_dif": 0, "subunit": "Greece", "su_a3": "GRC", "brk_diff": 0, "name": "Greece", "name_long": "Greece", "brk_a3": "GRC", "brk_name": "Greece", "brk_group": null, "abbrev": "Greece", "postal": "GR", "formal_en": "Hellenic Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Greece", "name_alt": null, "mapcolor7": 2, "mapcolor8": 2, "mapcolor9": 2, "mapcolor13": 9, "pop_est": 10737428, "gdp_md_est": 343000, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "GR", "iso_a3": "GRC", "iso_n3": "300", "un_a3": "300", "wb_a2": "GR", "wb_a3": "GRC", "woe_id": -99, "adm0_a3_is": "GRC", "adm0_a3_us": "GRC", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 6, "tiny": -99, "homepart": 1}, "arcs": [[[65]], [[-2, 66, -20, 67]]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Croatia", "sov_a3": "HRV", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Croatia", "adm0_a3": "HRV", "geou_dif": 0, "geounit": "Croatia", "gu_a3": "HRV", "su_dif": 0, "subunit": "Croatia", "su_a3": "HRV", "brk_diff": 0, "name": "Croatia", "name_long": "Croatia", "brk_a3": "HRV", "brk_name": "Croatia", "brk_group": null, "abbrev": "Cro<PERSON>", "postal": "HR", "formal_en": "Republic of Croatia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Croatia", "name_alt": null, "mapcolor7": 5, "mapcolor8": 4, "mapcolor9": 5, "mapcolor13": 1, "pop_est": 4489409, "gdp_md_est": 82390, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "2. High income: nonOECD", "wikipedia": -99, "fips_10": null, "iso_a2": "HR", "iso_a3": "HRV", "iso_n3": "191", "un_a3": "191", "wb_a2": "HR", "wb_a3": "HRV", "woe_id": -99, "adm0_a3_is": "HRV", "adm0_a3_us": "HRV", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[68, -25, 69, 70, 71, 72]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Hungary", "sov_a3": "HUN", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Hungary", "adm0_a3": "HUN", "geou_dif": 0, "geounit": "Hungary", "gu_a3": "HUN", "su_dif": 0, "subunit": "Hungary", "su_a3": "HUN", "brk_diff": 0, "name": "Hungary", "name_long": "Hungary", "brk_a3": "HUN", "brk_name": "Hungary", "brk_group": null, "abbrev": "Hun.", "postal": "HU", "formal_en": "Republic of Hungary", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Hungary", "name_alt": null, "mapcolor7": 4, "mapcolor8": 6, "mapcolor9": 1, "mapcolor13": 5, "pop_est": 9905596, "gdp_md_est": 196600, "pop_year": -99, "lastcensus": 2001, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "HU", "iso_a3": "HUN", "iso_n3": "348", "un_a3": "348", "wb_a2": "HU", "wb_a3": "HUN", "woe_id": -99, "adm0_a3_is": "HUN", "adm0_a3_us": "HUN", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-6, 73, 74, 75, 76, -73, 77]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Ireland", "sov_a3": "IRL", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Ireland", "adm0_a3": "IRL", "geou_dif": 0, "geounit": "Ireland", "gu_a3": "IRL", "su_dif": 0, "subunit": "Ireland", "su_a3": "IRL", "brk_diff": 0, "name": "Ireland", "name_long": "Ireland", "brk_a3": "IRL", "brk_name": "Ireland", "brk_group": null, "abbrev": "<PERSON><PERSON>.", "postal": "IRL", "formal_en": "Ireland", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Ireland", "name_alt": null, "mapcolor7": 2, "mapcolor8": 3, "mapcolor9": 2, "mapcolor13": 2, "pop_est": 4203200, "gdp_md_est": 188400, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "IE", "iso_a3": "IRL", "iso_n3": "372", "un_a3": "372", "wb_a2": "IE", "wb_a3": "IRL", "woe_id": -99, "adm0_a3_is": "IRL", "adm0_a3_us": "IRL", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[78, -63]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Iceland", "sov_a3": "ISL", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Iceland", "adm0_a3": "ISL", "geou_dif": 0, "geounit": "Iceland", "gu_a3": "ISL", "su_dif": 0, "subunit": "Iceland", "su_a3": "ISL", "brk_diff": 0, "name": "Iceland", "name_long": "Iceland", "brk_a3": "ISL", "brk_name": "Iceland", "brk_group": null, "abbrev": "Iceland", "postal": "IS", "formal_en": "Republic of Iceland", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Iceland", "name_alt": null, "mapcolor7": 1, "mapcolor8": 4, "mapcolor9": 4, "mapcolor13": 9, "pop_est": 306694, "gdp_md_est": 12710, "pop_year": -99, "lastcensus": -99, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "IS", "iso_a3": "ISL", "iso_n3": "352", "un_a3": "352", "wb_a2": "IS", "wb_a3": "ISL", "woe_id": -99, "adm0_a3_is": "ISL", "adm0_a3_us": "ISL", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 7, "tiny": -99, "homepart": 1}, "arcs": [[79]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Italy", "sov_a3": "ITA", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Italy", "adm0_a3": "ITA", "geou_dif": 0, "geounit": "Italy", "gu_a3": "ITA", "su_dif": 0, "subunit": "Italy", "su_a3": "ITA", "brk_diff": 0, "name": "Italy", "name_long": "Italy", "brk_a3": "ITA", "brk_name": "Italy", "brk_group": null, "abbrev": "Italy", "postal": "I", "formal_en": "Italian Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Italy", "name_alt": null, "mapcolor7": 6, "mapcolor8": 7, "mapcolor9": 8, "mapcolor13": 7, "pop_est": 58126212, "gdp_md_est": 1823000, "pop_year": -99, "lastcensus": 2012, "gdp_year": -99, "economy": "1. Developed region: G7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "IT", "iso_a3": "ITA", "iso_n3": "380", "un_a3": "380", "wb_a2": "IT", "wb_a3": "ITA", "woe_id": -99, "adm0_a3_is": "ITA", "adm0_a3_us": "ITA", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 5, "long_len": 5, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[[80]], [[81]], [[82, 83, -60, -31, -8]]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Kosovo", "sov_a3": "KOS", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Kosovo", "adm0_a3": "KOS", "geou_dif": 0, "geounit": "Kosovo", "gu_a3": "KOS", "su_dif": 0, "subunit": "Kosovo", "su_a3": "KOS", "brk_diff": 1, "name": "Kosovo", "name_long": "Kosovo", "brk_a3": "B57", "brk_name": "Kosovo", "brk_group": null, "abbrev": "<PERSON><PERSON><PERSON>", "postal": "KO", "formal_en": "Republic of Kosovo", "formal_fr": null, "note_adm0": null, "note_brk": "Self admin.; Claimed by Serbia", "name_sort": "Kosovo", "name_alt": null, "mapcolor7": 2, "mapcolor8": 2, "mapcolor9": 3, "mapcolor13": 11, "pop_est": 1804838, "gdp_md_est": 5352, "pop_year": -99, "lastcensus": 1981, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "4. Lower middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "-99", "iso_a3": "-99", "iso_n3": "-99", "un_a3": "-099", "wb_a2": "KV", "wb_a3": "KSV", "woe_id": -99, "adm0_a3_is": "SRB", "adm0_a3_us": "KOS", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-5, 84, 85, 86]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Lithuania", "sov_a3": "LTU", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Lithuania", "adm0_a3": "LTU", "geou_dif": 0, "geounit": "Lithuania", "gu_a3": "LTU", "su_dif": 0, "subunit": "Lithuania", "su_a3": "LTU", "brk_diff": 0, "name": "Lithuania", "name_long": "Lithuania", "brk_a3": "LTU", "brk_name": "Lithuania", "brk_group": null, "abbrev": "Lith.", "postal": "LT", "formal_en": "Republic of Lithuania", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Lithuania", "name_alt": null, "mapcolor7": 6, "mapcolor8": 3, "mapcolor9": 3, "mapcolor13": 9, "pop_est": 3555179, "gdp_md_est": 63330, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "LT", "iso_a3": "LTU", "iso_n3": "440", "un_a3": "440", "wb_a2": "LT", "wb_a3": "LTU", "woe_id": -99, "adm0_a3_is": "LTU", "adm0_a3_us": "LTU", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 9, "long_len": 9, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[87, 88, 89, -26, 90]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Luxembourg", "sov_a3": "LUX", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Luxembourg", "adm0_a3": "LUX", "geou_dif": 0, "geounit": "Luxembourg", "gu_a3": "LUX", "su_dif": 0, "subunit": "Luxembourg", "su_a3": "LUX", "brk_diff": 0, "name": "Luxembourg", "name_long": "Luxembourg", "brk_a3": "LUX", "brk_name": "Luxembourg", "brk_group": null, "abbrev": "<PERSON><PERSON>.", "postal": "L", "formal_en": "Grand Duchy of Luxembourg", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Luxembourg", "name_alt": null, "mapcolor7": 1, "mapcolor8": 7, "mapcolor9": 3, "mapcolor13": 7, "pop_est": 491775, "gdp_md_est": 39370, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "LU", "iso_a3": "LUX", "iso_n3": "442", "un_a3": "442", "wb_a2": "LU", "wb_a3": "LUX", "woe_id": -99, "adm0_a3_is": "LUX", "adm0_a3_us": "LUX", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 10, "long_len": 10, "abbrev_len": 4, "tiny": 5, "homepart": 1}, "arcs": [[-43, -59, -15]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Latvia", "sov_a3": "LVA", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Latvia", "adm0_a3": "LVA", "geou_dif": 0, "geounit": "Latvia", "gu_a3": "LVA", "su_dif": 0, "subunit": "Latvia", "su_a3": "LVA", "brk_diff": 0, "name": "Latvia", "name_long": "Latvia", "brk_a3": "LVA", "brk_name": "Latvia", "brk_group": null, "abbrev": "Lat.", "postal": "LV", "formal_en": "Republic of Latvia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Latvia", "name_alt": null, "mapcolor7": 4, "mapcolor8": 7, "mapcolor9": 6, "mapcolor13": 13, "pop_est": 2231503, "gdp_md_est": 38860, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "LV", "iso_a3": "LVA", "iso_n3": "428", "un_a3": "428", "wb_a2": "LV", "wb_a3": "LVA", "woe_id": -99, "adm0_a3_is": "LVA", "adm0_a3_us": "LVA", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[91, -52, 92, -27, -90]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Moldova", "sov_a3": "MDA", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Moldova", "adm0_a3": "MDA", "geou_dif": 0, "geounit": "Moldova", "gu_a3": "MDA", "su_dif": 0, "subunit": "Moldova", "su_a3": "MDA", "brk_diff": 0, "name": "Moldova", "name_long": "Moldova", "brk_a3": "MDA", "brk_name": "Moldova", "brk_group": null, "abbrev": "Mda.", "postal": "MD", "formal_en": "Republic of Moldova", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Moldova", "name_alt": null, "mapcolor7": 3, "mapcolor8": 5, "mapcolor9": 4, "mapcolor13": 12, "pop_est": 4320748, "gdp_md_est": 10670, "pop_year": -99, "lastcensus": 2004, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "4. Lower middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "MD", "iso_a3": "MDA", "iso_n3": "498", "un_a3": "498", "wb_a2": "MD", "wb_a3": "MDA", "woe_id": -99, "adm0_a3_is": "MDA", "adm0_a3_us": "MDA", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[93, 94]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Macedonia", "sov_a3": "MKD", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Macedonia", "adm0_a3": "MKD", "geou_dif": 0, "geounit": "Macedonia", "gu_a3": "MKD", "su_dif": 0, "subunit": "Macedonia", "su_a3": "MKD", "brk_diff": 0, "name": "Macedonia", "name_long": "Macedonia", "brk_a3": "MKD", "brk_name": "Macedonia", "brk_group": null, "abbrev": "Mkd.", "postal": "MK", "formal_en": "Former Yugoslav Republic of Macedonia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Macedonia, FYR", "name_alt": null, "mapcolor7": 5, "mapcolor8": 3, "mapcolor9": 7, "mapcolor13": 3, "pop_est": 2066718, "gdp_md_est": 18780, "pop_year": -99, "lastcensus": 2010, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "MK", "iso_a3": "MKD", "iso_n3": "807", "un_a3": "807", "wb_a2": "MK", "wb_a3": "MKD", "woe_id": -99, "adm0_a3_is": "MKD", "adm0_a3_us": "MKD", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 9, "long_len": 9, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-87, 95, -21, -67, -1]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Montenegro", "sov_a3": "MNE", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Montenegro", "adm0_a3": "MNE", "geou_dif": 0, "geounit": "Montenegro", "gu_a3": "MNE", "su_dif": 0, "subunit": "Montenegro", "su_a3": "MNE", "brk_diff": 0, "name": "Montenegro", "name_long": "Montenegro", "brk_a3": "MNE", "brk_name": "Montenegro", "brk_group": null, "abbrev": "Mont.", "postal": "ME", "formal_en": "Montenegro", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Montenegro", "name_alt": null, "mapcolor7": 4, "mapcolor8": 1, "mapcolor9": 4, "mapcolor13": 5, "pop_est": 672180, "gdp_md_est": 6816, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "ME", "iso_a3": "MNE", "iso_n3": "499", "un_a3": "499", "wb_a2": "ME", "wb_a3": "MNE", "woe_id": -99, "adm0_a3_is": "MNE", "adm0_a3_us": "MNE", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 10, "long_len": 10, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[96, -70, -24, 97, -85, -4]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Netherlands", "sov_a3": "NL1", "adm0_dif": 1, "level": 2, "type": "Country", "admin": "Netherlands", "adm0_a3": "NLD", "geou_dif": 0, "geounit": "Netherlands", "gu_a3": "NLD", "su_dif": 0, "subunit": "Netherlands", "su_a3": "NLD", "brk_diff": 0, "name": "Netherlands", "name_long": "Netherlands", "brk_a3": "NLD", "brk_name": "Netherlands", "brk_group": null, "abbrev": "<PERSON><PERSON>.", "postal": "NL", "formal_en": "Kingdom of the Netherlands", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Netherlands", "name_alt": null, "mapcolor7": 4, "mapcolor8": 2, "mapcolor9": 2, "mapcolor13": 9, "pop_est": 16715999, "gdp_md_est": 672000, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "NL", "iso_a3": "NLD", "iso_n3": "528", "un_a3": "528", "wb_a2": "NL", "wb_a3": "NLD", "woe_id": -99, "adm0_a3_is": "NLD", "adm0_a3_us": "NLD", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Western Europe", "region_wb": "Europe & Central Asia", "name_len": 11, "long_len": 11, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[-44, -13, 98]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Norway", "sov_a3": "NOR", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Norway", "adm0_a3": "NOR", "geou_dif": 0, "geounit": "Norway", "gu_a3": "NOR", "su_dif": 0, "subunit": "Norway", "su_a3": "NOR", "brk_diff": 0, "name": "Norway", "name_long": "Norway", "brk_a3": "NOR", "brk_name": "Norway", "brk_group": null, "abbrev": "Nor.", "postal": "N", "formal_en": "Kingdom of Norway", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Norway", "name_alt": null, "mapcolor7": 5, "mapcolor8": 3, "mapcolor9": 8, "mapcolor13": 12, "pop_est": 4676305, "gdp_md_est": 276400, "pop_year": -99, "lastcensus": 2001, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "NO", "iso_a3": "NOR", "iso_n3": "578", "un_a3": "578", "wb_a2": "NO", "wb_a3": "NOR", "woe_id": -99, "adm0_a3_is": "NOR", "adm0_a3_us": "NOR", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[[99, -56, 100, 101]], [[102]], [[103]], [[104]]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Poland", "sov_a3": "POL", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Poland", "adm0_a3": "POL", "geou_dif": 0, "geounit": "Poland", "gu_a3": "POL", "su_dif": 0, "subunit": "Poland", "su_a3": "POL", "brk_diff": 0, "name": "Poland", "name_long": "Poland", "brk_a3": "POL", "brk_name": "Poland", "brk_group": null, "abbrev": "Pol.", "postal": "PL", "formal_en": "Republic of Poland", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Poland", "name_alt": null, "mapcolor7": 3, "mapcolor8": 7, "mapcolor9": 1, "mapcolor13": 2, "pop_est": 38482919, "gdp_md_est": 667900, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "PL", "iso_a3": "POL", "iso_n3": "616", "un_a3": "616", "wb_a2": "PL", "wb_a3": "POL", "woe_id": -99, "adm0_a3_is": "POL", "adm0_a3_us": "POL", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-41, 105, 106, -91, -30, 107, 108, -35]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Portugal", "sov_a3": "PRT", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Portugal", "adm0_a3": "PRT", "geou_dif": 0, "geounit": "Portugal", "gu_a3": "PRT", "su_dif": 1, "subunit": "Portugal", "su_a3": "PR1", "brk_diff": 0, "name": "Portugal", "name_long": "Portugal", "brk_a3": "PR1", "brk_name": "Portugal", "brk_group": null, "abbrev": "Port.", "postal": "P", "formal_en": "Portuguese Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Portugal", "name_alt": null, "mapcolor7": 1, "mapcolor8": 7, "mapcolor9": 1, "mapcolor13": 4, "pop_est": 10707924, "gdp_md_est": 208627, "pop_year": -99, "lastcensus": 2011, "gdp_year": 0, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "PT", "iso_a3": "PRT", "iso_n3": "620", "un_a3": "620", "wb_a2": "PT", "wb_a3": "PRT", "woe_id": -99, "adm0_a3_is": "PRT", "adm0_a3_us": "PRT", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 8, "long_len": 8, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[-49, 109]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Romania", "sov_a3": "ROU", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Romania", "adm0_a3": "ROU", "geou_dif": 0, "geounit": "Romania", "gu_a3": "ROU", "su_dif": 0, "subunit": "Romania", "su_a3": "ROU", "brk_diff": 0, "name": "Romania", "name_long": "Romania", "brk_a3": "ROU", "brk_name": "Romania", "brk_group": null, "abbrev": "Rom.", "postal": "RO", "formal_en": "Romania", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Romania", "name_alt": null, "mapcolor7": 1, "mapcolor8": 4, "mapcolor9": 3, "mapcolor13": 13, "pop_est": 22215421, "gdp_md_est": 271400, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "RO", "iso_a3": "ROU", "iso_n3": "642", "un_a3": "642", "wb_a2": "RO", "wb_a3": "ROM", "woe_id": -99, "adm0_a3_is": "ROU", "adm0_a3_us": "ROU", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[110, -95, 111, 112, -18, 113, -76]]}, {"type": "MultiPolygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 2, "sovereignt": "Russia", "sov_a3": "RUS", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Russia", "adm0_a3": "RUS", "geou_dif": 0, "geounit": "Russia", "gu_a3": "RUS", "su_dif": 0, "subunit": "Russia", "su_a3": "RUS", "brk_diff": 0, "name": "Russia", "name_long": "Russian Federation", "brk_a3": "RUS", "brk_name": "Russia", "brk_group": null, "abbrev": "Rus.", "postal": "RUS", "formal_en": "Russian Federation", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Russian Federation", "name_alt": null, "mapcolor7": 2, "mapcolor8": 5, "mapcolor9": 7, "mapcolor13": 7, "pop_est": 140041247, "gdp_md_est": 2266000, "pop_year": -99, "lastcensus": 2010, "gdp_year": -99, "economy": "3. Emerging region: BRIC", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "RU", "iso_a3": "RUS", "iso_n3": "643", "un_a3": "643", "wb_a2": "RU", "wb_a3": "RUS", "woe_id": -99, "adm0_a3_is": "RUS", "adm0_a3_us": "RUS", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 18, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[[114]], [[-107, 115, -88]], [], [], [[116]], [[117]], [[118]], [[119]], [[120]], [[121, -28, -93, -51, 122, -53, -100, 123]], [[124]], [[125]], [[126]]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 5, "sovereignt": "Republic of Serbia", "sov_a3": "SRB", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Republic of Serbia", "adm0_a3": "SRB", "geou_dif": 0, "geounit": "Republic of Serbia", "gu_a3": "SRB", "su_dif": 0, "subunit": "Republic of Serbia", "su_a3": "SRB", "brk_diff": 0, "name": "Serbia", "name_long": "Serbia", "brk_a3": "SRB", "brk_name": "Serbia", "brk_group": null, "abbrev": "Serb.", "postal": "RS", "formal_en": "Republic of Serbia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Serbia", "name_alt": null, "mapcolor7": 3, "mapcolor8": 3, "mapcolor9": 2, "mapcolor13": 10, "pop_est": 7379339, "gdp_md_est": 80340, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "3. Upper middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "RS", "iso_a3": "SRB", "iso_n3": "688", "un_a3": "688", "wb_a2": "YF", "wb_a3": "SRB", "woe_id": -99, "adm0_a3_is": "SRB", "adm0_a3_us": "SRB", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 5, "tiny": -99, "homepart": 1}, "arcs": [[-22, -96, -86, -98, -23, -69, -77, -114]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Slovakia", "sov_a3": "SVK", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Slovakia", "adm0_a3": "SVK", "geou_dif": 0, "geounit": "Slovakia", "gu_a3": "SVK", "su_dif": 0, "subunit": "Slovakia", "su_a3": "SVK", "brk_diff": 0, "name": "Slovakia", "name_long": "Slovakia", "brk_a3": "SVK", "brk_name": "Slovakia", "brk_group": null, "abbrev": "Svk.", "postal": "SK", "formal_en": "Slovak Republic", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Slovak Republic", "name_alt": null, "mapcolor7": 2, "mapcolor8": 4, "mapcolor9": 4, "mapcolor13": 9, "pop_est": 5463046, "gdp_md_est": 119500, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "SK", "iso_a3": "SVK", "iso_n3": "703", "un_a3": "703", "wb_a2": "SK", "wb_a3": "SVK", "woe_id": -99, "adm0_a3_is": "SVK", "adm0_a3_us": "SVK", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 8, "long_len": 8, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-109, 127, -74, -12, -36]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Sweden", "sov_a3": "SWE", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Sweden", "adm0_a3": "SWE", "geou_dif": 0, "geounit": "Sweden", "gu_a3": "SWE", "su_dif": 0, "subunit": "Sweden", "su_a3": "SWE", "brk_diff": 0, "name": "Sweden", "name_long": "Sweden", "brk_a3": "SWE", "brk_name": "Sweden", "brk_group": null, "abbrev": "Swe.", "postal": "S", "formal_en": "Kingdom of Sweden", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Sweden", "name_alt": null, "mapcolor7": 1, "mapcolor8": 4, "mapcolor9": 2, "mapcolor13": 4, "pop_est": 9059651, "gdp_md_est": 344300, "pop_year": -99, "lastcensus": -99, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "SE", "iso_a3": "SWE", "iso_n3": "752", "un_a3": "752", "wb_a2": "SE", "wb_a3": "SWE", "woe_id": -99, "adm0_a3_is": "SWE", "adm0_a3_us": "SWE", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Northern Europe", "region_wb": "Europe & Central Asia", "name_len": 6, "long_len": 6, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-101, -55, 128]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 6, "sovereignt": "Slovenia", "sov_a3": "SVN", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Slovenia", "adm0_a3": "SVN", "geou_dif": 0, "geounit": "Slovenia", "gu_a3": "SVN", "su_dif": 0, "subunit": "Slovenia", "su_a3": "SVN", "brk_diff": 0, "name": "Slovenia", "name_long": "Slovenia", "brk_a3": "SVN", "brk_name": "Slovenia", "brk_group": null, "abbrev": "Slo.", "postal": "SLO", "formal_en": "Republic of Slovenia", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Slovenia", "name_alt": null, "mapcolor7": 2, "mapcolor8": 3, "mapcolor9": 2, "mapcolor13": 12, "pop_est": 2005692, "gdp_md_est": 59340, "pop_year": -99, "lastcensus": 2011, "gdp_year": -99, "economy": "2. Developed region: nonG7", "income_grp": "1. High income: OECD", "wikipedia": -99, "fips_10": null, "iso_a2": "SI", "iso_a3": "SVN", "iso_n3": "705", "un_a3": "705", "wb_a2": "SI", "wb_a3": "SVN", "woe_id": -99, "adm0_a3_is": "SVN", "adm0_a3_us": "SVN", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Southern Europe", "region_wb": "Europe & Central Asia", "name_len": 8, "long_len": 8, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-7, -78, -72, 129, -83]]}, {"type": "Polygon", "properties": {"scalerank": 1, "featurecla": "Admin-0 country", "labelrank": 3, "sovereignt": "Ukraine", "sov_a3": "UKR", "adm0_dif": 0, "level": 2, "type": "Sovereign country", "admin": "Ukraine", "adm0_a3": "UKR", "geou_dif": 0, "geounit": "Ukraine", "gu_a3": "UKR", "su_dif": 0, "subunit": "Ukraine", "su_a3": "UKR", "brk_diff": 0, "name": "Ukraine", "name_long": "Ukraine", "brk_a3": "UKR", "brk_name": "Ukraine", "brk_group": null, "abbrev": "Ukr.", "postal": "UA", "formal_en": "Ukraine", "formal_fr": null, "note_adm0": null, "note_brk": null, "name_sort": "Ukraine", "name_alt": null, "mapcolor7": 5, "mapcolor8": 1, "mapcolor9": 6, "mapcolor13": 3, "pop_est": 45700395, "gdp_md_est": 339800, "pop_year": -99, "lastcensus": 2001, "gdp_year": -99, "economy": "6. Developing region", "income_grp": "4. Lower middle income", "wikipedia": -99, "fips_10": null, "iso_a2": "UA", "iso_a3": "UKR", "iso_n3": "804", "un_a3": "804", "wb_a2": "UA", "wb_a3": "UKR", "woe_id": -99, "adm0_a3_is": "UKR", "adm0_a3_us": "UKR", "adm0_a3_un": -99, "adm0_a3_wb": -99, "continent": "Europe", "region_un": "Europe", "subregion": "Eastern Europe", "region_wb": "Europe & Central Asia", "name_len": 7, "long_len": 7, "abbrev_len": 4, "tiny": -99, "homepart": 1}, "arcs": [[-122, 130, -112, -94, -111, -75, -128, -108, -29]]}]}}, "arcs": [[[5571, 5025], [-3, -43], [4, -54], [11, -31]], [[5583, 4897], [0, -33], [-9, -18], [-2, -41], [-13, -61]], [[5559, 4744], [-5, 8], [0, 28], [-15, 43], [-3, 60], [2, 86], [4, 39], [-4, 20]], [[5538, 5028], [-2, 40], [12, 62], [1, -23], [8, 11]], [[5557, 5118], [6, -34], [7, -13], [1, -46]], [[5471, 5817], [-2, -52], [-16, 0], [6, -28], [-9, -81]], [[5450, 5656], [-6, -21], [-24, -3], [-14, -29], [-23, 10]], [[5383, 5613], [-40, 32], [-6, 44], [-27, -22], [-4, -24], [-16, 18]], [[5290, 5661], [-15, 4], [-12, 23], [4, 31], [-1, 22]], [[5266, 5741], [8, 7], [14, -35], [4, 33], [25, -5], [20, 22], [13, -3], [9, -26], [2, 21], [-4, 82], [10, 17], [10, 58]], [[5377, 5912], [21, -41], [15, 52], [10, 9], [22, -38], [13, 6], [13, -24]], [[5471, 5876], [-3, -16], [3, -43]], [[5092, 6223], [20, -10], [26, 27], [17, -56], [16, -29]], [[5171, 6155], [-4, -85]], [[5167, 6070], [-7, -5], [-3, -71]], [[5157, 5994], [-24, 58], [-14, -10], [-20, 59], [-13, 51], [-13, 2], [-4, 44]], [[5069, 6198], [23, 25]], [[5629, 5326], [8, -52], [11, 9], [21, -20], [41, -6], [13, 32], [33, 29], [20, -46], [17, -13]], [[5793, 5259], [-15, -52], [-10, -91], [9, -72], [-24, 17], [-28, -39]], [[5725, 5022], [0, -63], [-26, -12], [-19, 44], [-22, -35], [-21, 4]], [[5637, 4960], [-2, 83], [-14, 41]], [[5621, 5084], [5, 18], [-3, 15], [4, 40], [11, 39], [-14, 55], [-2, 46], [7, 29]], [[5527, 5405], [10, 0], [-7, -56], [14, -48], [-4, -60], [-7, -5]], [[5533, 5236], [-5, -12], [-9, -29], [-4, -69]], [[5515, 5126], [-25, 47], [-10, 53], [-11, 28], [-12, 47], [-6, 39], [-14, 59], [6, 53], [10, -29], [6, 26], [13, 3], [24, -21], [19, 2], [12, -28]], [[5652, 6547], [27, 0], [30, 47], [6, 71], [23, 41], [-3, 56]], [[5735, 6762], [17, 22], [30, 48]], [[5782, 6832], [29, -31], [4, -32], [15, 15], [27, -30], [3, -59], [-6, -34], [17, -83], [12, -23], [-2, -22], [19, -23], [8, -33], [-11, -28], [-23, 4], [-5, -11], [7, -42], [6, -81]], [[5882, 6319], [-23, -8], [-9, -27], [-2, -64], [-11, 12], [-25, -6], [-7, 30], [-11, -22], [-10, 18], [-22, 3], [-31, 30], [-28, 10], [-22, -3], [-15, -34], [-13, -5]], [[5653, 6253], [-1, 56], [-8, 58], [17, 26], [0, 51], [-8, 48], [-1, 55]], [[5290, 5661], [-3, -51], [-12, -22], [-20, 16], [-6, -51], [-14, -4], [-5, 20], [-15, -43], [-13, -6], [-12, 27]], [[5190, 5547], [-10, 56], [-13, -20], [0, 57], [21, 71], [-1, 32], [12, -11], [8, 21]], [[5207, 5753], [24, -1], [5, 28], [30, -39]], [[5377, 5912], [-16, 54], [-14, 30], [-3, 54], [-5, 37], [21, 28], [10, 31], [20, 25], [7, 24], [7, -15], [13, 13]], [[5417, 6193], [13, -40], [21, -11], [-2, -35], [15, -26], [4, 32], [19, -14], [3, -39], [20, -8], [13, -62]], [[5523, 5990], [-8, 0], [-4, -23], [-7, -5], [-2, -29], [-5, -6], [-1, -12], [-9, -13], [-12, 2], [-4, -28]], [[5352, 6762], [-17, -102], [-29, 71], [-4, 52], [41, 42], [9, -63]], [[5275, 6683], [-18, -20], [-21, 17]], [[5236, 6680], [-11, 70], [-1, 129], [5, 34], [8, 38], [24, 8], [10, 35], [22, 35], [-1, -64], [-8, -42], [4, -35], [15, -19], [-7, -48], [-8, 14], [-20, -91], [7, -61]], [[5275, 6683], [1, -49], [28, -30], [-1, -44], [29, 23], [15, 35], [32, -50], [13, -40]], [[5392, 6528], [6, -64], [-8, -34], [11, -45], [6, -68], [-2, -43], [12, -81]], [[5207, 5753], [3, 90], [14, 86], [-40, 24], [-13, 33]], [[5171, 5986], [2, 55], [-6, 29]], [[5171, 6155], [-5, 132], [17, 0], [7, 48], [6, 115], [-5, 43]], [[5191, 6493], [6, 27], [23, 7], [5, -28], [19, 62], [-6, 47], [-2, 72]], [[4749, 5028], [1, 90], [-11, 55], [39, 91], [34, -23], [37, 1], [30, -21], [23, 6], [45, -4]], [[4947, 5223], [11, -49], [51, -57], [10, 27], [31, -57], [32, 16]], [[5082, 5103], [2, -73], [-26, -84], [-36, -27], [-2, -42], [-18, -70], [-10, -103], [11, -72], [-16, -57], [-6, -82], [-21, -25], [-20, -97], [-35, -2], [-27, 3], [-17, -45], [-11, -48], [-13, 11], [-11, 42], [-8, 73], [-26, 20]], [[4792, 4425], [-2, 41], [10, 48], [4, 34], [-9, 38], [7, 82], [-11, 76], [12, 11], [1, 59], [5, 19], [0, 98], [13, 34], [-8, 64], [-16, 4], [-5, -16], [-16, 0], [-7, 62], [-11, -19], [-10, -32]], [[5675, 7037], [3, 75], [-10, -16], [-18, 45], [-2, 72], [35, 36], [35, 18], [30, -21], [29, 4]], [[5777, 7250], [4, -22], [-20, -73], [8, -118], [-12, -40]], [[5757, 6997], [-22, 0], [-24, 47], [-13, 16], [-23, -23]], [[5794, 8461], [-4, -89], [42, -84], [-26, -95], [33, -144], [-19, -108], [25, -94], [-11, -82], [41, -87], [-11, -64], [-25, -73], [-60, -161]], [[5779, 7380], [-50, -10], [-49, -47], [-45, -26], [-16, 69], [-27, 41], [6, 124], [-14, 114], [14, 74], [25, 79], [63, 137], [19, 26], [-3, 54], [-39, 59]], [[5663, 8074], [-9, 50], [-1, 194], [-43, 86], [-37, 62]], [[5573, 8466], [17, 33], [30, -67], [37, 7], [30, -31], [26, 56], [14, 93], [43, 42], [35, -50], [-11, -88]], [[3540, 57], [-11, -48], [-13, -9], [-4, 36], [-6, 5], [-9, -34], [-12, 26], [7, 54], [3, 57], [4, 54], [-10, 75], [-3, 86], [15, 109], [9, -14], [21, -30], [29, -107], [5, -52], [-17, -115], [-8, -93]], [[5265, 5063], [-9, -98], [-13, 26], [-6, 85], [5, 47], [18, 48], [5, -108]], [[5157, 5994], [6, -11], [8, 3]], [[5190, 5547], [-2, -35], [9, -48], [-10, -38], [7, -98], [15, -16], [-3, -55]], [[5206, 5257], [-25, -71], [-55, 34], [-40, -41], [-4, -76]], [[4947, 5223], [14, 76], [5, 251], [-28, 133], [-21, 64], [-42, 48], [-3, 92], [36, 28], [47, -33], [-9, 143], [26, -54], [65, 99], [8, 103], [24, 25]], [[4827, 6542], [-21, 26], [-17, -2], [6, 68], [-6, 67]], [[4789, 6701], [23, 6], [30, -78], [-15, -87]], [[4916, 7144], [-30, -137], [29, 17], [30, 0], [-7, -103], [-25, -113], [29, -8], [2, -14], [25, -149], [19, -20], [17, -144], [8, -50], [33, -24], [-3, -80], [-14, -37], [11, -66], [-25, -66], [-37, 1], [-48, -34], [-13, 24], [-18, -59], [-26, 15], [-19, -49], [-15, 26], [41, 132], [25, 28], [-1, 0], [-43, 21], [-8, 50], [29, 39], [-15, 68], [5, 83], [42, -12], [4, 74], [-19, 77], [0, 2], [-34, 22], [-7, 35], [10, 56], [-9, 35], [-15, -60], [-1, 122], [-14, 64], [10, 131], [21, 102], [23, -10], [33, 11]], [[5658, 4249], [15, -43], [22, 7], [20, -9], [0, -22], [15, 16], [-4, -38], [-40, -10], [1, 20], [-34, 25], [5, 54]], [[5583, 4897], [18, 12], [11, 27], [15, -2], [5, 22], [5, 4]], [[5725, 5022], [13, -34], [-8, -79], [-7, -14], [-17, 4], [-14, 12], [-34, -33], [19, -71], [-14, -21], [-15, 0], [-15, 65], [-5, -28], [6, -75], [14, -59], [-10, -28], [15, -58], [14, -37], [0, -71], [-25, 33], [8, -64], [-18, -13], [11, -112], [-19, -1], [-23, 55], [-10, 101], [-5, 84], [-11, 58], [-14, 72], [-2, 36]], [[5522, 5537], [7, -49], [9, -36], [-11, -47]], [[5515, 5126], [-3, -22]], [[5512, 5104], [-26, 47], [-16, 45], [-26, 38], [-23, 93], [6, 9], [-13, 53], [-1, 43], [-17, 20], [-9, -55], [-8, 43], [0, 43], [1, 2]], [[5380, 5485], [20, -4], [5, 21], [9, -20], [11, -3], [0, 36], [10, 13], [2, 51], [23, 33]], [[5460, 5612], [8, -15], [21, -55], [23, -24], [10, 19]], [[5471, 5817], [14, -33], [10, -14], [24, 16], [2, 25], [11, 4], [14, 20], [3, -8], [13, 15], [6, 30], [9, 8], [30, -39], [6, 13]], [[5613, 5854], [15, -34], [2, -34]], [[5630, 5786], [-17, -26], [-13, -86], [-17, -86], [-22, -23]], [[5561, 5565], [-17, 5], [-22, -33]], [[5460, 5612], [-6, 43], [-4, 1]], [[4827, 6542], [5, -90], [-21, -113], [-49, -75], [-40, 19], [23, 132], [-15, 129], [38, 98], [21, 59]], [[4597, 8131], [-7, -82], [31, -86], [-36, -96], [-80, -87], [-24, -23], [-36, 19], [-78, 40], [28, 56], [-61, 62], [49, 24], [-1, 37], [-58, 29], [19, 83], [42, 18], [43, -85], [42, 68], [35, -35], [45, 67], [47, -9]], [[5431, 4568], [-10, -100], [4, -39], [-6, -65], [-21, 48], [-14, 13], [-39, 65], [4, 64], [32, -11], [28, 14], [22, 11]], [[5255, 4944], [17, -90], [-4, -167], [-13, 8], [-11, -42], [-10, 33], [-2, 153], [-6, 72], [15, -6], [14, 39]], [[5383, 5613], [-3, -62], [7, -54]], [[5387, 5497], [-22, 18], [-23, -45], [1, -62], [-3, -36], [9, -65], [26, -63], [14, -104], [31, -102], [22, 1], [7, -28], [-8, -25], [25, -46], [20, -38], [24, -66], [3, -24], [-5, -45], [-16, 59], [-24, 21], [-12, -82], [20, -47], [-3, -66], [-11, -7], [-15, -108], [-12, -10], [0, 39], [6, 67], [6, 27], [-11, 73], [-8, 64], [-12, 16], [-8, 54], [-18, 23], [-12, 51], [-21, 8], [-21, 57], [-26, 82], [-19, 73], [-8, 125], [-14, 15], [-23, 41], [-12, -17], [-16, -58], [-12, -10]], [[5557, 5118], [5, 28]], [[5562, 5146], [7, 9], [4, 42], [5, 7], [4, -18], [5, -8], [3, -20], [5, -6], [5, -23], [4, 1], [-3, -31], [-3, -15], [1, -10]], [[5599, 5074], [-6, -4], [-17, -20], [-1, -26], [-4, 1]], [[5631, 6600], [-2, 32], [3, 35], [-13, 20], [-29, 22]], [[5590, 6709], [-6, 106]], [[5584, 6815], [32, 39], [47, -8], [27, 12], [4, -26], [15, -8], [26, -62]], [[5652, 6547], [-7, 39], [-14, 14]], [[5584, 6815], [1, 95], [14, 79], [26, 43], [22, -94], [22, 3], [6, 96]], [[5757, 6997], [14, -29], [2, -61], [9, -75]], [[5739, 5829], [6, 18], [19, 13], [20, -39], [12, -5], [12, -34], [-2, -43], [11, -21], [4, -52], [9, -32], [-2, -19], [5, -13], [-7, -9], [-16, 3], [-3, 18], [-6, -10], [2, -23], [-7, -40], [-5, -43], [-7, -14]], [[5784, 5484], [-5, 57], [3, 54], [-1, 56], [-16, 75], [-9, 53], [-9, 38], [-8, 12]], [[5599, 5074], [9, 8], [13, 2]], [[5538, 5028], [-6, 10], [-8, 41], [-12, 25]], [[5533, 5236], [8, -22], [4, -17], [9, -14], [10, -26], [-2, -11]], [[5092, 6223], [14, 35], [24, 186], [38, 53], [23, -4]], [[5863, 8523], [-47, -51], [-22, -11]], [[5573, 8466], [-17, -5], [-4, -84], [-53, 21], [-7, -71], [-27, 1], [-18, -90], [-28, -140], [-43, -178], [10, -43], [-10, -50], [-27, 2], [-18, -118], [2, -168], [17, -64], [-9, -148], [-23, -87], [-12, -72]], [[5306, 7172], [-19, 77], [-55, -146], [-37, -30], [-38, 65], [-10, 136], [-9, 291], [26, 81], [73, 106], [55, 130], [51, 176], [66, 244], [47, 95], [76, 159], [61, 55], [46, -7], [42, 105], [51, -6], [50, 25], [87, -92], [-36, -34], [30, -79]], [[5686, 9570], [-62, -51], [-49, 29], [19, 32], [-16, 41], [57, 25], [11, -47], [40, -29]], [[5506, 9803], [92, -94], [-70, -49], [-15, -93], [-25, -24], [-13, -105], [-34, -5], [-59, 77], [25, 45], [-42, 37], [-54, 106], [-21, 99], [75, 45], [16, -44], [39, 2], [11, 43], [40, 5], [35, -45]], [[5706, 9893], [55, -45], [-41, -68], [-81, -15], [-82, 21], [-5, 35], [-40, 2], [-30, 58], [86, 36], [40, -31], [28, 38], [70, -31]], [[5392, 6528], [19, 37], [43, 58], [35, 43], [28, -21], [2, -31], [27, -2]], [[5546, 6612], [34, -14], [51, 2]], [[5653, 6253], [14, -110], [-3, -36], [-14, -15], [-25, -105], [7, -56], [-6, 7]], [[5626, 5938], [-26, 49], [-20, -18], [-13, 13], [-17, -27], [-14, 44], [-11, -17], [-2, 8]], [[4792, 4425], [-11, -33], [-14, 18], [-15, -14], [5, 98], [-3, 78], [-12, 12], [-7, 48], [2, 82], [11, 46], [2, 51], [6, 76], [-1, 53], [-5, 46], [-1, 42]], [[5630, 5786], [12, 27], [17, -14], [18, 0], [13, -31], [10, 19], [20, 12], [7, 30], [12, 0]], [[5784, 5484], [12, -23], [13, 20], [13, -22]], [[5822, 5459], [0, -32], [-13, -27], [-9, 11], [-7, -152]], [[5629, 5326], [-5, 22], [6, 21], [-7, 16], [-8, -29], [-17, 37], [-2, 52], [-17, 30], [-3, 40], [-15, 50]], [[8989, 6148], [28, -224], [-41, 42], [-17, -182], [27, -130], [-1, -88], [-21, 76], [-18, -98], [-5, 106], [3, 123], [-3, 136], [6, 96], [2, 169], [-17, 124], [3, 172], [25, 58], [-11, 59], [13, 18], [7, -84], [10, -121], [-1, -125], [11, -127]], [[5546, 6612], [6, 56], [38, 41]], [[9999, 8684], [-30, -7], [-5, 40], [-9964, 53], [4, 5], [23, 0], [40, -36], [-2, -17], [-29, -31], [-36, -7], [9999, 0]], [[8988, 8984], [-42, -1], [-57, 14], [-5, 7], [27, 50], [34, 12], [40, -49], [3, -33]], [[9186, 9220], [-32, -49], [-44, 11], [-52, 50], [7, 41], [51, -19], [70, -34]], [[9029, 9281], [-22, -94], [-102, 4], [-46, -30], [-55, 82], [15, 87], [37, 23], [73, -5], [100, -67]], [[6598, 8670], [-17, -12], [-91, 17], [-7, 56], [-50, 34], [-4, 68], [28, 27], [-1, 69], [55, 108], [-25, 15], [66, 111], [-7, 57], [62, 67], [91, 81], [93, 24], [48, 46], [54, 17], [19, -50], [-19, -39], [-98, -63], [-85, -60], [-86, -120], [-42, -124], [-43, -121], [5, -105], [54, -103]], [[6061, 5688], [1, 56], [14, 35], [27, 9], [5, 42], [-7, 70], [12, 66], [-1, 37], [-41, 41], [-16, -1], [-17, 59], [-21, -20], [-35, 44], [0, 25], [-10, 55], [-22, 6], [-2, 39], [7, 26], [-18, 71], [-29, -12], [-8, 6], [-7, -28], [-11, 5]], [[5777, 7250], [31, 70], [-29, 60]], [[5863, 8523], [29, 44], [46, -77], [76, -30], [105, -142], [21, -60], [2, -84], [-31, -67], [-45, -33], [-124, 95], [-21, -16], [45, -92], [2, -59], [2, -129], [36, -38], [22, -33], [3, 61], [-17, 55], [18, 47], [67, -78], [24, 31], [-19, 92], [65, 124], [25, -8], [26, -44], [16, 87], [-23, 75], [14, 76], [-21, 78], [78, -40], [16, -71], [-35, -16], [0, -70], [22, -43], [43, 27], [7, 81], [58, 60], [97, 108], [20, -6], [-27, -77], [35, -13], [19, 43], [52, 4], [42, 52], [31, -76], [32, 84], [-29, 73], [14, 42], [82, -39], [39, -39], [100, -144], [19, 66], [-28, 67], [-1, 26], [-34, 13], [10, 60], [-15, 98], [-1, 40], [51, 115], [18, 115], [21, 24], [74, -33], [5, -70], [-26, -103], [17, -40], [9, -88], [-6, -173], [31, -77], [-12, -85], [-55, -179], [32, -19], [11, 46], [31, 32], [7, 63], [24, 60], [-16, 72], [13, 83], [-31, 10], [-6, 71], [22, 126], [-36, 103], [50, 85], [-7, 90], [14, 3], [15, -70], [-11, -122], [29, -23], [-12, 91], [46, 50], [58, 7], [51, -72], [-25, 105], [-2, 134], [48, 26], [67, -6], [60, 17], [-23, 66], [33, 83], [31, 3], [54, 63], [74, 16], [9, 35], [73, 12], [23, -29], [62, 68], [51, -3], [8, 55], [26, 54], [66, 51], [48, -40], [-38, -32], [63, -19], [7, -62], [25, 30], [82, -1], [62, -62], [23, -47], [-7, -66], [-31, -37], [-73, -70], [-21, -38], [35, -17], [41, -32], [25, 24], [14, -81], [12, 32], [44, 20], [90, -20], [6, -60], [116, -18], [2, 96], [59, -22], [44, 1], [45, -67], [13, -81], [-17, -52], [35, -100], [44, -51], [27, 132], [44, -56], [48, 34], [53, -39], [21, 35], [45, -18], [-20, 118], [37, 54], [251, -82], [24, -75], [72, -96], [112, 24], [56, -21], [23, -52], [-4, -93], [35, -35], [37, 25], [49, 4], [52, -25], [53, 14], [49, -112], [34, 40], [-23, 81], [13, 56], [88, -35], [58, 7], [80, -60], [-9960, -55], [68, -97], [73, -125], [-3, -79], [19, -31], [-6, 92], [75, -19], [55, -118], [-28, -55], [-46, -13], [0, -124], [-11, -26], [-26, 4], [-22, 44], [-36, 37], [-7, 54], [-28, 21], [-31, -16], [-16, 44], [6, 47], [-33, -30], [13, -60], [-16, -53], [0, -1], [9963, -55], [-36, 9], [25, -67], [17, -104], [13, -34], [3, -53], [-7, -33], [-52, 27], [-78, -95], [-25, -14], [-42, -89], [-40, -78], [-11, -57], [-39, 87], [-73, -99], [-12, 47], [-27, -54], [-37, 17], [-9, -83], [-33, -122], [1, -51], [31, -28], [-4, -184], [-25, -5], [-12, -105], [11, -55], [-48, -64], [-10, -144], [-41, -31], [-9, -128], [-40, -118], [-10, 87], [-12, 184], [-15, 281], [13, 175], [23, 75], [2, 59], [43, 29], [50, 159], [47, 129], [50, 101], [23, 178], [-34, -11], [-17, -104], [-70, -138], [-23, 155], [-72, -43], [-69, -211], [23, -78], [-62, -33], [-43, -13], [2, 91], [-43, 20], [-35, -62], [-85, 21], [-91, -37], [-90, -247], [-106, -297], [43, -16], [14, -79], [27, -28], [18, 63], [30, -9], [40, -138], [1, -108], [-21, -126], [-3, -151], [-12, -202], [-42, -182], [-9, -88], [-38, -147], [-38, -145], [-18, -75], [-37, -74], [-17, -2], [-17, 62], [-38, -93], [-4, -42], [-4, 22], [0, 64], [14, 4], [4, 149], [-7, 108], [24, 45], [33, -23], [19, 123], [9, 139], [11, 46], [15, 113], [-46, -37], [-24, -50], [-42, 0], [-12, 119], [-32, 90], [-49, 40], [-10, 124], [-10, 77], [-10, 55], [-17, 127], [-25, 47], [-41, 37], [-37, -3], [-35, -23], [-23, -63], [16, -30], [0, -70], [-15, -40], [-26, -134], [1, -55], [-39, -80], [-34, 47], [-33, -10], [-14, 42], [-17, 14], [-41, -89], [-36, -21], [-26, -31], [-35, 20], [-26, -1], [-16, 64], [-28, 61], [-27, 17], [-36, -17], [-26, -23], [-39, 53], [-6, 95], [-32, 32], [-26, 15], [-31, 52], [-28, -131], [11, -74], [-27, -88], [-40, 32], [-28, 4], [-19, 59], [-29, 2], [-24, 39], [-42, -60], [-53, -108], [-29, -22], [-11, -11], [-15, 78], [-36, -17], [-11, 53], [-20, 25], [-13, 73], [-16, 22], [-39, -32], [-39, 73], [-15, -66], [-62, 320], [-35, 98], [10, 39], [-69, -119], [-27, -7], [2, 69], [-35, 43], [-29, -31], [-9, 131], [-50, 27], [-25, -52], [-70, -47], [-13, -31], [-104, -44], [-13, -43], [20, -86], [-26, -33], [5, -34], [-27, -62], [45, -87], [-7, -60], [-39, 6], [-8, -38], [-35, 66], [-44, -3], [-30, -53], [-33, 51], [-61, 87], [-43, -3], [-58, -137], [-3, -92], [-29, 73], [-22, -139], [8, -26], [-16, -95], [24, -86], [20, 4], [18, -85], [-3, -65], [14, -20], [-12, -75], [-27, -21], [-28, -130], [25, -120], [-2, -85], [30, -149], [-17, -51], [-4, -32], [-13, 9], [-19, 77], [-8, 4], [-17, 29], [-9, 52], [-25, 26], [-17, -19], [-5, 23], [-38, 61], [-41, 20], [-23, 22], [-4, -15], [-35, 106], [-32, 48], [-24, 74], [20, 20], [23, 106], [-15, 50], [41, 51], [-1, 28], [-25, -20]], [[7918, 9627], [-157, -48], [51, 165], [23, 15], [21, -9], [70, -71], [-8, -52]], [[6420, 9910], [-37, -16], [-25, -10], [-4, -21], [-33, -21], [-30, 30], [16, 40], [-62, 4], [54, 23], [43, 1], [5, -34], [16, 30], [26, 21], [42, -28], [-11, -19]], [[7775, 9700], [-60, -16], [-78, 37], [-46, 48], [-21, 90], [-38, 25], [72, 86], [60, 29], [54, -64], [64, -122], [-7, -113]], [[5626, 5938], [-8, -33], [-5, -51]], [[5663, 8074], [-47, -35], [-27, -88], [4, -78], [-44, -101], [-54, -109], [-20, -178], [20, -89], [26, -70], [-25, -142], [-29, -30], [-11, -212], [-15, -118], [-34, 12], [-16, -100], [-32, -6], [-9, 120], [-23, 143], [-21, 179]], [[5380, 5485], [7, 12]], [[6061, 5688], [-22, -10], [-18, -41], [-26, -7], [-24, -47], [1, -79], [14, -30], [28, 8], [-5, -45], [-31, -22], [-37, -73], [-16, 25], [6, 60], [-30, 37], [5, 24], [26, 42], [-8, 29], [-43, 32], [-2, 47], [-25, -16], [-11, -69], [-21, -94]]], "transform": {"scale": [0.036003600360036005, 0.007920493130611456], "translate": [-180, 2.053389187016037]}, "bbox": [-180, 2.053389187016037, 180, 81.2504]}