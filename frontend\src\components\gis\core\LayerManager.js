/**
 * Gestionnaire de couches pour OpenLayers
 * Module cartographique C2-EW
 */

import { Tile as Tile<PERSON>ayer, Vector as VectorLayer, Group as LayerGroup } from 'ol/layer';
import { OSM, XYZ, Vector as VectorSource, Cluster, TileWMS } from 'ol/source';
import { Style, Fill, Stroke, Circle, Text, Icon, RegularShape } from 'ol/style';
import { GeoJSON, KML } from 'ol/format';
import { bbox as bboxStrategy } from 'ol/loadingstrategy';
import { EventEmitter } from 'events';

/**
 * Gestionnaire de couches SIG
 */
export class LayerManager extends EventEmitter {
    constructor(gisEngine) {
        super();
        
        this.gisEngine = gisEngine;
        this.map = gisEngine.getMap();
        this.layers = new Map();
        this.layerGroups = new Map();
        this.layerOrder = [];
        
        this.init();
    }
    
    /**
     * Initialisation du gestionnaire
     */
    init() {
        this.createDefaultStyles();
        this.setupEventHandlers();
        
        console.log('✅ Gestionnaire de couches initialisé');
    }
    
    /**
     * Création des styles par défaut
     */
    createDefaultStyles() {
        this.defaultStyles = {
            // Style pour les équipements COMINT
            comint: new Style({
                image: new Circle({
                    radius: 8,
                    fill: new Fill({ color: '#2196F3' }),
                    stroke: new Stroke({ color: '#1976D2', width: 2 })
                }),
                text: new Text({
                    font: '12px Segoe UI',
                    fill: new Fill({ color: '#000' }),
                    stroke: new Stroke({ color: '#fff', width: 2 }),
                    offsetY: -20
                })
            }),
            
            // Style pour les équipements ELINT
            elint: new Style({
                image: new RegularShape({
                    points: 4,
                    radius: 10,
                    fill: new Fill({ color: '#FF9800' }),
                    stroke: new Stroke({ color: '#F57C00', width: 2 })
                }),
                text: new Text({
                    font: '12px Segoe UI',
                    fill: new Fill({ color: '#000' }),
                    stroke: new Stroke({ color: '#fff', width: 2 }),
                    offsetY: -20
                })
            }),
            
            // Style pour les systèmes anti-drone
            antiDrone: new Style({
                image: new RegularShape({
                    points: 3,
                    radius: 10,
                    fill: new Fill({ color: '#F44336' }),
                    stroke: new Stroke({ color: '#D32F2F', width: 2 })
                }),
                text: new Text({
                    font: '12px Segoe UI',
                    fill: new Fill({ color: '#000' }),
                    stroke: new Stroke({ color: '#fff', width: 2 }),
                    offsetY: -20
                })
            }),
            
            // Style pour les brouilleurs
            jammer: new Style({
                image: new RegularShape({
                    points: 6,
                    radius: 10,
                    fill: new Fill({ color: '#9C27B0' }),
                    stroke: new Stroke({ color: '#7B1FA2', width: 2 })
                }),
                text: new Text({
                    font: '12px Segoe UI',
                    fill: new Fill({ color: '#000' }),
                    stroke: new Stroke({ color: '#fff', width: 2 }),
                    offsetY: -20
                })
            }),
            
            // Style pour les capteurs
            sensor: new Style({
                image: new Circle({
                    radius: 6,
                    fill: new Fill({ color: '#4CAF50' }),
                    stroke: new Stroke({ color: '#388E3C', width: 2 })
                }),
                text: new Text({
                    font: '12px Segoe UI',
                    fill: new Fill({ color: '#000' }),
                    stroke: new Stroke({ color: '#fff', width: 2 }),
                    offsetY: -20
                })
            }),
            
            // Style pour le territoire marocain
            moroccoTerritory: new Style({
                fill: new Fill({
                    color: 'rgba(255, 0, 0, 0.1)'
                }),
                stroke: new Stroke({
                    color: '#FF0000',
                    width: 2
                })
            }),
            
            // Style pour les zones de visibilité
            visibilityArea: new Style({
                fill: new Fill({
                    color: 'rgba(76, 175, 80, 0.3)'
                }),
                stroke: new Stroke({
                    color: '#4CAF50',
                    width: 1
                })
            }),
            
            // Style pour les mesures
            measurement: new Style({
                fill: new Fill({
                    color: 'rgba(33, 150, 243, 0.2)'
                }),
                stroke: new Stroke({
                    color: '#2196F3',
                    width: 2,
                    lineDash: [5, 5]
                }),
                image: new Circle({
                    radius: 5,
                    fill: new Fill({ color: '#2196F3' }),
                    stroke: new Stroke({ color: '#fff', width: 2 })
                })
            })
        };
    }
    
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        // Écouter les changements de couches
        this.map.getLayers().on('add', (event) => {
            this.emit('layerAdded', event.element);
        });
        
        this.map.getLayers().on('remove', (event) => {
            this.emit('layerRemoved', event.element);
        });
    }
    
    /**
     * Créer une couche vectorielle
     */
    createVectorLayer(options = {}) {
        const {
            name,
            source,
            style,
            visible = true,
            opacity = 1.0,
            zIndex = 0,
            clustering = false,
            clusterDistance = 50
        } = options;
        
        let vectorSource;
        
        if (source instanceof VectorSource) {
            vectorSource = source;
        } else if (typeof source === 'string') {
            // URL vers des données GeoJSON
            vectorSource = new VectorSource({
                url: source,
                format: new GeoJSON(),
                strategy: bboxStrategy
            });
        } else {
            // Source vide
            vectorSource = new VectorSource();
        }
        
        // Clustering si activé
        if (clustering) {
            vectorSource = new Cluster({
                distance: clusterDistance,
                source: vectorSource
            });
        }
        
        const layer = new VectorLayer({
            source: vectorSource,
            style: style || this.getStyleForLayerType(name),
            visible,
            opacity,
            zIndex
        });
        
        layer.set('name', name);
        layer.set('type', 'vector');
        
        this.layers.set(name, layer);
        this.layerOrder.push(name);
        
        this.emit('layerCreated', { name, layer });
        
        return layer;
    }
    
    /**
     * Créer une couche de tuiles
     */
    createTileLayer(options = {}) {
        const {
            name,
            url,
            type = 'xyz',
            visible = true,
            opacity = 1.0,
            zIndex = 0,
            attribution,
            crossOrigin = 'anonymous'
        } = options;
        
        let source;
        
        switch (type) {
            case 'osm':
                source = new OSM({ url, crossOrigin });
                break;
            case 'wms':
                source = new TileWMS({
                    url,
                    params: options.params || {},
                    crossOrigin
                });
                break;
            case 'xyz':
            default:
                source = new XYZ({ 
                    url, 
                    crossOrigin,
                    attributions: attribution 
                });
                break;
        }
        
        const layer = new TileLayer({
            source,
            visible,
            opacity,
            zIndex
        });
        
        layer.set('name', name);
        layer.set('type', 'tile');
        
        this.layers.set(name, layer);
        this.layerOrder.push(name);
        
        this.emit('layerCreated', { name, layer });
        
        return layer;
    }
    
    /**
     * Créer une couche d'équipements
     */
    createEquipmentLayer(equipmentType) {
        const style = this.getStyleForEquipmentType(equipmentType);
        
        const layer = this.createVectorLayer({
            name: `equipment_${equipmentType}`,
            style,
            clustering: true,
            clusterDistance: 40
        });
        
        // Ajouter au groupe d'équipements
        const equipmentGroup = this.gisEngine.getLayerGroup('equipment');
        if (equipmentGroup) {
            equipmentGroup.getLayers().push(layer);
        }
        
        return layer;
    }
    
    /**
     * Créer la couche du territoire marocain
     */
    createMoroccoTerritoryLayer() {
        const layer = this.createVectorLayer({
            name: 'morocco_territory',
            source: '/api/v1/gis/morocco-territory/geojson',
            style: this.defaultStyles.moroccoTerritory,
            zIndex: 1
        });
        
        // Ajouter au groupe vectoriel
        const vectorGroup = this.gisEngine.getLayerGroup('vector');
        if (vectorGroup) {
            vectorGroup.getLayers().push(layer);
        }
        
        return layer;
    }
    
    /**
     * Obtenir le style pour un type d'équipement
     */
    getStyleForEquipmentType(equipmentType) {
        const styleMap = {
            'comint': this.defaultStyles.comint,
            'elint': this.defaultStyles.elint,
            'anti-drone': this.defaultStyles.antiDrone,
            'jammer': this.defaultStyles.jammer,
            'sensor': this.defaultStyles.sensor
        };
        
        return styleMap[equipmentType.toLowerCase()] || this.defaultStyles.sensor;
    }
    
    /**
     * Obtenir le style pour un type de couche
     */
    getStyleForLayerType(layerType) {
        const styleMap = {
            'morocco_territory': this.defaultStyles.moroccoTerritory,
            'visibility_area': this.defaultStyles.visibilityArea,
            'measurement': this.defaultStyles.measurement
        };
        
        return styleMap[layerType] || this.defaultStyles.sensor;
    }
    
    /**
     * Ajouter une couche à la carte
     */
    addLayer(layer, groupName = null) {
        if (groupName) {
            const group = this.layerGroups.get(groupName);
            if (group) {
                group.getLayers().push(layer);
            }
        } else {
            this.map.addLayer(layer);
        }
        
        this.emit('layerAdded', layer);
    }
    
    /**
     * Supprimer une couche
     */
    removeLayer(nameOrLayer) {
        let layer;
        let name;
        
        if (typeof nameOrLayer === 'string') {
            name = nameOrLayer;
            layer = this.layers.get(name);
        } else {
            layer = nameOrLayer;
            name = layer.get('name');
        }
        
        if (layer) {
            this.map.removeLayer(layer);
            this.layers.delete(name);
            
            const index = this.layerOrder.indexOf(name);
            if (index > -1) {
                this.layerOrder.splice(index, 1);
            }
            
            this.emit('layerRemoved', layer);
        }
    }
    
    /**
     * Afficher/masquer une couche
     */
    setLayerVisible(name, visible) {
        const layer = this.layers.get(name);
        if (layer) {
            layer.setVisible(visible);
            this.emit('layerVisibilityChanged', { name, visible });
        }
    }
    
    /**
     * Définir l'opacité d'une couche
     */
    setLayerOpacity(name, opacity) {
        const layer = this.layers.get(name);
        if (layer) {
            layer.setOpacity(opacity);
            this.emit('layerOpacityChanged', { name, opacity });
        }
    }
    
    /**
     * Définir l'ordre des couches
     */
    setLayerOrder(name, zIndex) {
        const layer = this.layers.get(name);
        if (layer) {
            layer.setZIndex(zIndex);
            this.emit('layerOrderChanged', { name, zIndex });
        }
    }
    
    /**
     * Obtenir toutes les couches
     */
    getAllLayers() {
        return Array.from(this.layers.values());
    }
    
    /**
     * Obtenir les couches par type
     */
    getLayersByType(type) {
        return this.getAllLayers().filter(layer => layer.get('type') === type);
    }
    
    /**
     * Obtenir une couche par nom
     */
    getLayer(name) {
        return this.layers.get(name);
    }
    
    /**
     * Vérifier si une couche existe
     */
    hasLayer(name) {
        return this.layers.has(name);
    }
    
    /**
     * Nettoyer toutes les couches
     */
    clearAllLayers() {
        this.layers.forEach((layer, name) => {
            this.removeLayer(name);
        });
        
        this.layers.clear();
        this.layerOrder = [];
        
        this.emit('allLayersCleared');
    }
    
    /**
     * Destruction du gestionnaire
     */
    destroy() {
        this.clearAllLayers();
        this.removeAllListeners();
        
        console.log('🗑️ Gestionnaire de couches détruit');
    }
}

export default LayerManager;
