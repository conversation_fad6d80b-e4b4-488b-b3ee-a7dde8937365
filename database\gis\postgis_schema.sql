-- ===================================
-- Schéma PostGIS pour Module SIG C2-EW
-- Territoire officiel du Royaume du Maroc
-- ===================================

-- Activation des extensions PostGIS
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS postgis_raster;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================
-- Tables Spatiales Principales
-- ===================================

-- Table des couches spatiales
CREATE TABLE IF NOT EXISTS spatial_layers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    layer_type VARCHAR(50) NOT NULL, -- vector, raster, mbtiles
    data_source VARCHAR(500) NOT NULL,
    
    -- Propriétés spatiales
    srid INTEGER DEFAULT 4326,
    geometry_type VARCHAR(50), -- POINT, LINESTRING, POLYGON
    
    -- Propriétés d'affichage
    visible BOOLEAN DEFAULT TRUE,
    opacity FLOAT DEFAULT 1.0 CHECK (opacity >= 0.0 AND opacity <= 1.0),
    z_index INTEGER DEFAULT 0,
    
    -- Style et métadonnées
    style_config JSONB,
    metadata JSONB,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- Table des entités spatiales
CREATE TABLE IF NOT EXISTS spatial_features (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    layer_id UUID NOT NULL REFERENCES spatial_layers(id) ON DELETE CASCADE,
    
    -- Géométries
    geometry GEOMETRY(GEOMETRY, 4326),
    geography GEOGRAPHY(GEOMETRY, 4326),
    
    -- Propriétés
    name VARCHAR(255),
    feature_type VARCHAR(100) NOT NULL,
    properties JSONB,
    style JSONB,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- ===================================
-- Territoire Officiel du Maroc
-- ===================================

-- Table du territoire marocain officiel
CREATE TABLE IF NOT EXISTS morocco_territory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Noms officiels
    region_name VARCHAR(255) NOT NULL,
    region_name_ar VARCHAR(255), -- Nom en arabe
    region_type VARCHAR(50) NOT NULL, -- mainland, sahara, maritime
    
    -- Géométrie officielle (MULTIPOLYGON pour gérer les enclaves/exclaves)
    geometry GEOMETRY(MULTIPOLYGON, 4326) NOT NULL,
    
    -- Hiérarchie administrative
    administrative_level INTEGER DEFAULT 1, -- 1=région, 2=province, 3=commune
    parent_region_id UUID REFERENCES morocco_territory(id),
    
    -- Métadonnées officielles
    official_code VARCHAR(20),
    population INTEGER,
    area_km2 FLOAT,
    
    -- Validation territoriale
    is_official BOOLEAN DEFAULT TRUE,
    source_authority VARCHAR(255) DEFAULT 'Ministère de l''Intérieur',
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    -- Contraintes
    CONSTRAINT valid_admin_level CHECK (administrative_level BETWEEN 1 AND 3),
    CONSTRAINT valid_region_type CHECK (region_type IN ('mainland', 'sahara', 'maritime'))
);

-- ===================================
-- Tables MBTiles
-- ===================================

-- Table des datasets MBTiles
CREATE TABLE IF NOT EXISTS mbtiles_datasets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    
    -- Fichier MBTiles
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT DEFAULT 0,
    
    -- Métadonnées MBTiles
    format VARCHAR(10) DEFAULT 'pbf', -- pbf, png, jpg
    min_zoom INTEGER DEFAULT 0 CHECK (min_zoom >= 0 AND min_zoom <= 22),
    max_zoom INTEGER DEFAULT 18 CHECK (max_zoom >= 0 AND max_zoom <= 22),
    
    -- Emprise géographique
    bounds GEOMETRY(POLYGON, 4326),
    
    -- Métadonnées et statut
    metadata JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    last_accessed TIMESTAMP WITH TIME ZONE,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    -- Contraintes
    CONSTRAINT valid_zoom_range CHECK (min_zoom <= max_zoom)
);

-- ===================================
-- Tables d'Analyse de Visibilité
-- ===================================

-- Table des analyses de visibilité
CREATE TABLE IF NOT EXISTS visibility_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Point d'observation
    observer_point GEOMETRY(POINT, 4326) NOT NULL,
    observer_height FLOAT DEFAULT 1.75, -- Hauteur en mètres
    
    -- Paramètres d'analyse
    max_distance FLOAT DEFAULT 10000.0, -- Distance max en mètres
    target_height FLOAT DEFAULT 0.0, -- Hauteur cible en mètres
    earth_curvature BOOLEAN DEFAULT TRUE,
    
    -- MNT utilisé
    dem_dataset VARCHAR(255),
    dem_resolution FLOAT,
    
    -- Résultats
    visible_area GEOMETRY(MULTIPOLYGON, 4326),
    
    -- Statistiques
    total_area FLOAT,
    visible_percentage FLOAT,
    calculation_time FLOAT, -- Temps en secondes
    grid_resolution FLOAT DEFAULT 100.0, -- Résolution grille en mètres
    
    -- Paramètres de calcul
    parameters JSONB,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    -- Contraintes
    CONSTRAINT valid_observer_height CHECK (observer_height >= 0),
    CONSTRAINT valid_max_distance CHECK (max_distance > 0),
    CONSTRAINT valid_grid_resolution CHECK (grid_resolution > 0)
);

-- ===================================
-- Tables de Mesures Spatiales
-- ===================================

-- Table des mesures spatiales
CREATE TABLE IF NOT EXISTS spatial_measurements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255),
    measurement_type VARCHAR(50) NOT NULL, -- distance, area, volume
    
    -- Géométrie mesurée
    geometry GEOMETRY(GEOMETRY, 4326) NOT NULL,
    
    -- Résultats de mesure
    value FLOAT NOT NULL,
    unit VARCHAR(20) NOT NULL,
    
    -- Mesures additionnelles
    perimeter FLOAT,
    area FLOAT,
    volume FLOAT,
    
    -- Paramètres de calcul
    projection_used VARCHAR(50),
    calculation_method VARCHAR(100),
    
    -- Métadonnées
    notes TEXT,
    properties JSONB,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    
    -- Contraintes
    CONSTRAINT valid_measurement_type CHECK (measurement_type IN ('distance', 'area', 'volume', 'perimeter')),
    CONSTRAINT valid_value CHECK (value >= 0)
);

-- ===================================
-- Tables de Cache Spatial
-- ===================================

-- Table de cache spatial
CREATE TABLE IF NOT EXISTS spatial_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key VARCHAR(255) UNIQUE NOT NULL,
    cache_type VARCHAR(50) NOT NULL, -- layer, feature, tile, analysis
    
    -- Données cachées
    data JSONB,
    geometry_data GEOMETRY(GEOMETRY, 4326),
    
    -- Métadonnées de cache
    source_id UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    access_count INTEGER DEFAULT 0,
    
    -- Taille et priorité
    data_size INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 1,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Contraintes
    CONSTRAINT valid_cache_type CHECK (cache_type IN ('layer', 'feature', 'tile', 'analysis')),
    CONSTRAINT valid_priority CHECK (priority >= 1)
);

-- ===================================
-- Tables de Synchronisation
-- ===================================

-- Table des logs de synchronisation
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sync_type VARCHAR(50) NOT NULL, -- upload, download, conflict
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    
    -- Statut
    status VARCHAR(20) NOT NULL, -- pending, success, error, conflict
    error_message TEXT,
    
    -- Données de synchronisation
    local_data JSONB,
    remote_data JSONB,
    
    -- Résolution de conflit
    conflict_resolution VARCHAR(50), -- local_wins, remote_wins, merge
    resolved_data JSONB,
    
    -- Métadonnées
    sync_session_id UUID,
    retry_count INTEGER DEFAULT 0,
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Contraintes
    CONSTRAINT valid_sync_type CHECK (sync_type IN ('upload', 'download', 'conflict')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'success', 'error', 'conflict')),
    CONSTRAINT valid_conflict_resolution CHECK (conflict_resolution IN ('local_wins', 'remote_wins', 'merge'))
);
