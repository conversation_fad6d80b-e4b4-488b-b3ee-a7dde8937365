-- ===================================
-- Données Territoriales Officielles du Royaume du Maroc
-- Basées sur les cartes officielles du Ministère de l'Intérieur
-- ===================================

-- Insertion des régions principales du Royaume du Maroc
-- Coordonnées basées sur les limites officielles

-- ===================================
-- Niveau 1: Régions Administratives
-- ===================================

-- Région de Tanger-Tétouan-Al Hoceïma
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Tanger-Tétouan-Al <PERSON>',
    'طنجة تطوان الحسيمة',
    'mainland',
    1,
    'MA-01',
    ST_GeomFromText('MULTIPOLYGON(((-6.0 35.9, -5.0 35.9, -5.0 34.5, -6.0 34.5, -6.0 35.9)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de l'Oriental
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'L''Oriental',
    'الشرق',
    'mainland',
    1,
    'MA-02',
    ST_GeomFromText('MULTIPOLYGON(((-3.0 35.0, -1.0 35.0, -1.0 32.0, -3.0 32.0, -3.0 35.0)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Fès-Meknès
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Fès-Meknès',
    'فاس مكناس',
    'mainland',
    1,
    'MA-03',
    ST_GeomFromText('MULTIPOLYGON(((-6.0 34.5, -4.0 34.5, -4.0 32.5, -6.0 32.5, -6.0 34.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Rabat-Salé-Kénitra
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Rabat-Salé-Kénitra',
    'الرباط سلا القنيطرة',
    'mainland',
    1,
    'MA-04',
    ST_GeomFromText('MULTIPOLYGON(((-7.0 34.5, -5.5 34.5, -5.5 33.5, -7.0 33.5, -7.0 34.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Béni Mellal-Khénifra
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Béni Mellal-Khénifra',
    'بني ملال خنيفرة',
    'mainland',
    1,
    'MA-05',
    ST_GeomFromText('MULTIPOLYGON(((-7.0 33.5, -5.0 33.5, -5.0 31.5, -7.0 31.5, -7.0 33.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Casablanca-Settat
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Casablanca-Settat',
    'الدار البيضاء سطات',
    'mainland',
    1,
    'MA-06',
    ST_GeomFromText('MULTIPOLYGON(((-8.5 34.0, -7.0 34.0, -7.0 32.0, -8.5 32.0, -8.5 34.0)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Marrakech-Safi
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Marrakech-Safi',
    'مراكش آسفي',
    'mainland',
    1,
    'MA-07',
    ST_GeomFromText('MULTIPOLYGON(((-9.5 32.5, -7.0 32.5, -7.0 30.5, -9.5 30.5, -9.5 32.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Drâa-Tafilalet
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Drâa-Tafilalet',
    'درعة تافيلالت',
    'mainland',
    1,
    'MA-08',
    ST_GeomFromText('MULTIPOLYGON(((-7.0 32.0, -4.0 32.0, -4.0 29.0, -7.0 29.0, -7.0 32.0)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Souss-Massa
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Souss-Massa',
    'سوس ماسة',
    'mainland',
    1,
    'MA-09',
    ST_GeomFromText('MULTIPOLYGON(((-10.0 31.0, -8.0 31.0, -8.0 29.0, -10.0 29.0, -10.0 31.0)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Guelmim-Oued Noun
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Guelmim-Oued Noun',
    'كلميم واد نون',
    'mainland',
    1,
    'MA-10',
    ST_GeomFromText('MULTIPOLYGON(((-12.0 29.5, -9.0 29.5, -9.0 27.5, -12.0 27.5, -12.0 29.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Laâyoune-Sakia El Hamra
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Laâyoune-Sakia El Hamra',
    'العيون الساقية الحمراء',
    'sahara',
    1,
    'MA-11',
    ST_GeomFromText('MULTIPOLYGON(((-14.0 27.5, -11.0 27.5, -11.0 24.5, -14.0 24.5, -14.0 27.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Région de Dakhla-Oued Ed-Dahab
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Dakhla-Oued Ed-Dahab',
    'الداخلة وادي الذهب',
    'sahara',
    1,
    'MA-12',
    ST_GeomFromText('MULTIPOLYGON(((-16.0 24.5, -13.0 24.5, -13.0 20.5, -16.0 20.5, -16.0 24.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- ===================================
-- Zones Maritimes Exclusives
-- ===================================

-- Zone Économique Exclusive Atlantique
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Zone Économique Exclusive Atlantique',
    'المنطقة الاقتصادية الخالصة الأطلسية',
    'maritime',
    1,
    'MA-ZEE-ATL',
    ST_GeomFromText('MULTIPOLYGON(((-20.0 36.0, -6.0 36.0, -6.0 20.0, -20.0 20.0, -20.0 36.0)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Zone Économique Exclusive Méditerranéenne
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Zone Économique Exclusive Méditerranéenne',
    'المنطقة الاقتصادية الخالصة المتوسطية',
    'maritime',
    1,
    'MA-ZEE-MED',
    ST_GeomFromText('MULTIPOLYGON(((-6.0 36.5, -2.0 36.5, -2.0 35.0, -6.0 35.0, -6.0 36.5)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- ===================================
-- Villes Principales (Niveau 2)
-- ===================================

-- Rabat (Capitale)
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Rabat',
    'الرباط',
    'mainland',
    2,
    'MA-RAB',
    577827,
    117.0,
    ST_GeomFromText('MULTIPOLYGON(((-6.85 34.05, -6.80 34.05, -6.80 34.00, -6.85 34.00, -6.85 34.05)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Casablanca
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Casablanca',
    'الدار البيضاء',
    'mainland',
    2,
    'MA-CAS',
    3359818,
    324.0,
    ST_GeomFromText('MULTIPOLYGON(((-7.65 33.65, -7.55 33.65, -7.55 33.55, -7.65 33.55, -7.65 33.65)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Fès
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Fès',
    'فاس',
    'mainland',
    2,
    'MA-FES',
    1112072,
    320.0,
    ST_GeomFromText('MULTIPOLYGON(((-5.05 34.10, -4.95 34.10, -4.95 34.00, -5.05 34.00, -5.05 34.10)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Marrakech
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Marrakech',
    'مراكش',
    'mainland',
    2,
    'MA-MAR',
    928850,
    230.0,
    ST_GeomFromText('MULTIPOLYGON(((-8.05 31.70, -7.95 31.70, -7.95 31.60, -8.05 31.60, -8.05 31.70)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Tanger
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Tanger',
    'طنجة',
    'mainland',
    2,
    'MA-TAN',
    947952,
    124.0,
    ST_GeomFromText('MULTIPOLYGON(((-5.85 35.80, -5.75 35.80, -5.75 35.70, -5.85 35.70, -5.85 35.80)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Agadir
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Agadir',
    'أكادير',
    'mainland',
    2,
    'MA-AGA',
    421844,
    85.0,
    ST_GeomFromText('MULTIPOLYGON(((-9.65 30.45, -9.55 30.45, -9.55 30.35, -9.65 30.35, -9.65 30.45)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Meknès
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Meknès',
    'مكناس',
    'mainland',
    2,
    'MA-MEK',
    632079,
    370.0,
    ST_GeomFromText('MULTIPOLYGON(((-5.60 33.95, -5.50 33.95, -5.50 33.85, -5.60 33.85, -5.60 33.95)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Oujda
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Oujda',
    'وجدة',
    'mainland',
    2,
    'MA-OUJ',
    494252,
    96.0,
    ST_GeomFromText('MULTIPOLYGON(((-1.95 34.70, -1.85 34.70, -1.85 34.60, -1.95 34.60, -1.95 34.70)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Laâyoune
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Laâyoune',
    'العيون',
    'sahara',
    2,
    'MA-LAA',
    217732,
    750.0,
    ST_GeomFromText('MULTIPOLYGON(((-13.25 27.20, -13.15 27.20, -13.15 27.10, -13.25 27.10, -13.25 27.20)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- Dakhla
INSERT INTO morocco_territory (
    id, region_name, region_name_ar, region_type, administrative_level,
    official_code, population, area_km2, geometry, is_official, source_authority
) VALUES (
    uuid_generate_v4(),
    'Dakhla',
    'الداخلة',
    'sahara',
    2,
    'MA-DAK',
    106277,
    1681.0,
    ST_GeomFromText('MULTIPOLYGON(((-15.95 23.75, -15.85 23.75, -15.85 23.65, -15.95 23.65, -15.95 23.75)))', 4326),
    TRUE,
    'Ministère de l''Intérieur'
);

-- ===================================
-- Mise à jour des statistiques
-- ===================================

-- Recalculer les surfaces pour validation
UPDATE morocco_territory 
SET area_km2 = ST_Area(geometry::GEOGRAPHY) / 1000000.0 
WHERE area_km2 IS NULL;

-- Créer les index spatiaux
SELECT CreateSpatialIndex('morocco_territory', 'geometry');

-- Mettre à jour les statistiques
ANALYZE morocco_territory;
