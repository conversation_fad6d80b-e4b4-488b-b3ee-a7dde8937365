<!DOCTYPE html>
<html>
<head>
	<title>Leaflet.draw vector editing handlers</title>

	<link rel="stylesheet" href="libs/leaflet.css" />

	<script src="libs/leaflet-src.js"></script>

	<script src="../../src/Leaflet.draw.js"></script>
	<script src="../../src/Leaflet.Draw.Event.js"></script>

	<script src="../../src/ext/TouchEvents.js"></script>

	<script src="../../src/edit/handler/Edit.Poly.js"></script>
	<script src="../../src/edit/handler/Edit.SimpleShape.js"></script>
	<script src="../../src/edit/handler/Edit.Rectangle.js"></script>
	<script src="../../src/edit/handler/Edit.Marker.js"></script>
	<script src="../../src/edit/handler/Edit.CircleMarker.js"></script>
	<script src="../../src/edit/handler/Edit.Circle.js"></script>

</head>
<body>
	<div id="map" style="width: 800px; height: 600px; border: 1px solid #ccc"></div>

	<script>
		var osmUrl = 'http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
			osmAttrib = '&copy; <a href="http://openstreetmap.org/copyright">OpenStreetMap</a> contributors',
			osm = L.tileLayer(osmUrl, {maxZoom: 18, attribution: osmAttrib});
			map = new L.Map('map', {layers: [osm], center: new L.LatLng(51.505, -0.04), zoom: 13});

		var polygon = new L.Polygon([
			[51.51, -0.1],
			[51.5, -0.06],
			[51.52, -0.03]
		]);

		polygon.editing.enable();

		map.addLayer(polygon);

		var polyline = new L.Polyline([
			[51.50, -0.04],
			[51.49, -0.02],
			[51.51, 0],
			[51.52, -0.02]
		]);

		polyline.editing.enable();

		map.addLayer(polyline);

		var circleMarker = L.circleMarker([51.50, -0.08]);

		circleMarker.editing.enable();

		map.addLayer(circleMarker);

		var circle = L.circle([51.53, -0.06], 600);

		circle.editing.enable();

		map.addLayer(circle);

		var rectangle = L.rectangle([[51.49, -0.1], [51.48, -0.06]]);

		rectangle.editing.enable();

		map.addLayer(rectangle);

		polygon.on('edit', function() {
			console.log('Polygon was edited!');
		});
		polyline.on('edit', function() {
			console.log('Polyline was edited!');
		});
	</script>
</body>
</html>
