import IntervalRTreeNode from './IntervalRTreeNode'

export default class IntervalRTreeLeafNode extends IntervalRTreeNode {
  constructor () {
    super()
    this._item = null
    const min = arguments[0]
    const max = arguments[1]
    const item = arguments[2]
    this._min = min
    this._max = max
    this._item = item
  }
  query (queryMin, queryMax, visitor) {
    if (!this.intersects(queryMin, queryMax)) return null
    visitor.visitItem(this._item)
  }
  interfaces_ () {
    return []
  }
  getClass () {
    return IntervalRTreeLeafNode
  }
}
