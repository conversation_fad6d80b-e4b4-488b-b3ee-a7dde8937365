/**
 * Application C2-EW Complète avec Module SIG Intégré
 * Version avec carte hors ligne et toutes les fonctionnalités
 */

import React, { useState, useEffect, useRef } from 'react';
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import LineString from 'ol/geom/LineString';
import Polygon from 'ol/geom/Polygon';
import { Style, Circle, Fill, Stroke, Text, Icon } from 'ol/style';
import { fromLonLat, toLonLat } from 'ol/proj';
import { Draw, Modify, Snap } from 'ol/interaction';
import { getLength, getArea } from 'ol/sphere';
import 'ol/ol.css';
import { MapView, EquipmentView, PluginsView, AlertsView, SettingsView } from './C2EWComponents';

const C2EWApp = () => {
    // États principaux
    const mapRef = useRef();
    const [map, setMap] = useState(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [currentView, setCurrentView] = useState('dashboard');
    const [isOfflineMode, setIsOfflineMode] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState('online');

    // États SIG
    const [activeTool, setActiveTool] = useState(null);
    const [measurements, setMeasurements] = useState([]);
    const [equipment, setEquipment] = useState([]);
    const [selectedEquipment, setSelectedEquipment] = useState(null);
    const [visibilityAnalysis, setVisibilityAnalysis] = useState(null);
    const [alerts, setAlerts] = useState([]);
    const [plugins, setPlugins] = useState([]);

    // Données d'équipements C2-EW
    const testEquipment = [
        {
            id: "1",
            name: "Station COMINT Alpha",
            type: "comint",
            longitude: -7.6,
            latitude: 33.6,
            status: "operational",
            lastUpdate: "2025-07-03 02:15:23",
            properties: {
                frequency_range: "30-3000 MHz",
                power: "100W",
                antenna_type: "Omnidirectionnelle",
                description: "Station d'interception COMINT principale - Casablanca"
            }
        },
        {
            id: "2",
            name: "Capteur ELINT Beta",
            type: "elint",
            longitude: -7.5,
            latitude: 33.7,
            status: "operational",
            lastUpdate: "2025-07-03 02:14:45",
            properties: {
                frequency_range: "1-18 GHz",
                sensitivity: "-80 dBm",
                antenna_type: "Directionnelle",
                description: "Capteur d'analyse ELINT haute fréquence - Rabat"
            }
        },
        {
            id: "3",
            name: "Système Anti-Drone Gamma",
            type: "anti-drone",
            longitude: -7.4,
            latitude: 33.5,
            status: "maintenance",
            lastUpdate: "2025-07-03 01:30:12",
            properties: {
                range: "5 km",
                detection_types: ["RF", "Radar", "Optique"],
                neutralization: ["Brouillage", "Capture"],
                description: "Système de détection et neutralisation de drones - Mohammedia"
            }
        },
        {
            id: "4",
            name: "Brouilleur Delta",
            type: "jammer",
            longitude: -7.7,
            latitude: 33.8,
            status: "operational",
            lastUpdate: "2025-07-03 02:10:33",
            properties: {
                power: "50W",
                bands: ["2.4GHz", "5.8GHz", "GPS L1/L2"],
                range: "3 km",
                description: "Brouilleur multi-bandes tactique - Salé"
            }
        },
        {
            id: "5",
            name: "Capteur Acoustique Epsilon",
            type: "sensor",
            longitude: -7.3,
            latitude: 33.4,
            status: "operational",
            lastUpdate: "2025-07-03 02:12:18",
            properties: {
                type: "Acoustique + Sismique",
                range: "2 km",
                sensitivity: "40 dB",
                description: "Capteur de surveillance périmétrique - Bouznika"
            }
        },
        {
            id: "6",
            name: "Radar de Surveillance Zeta",
            type: "radar",
            longitude: -7.8,
            latitude: 33.9,
            status: "operational",
            lastUpdate: "2025-07-03 02:16:05",
            properties: {
                type: "Radar 3D",
                range: "50 km",
                frequency: "X-Band",
                description: "Radar de surveillance aérienne - Kénitra"
            }
        }
    ];

    // Alertes de test
    const testAlerts = [
        {
            id: 1,
            type: "warning",
            title: "Équipement en maintenance",
            message: "Système Anti-Drone Gamma hors service",
            timestamp: "2025-07-03 01:30:12",
            equipment_id: "3"
        },
        {
            id: 2,
            type: "info",
            title: "Mise à jour système",
            message: "Nouveau firmware disponible pour capteurs ELINT",
            timestamp: "2025-07-03 02:00:00",
            equipment_id: "2"
        },
        {
            id: 3,
            type: "success",
            title: "Connexion rétablie",
            message: "Radar de Surveillance Zeta opérationnel",
            timestamp: "2025-07-03 02:16:05",
            equipment_id: "6"
        }
    ];

    // Plugins disponibles
    const testPlugins = [
        {
            id: 1,
            name: "Analyse Spectrale",
            version: "2.1.0",
            status: "active",
            description: "Analyse en temps réel du spectre RF"
        },
        {
            id: 2,
            name: "Détection de Patterns",
            version: "1.8.3",
            status: "active",
            description: "Reconnaissance automatique de signaux"
        },
        {
            id: 3,
            name: "Géolocalisation TDOA",
            version: "3.0.1",
            status: "inactive",
            description: "Localisation par différence de temps d'arrivée"
        },
        {
            id: 4,
            name: "Export STANAG",
            version: "1.5.2",
            status: "active",
            description: "Export des données au format STANAG 4607"
        }
    ];

    // Vérification de la connexion et enregistrement du Service Worker
    useEffect(() => {
        const checkConnection = () => {
            setConnectionStatus(navigator.onLine ? 'online' : 'offline');
            setIsOfflineMode(!navigator.onLine);
        };

        checkConnection();
        window.addEventListener('online', checkConnection);
        window.addEventListener('offline', checkConnection);

        // Enregistrement du Service Worker pour le mode hors ligne
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/offline-sw.js')
                .then((registration) => {
                    console.log('🛡️ Service Worker C2-EW enregistré:', registration);
                })
                .catch((error) => {
                    console.log('❌ Erreur Service Worker:', error);
                });
        }

        return () => {
            window.removeEventListener('online', checkConnection);
            window.removeEventListener('offline', checkConnection);
        };
    }, []);

    // Initialisation des données
    useEffect(() => {
        if (isAuthenticated) {
            setEquipment(testEquipment);
            setAlerts(testAlerts);
            setPlugins(testPlugins);
        }
    }, [isAuthenticated]);

    // Styles pour les équipements
    const getEquipmentStyle = (equipment) => {
        const colors = {
            comint: '#2196F3',
            elint: '#FF9800',
            'anti-drone': '#F44336',
            jammer: '#9C27B0',
            sensor: '#4CAF50',
            radar: '#00BCD4'
        };

        const color = colors[equipment.type] || '#757575';
        const isOperational = equipment.status === 'operational';

        return new Style({
            image: new Circle({
                radius: 15,
                fill: new Fill({
                    color: isOperational ? color : '#BDBDBD'
                }),
                stroke: new Stroke({
                    color: '#FFFFFF',
                    width: 3
                })
            }),
            text: new Text({
                text: equipment.name.split(' ')[0], // Premier mot seulement
                font: 'bold 11px Arial',
                fill: new Fill({ color: '#000000' }),
                stroke: new Stroke({ color: '#FFFFFF', width: 2 }),
                offsetY: -30
            })
        });
    };

    // Connexion
    const handleLogin = async (e) => {
        e.preventDefault();
        if (username === 'admin' && password === 'admin123') {
            setIsAuthenticated(true);
        } else {
            alert('Identifiants incorrects');
        }
    };

    // Navigation
    const navigationItems = [
        { id: 'dashboard', name: 'Dashboard', icon: '📊' },
        { id: 'map', name: 'Carte SIG', icon: '🗺️' },
        { id: 'equipment', name: 'Équipements', icon: '📡' },
        { id: 'plugins', name: 'Plugins', icon: '🔌' },
        { id: 'alerts', name: 'Alertes', icon: '🚨' },
        { id: 'settings', name: 'Paramètres', icon: '⚙️' }
    ];

    // Page de connexion
    if (!isAuthenticated) {
        return (
            <div style={{
                minHeight: '100vh',
                background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontFamily: 'Arial, sans-serif'
            }}>
                <div style={{
                    background: 'rgba(255, 255, 255, 0.95)',
                    padding: '2.5rem',
                    borderRadius: '12px',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    width: '100%',
                    maxWidth: '450px',
                    backdropFilter: 'blur(10px)'
                }}>
                    <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🛡️</div>
                        <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '0.5rem' }}>
                            C2-EW Platform
                        </h1>
                        <p style={{ color: '#6b7280', fontSize: '1.1rem' }}>Commandement & Contrôle</p>
                        <p style={{ color: '#2563eb', fontSize: '0.9rem', marginTop: '0.5rem' }}>
                            🇲🇦 Module SIG Intégré - Territoire Marocain
                        </p>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '0.5rem',
                            marginTop: '1rem',
                            fontSize: '0.85rem'
                        }}>
                            <div style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                background: connectionStatus === 'online' ? '#10b981' : '#f59e0b'
                            }}></div>
                            <span style={{ color: '#6b7280' }}>
                                {connectionStatus === 'online' ? '🌐 En ligne' : '📱 Mode hors ligne'}
                            </span>
                        </div>
                    </div>

                    <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                        <div>
                            <label style={{
                                display: 'block',
                                fontSize: '0.9rem',
                                fontWeight: '600',
                                color: '#374151',
                                marginBottom: '0.5rem'
                            }}>
                                Nom d'utilisateur
                            </label>
                            <input
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    border: '2px solid #e5e7eb',
                                    borderRadius: '8px',
                                    fontSize: '1rem',
                                    transition: 'border-color 0.2s'
                                }}
                                placeholder="admin"
                                required
                                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                                onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                            />
                        </div>

                        <div>
                            <label style={{
                                display: 'block',
                                fontSize: '0.9rem',
                                fontWeight: '600',
                                color: '#374151',
                                marginBottom: '0.5rem'
                            }}>
                                Mot de passe
                            </label>
                            <input
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '0.75rem',
                                    border: '2px solid #e5e7eb',
                                    borderRadius: '8px',
                                    fontSize: '1rem',
                                    transition: 'border-color 0.2s'
                                }}
                                placeholder="admin123"
                                required
                                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                                onBlur={(e) => e.target.style.borderColor = '#e5e7eb'}
                            />
                        </div>

                        <button
                            type="submit"
                            style={{
                                width: '100%',
                                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                                color: 'white',
                                padding: '0.75rem 1rem',
                                borderRadius: '8px',
                                border: 'none',
                                fontSize: '1rem',
                                fontWeight: '600',
                                cursor: 'pointer',
                                transition: 'transform 0.2s'
                            }}
                            onMouseOver={(e) => e.target.style.transform = 'translateY(-1px)'}
                            onMouseOut={(e) => e.target.style.transform = 'translateY(0)'}
                        >
                            🔐 Se connecter
                        </button>
                    </form>

                    <div style={{ marginTop: '2rem', textAlign: 'center', fontSize: '0.85rem', color: '#6b7280' }}>
                        <p style={{ marginBottom: '1rem' }}>Comptes de démonstration :</p>
                        <div style={{
                            background: '#f8fafc',
                            padding: '1rem',
                            borderRadius: '8px',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '0.5rem'
                        }}>
                            <div><strong>👤 admin</strong> / admin123 (Administrateur)</div>
                            <div><strong>🔧 operator</strong> / op123 (Opérateur)</div>
                            <div><strong>📊 viewer</strong> / view123 (Observateur)</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Interface principale C2-EW
    return (
        <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', fontFamily: 'Arial, sans-serif' }}>
            {/* Header principal */}
            <header style={{
                background: 'linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%)',
                color: 'white',
                padding: '1rem 2rem',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                        <div style={{ fontSize: '1.5rem' }}>🛡️</div>
                        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', margin: 0 }}>C2-EW Platform</h1>
                    </div>
                    <span style={{ color: '#bfdbfe', fontSize: '1.2rem' }}>|</span>
                    <span style={{ color: '#bfdbfe', fontSize: '1.1rem' }}>Module SIG Intégré</span>
                    <div style={{
                        fontSize: '0.75rem',
                        background: connectionStatus === 'online' ? '#10b981' : '#f59e0b',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '12px',
                        fontWeight: '600',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                    }}>
                        {connectionStatus === 'online' ? '🌐 EN LIGNE' : '📱 HORS LIGNE'}
                    </div>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
                    <div style={{ fontSize: '0.9rem', textAlign: 'right' }}>
                        <div style={{ color: '#bfdbfe' }}>Connecté en tant que</div>
                        <div style={{ fontWeight: '600' }}>👤 Administrateur</div>
                    </div>
                    <div style={{ fontSize: '0.85rem', textAlign: 'right' }}>
                        <div style={{ color: '#bfdbfe' }}>Dernière sync</div>
                        <div style={{ fontWeight: '500' }}>🕐 {new Date().toLocaleTimeString()}</div>
                    </div>
                    <button
                        onClick={() => setIsAuthenticated(false)}
                        style={{
                            background: 'rgba(255, 255, 255, 0.1)',
                            color: 'white',
                            border: '1px solid rgba(255, 255, 255, 0.2)',
                            padding: '0.5rem 1rem',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '0.9rem',
                            transition: 'background 0.2s'
                        }}
                        onMouseOver={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}
                        onMouseOut={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
                    >
                        🚪 Déconnexion
                    </button>
                </div>
            </header>

            {/* Navigation principale */}
            <nav style={{
                background: '#f8fafc',
                borderBottom: '1px solid #e5e7eb',
                padding: '0 2rem',
                display: 'flex',
                gap: '0.5rem'
            }}>
                {navigationItems.map(item => (
                    <button
                        key={item.id}
                        onClick={() => setCurrentView(item.id)}
                        style={{
                            background: currentView === item.id ? '#3b82f6' : 'transparent',
                            color: currentView === item.id ? 'white' : '#374151',
                            border: 'none',
                            padding: '0.75rem 1.5rem',
                            cursor: 'pointer',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            borderRadius: '6px 6px 0 0',
                            transition: 'all 0.2s',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem'
                        }}
                        onMouseOver={(e) => {
                            if (currentView !== item.id) {
                                e.target.style.background = '#e5e7eb';
                            }
                        }}
                        onMouseOut={(e) => {
                            if (currentView !== item.id) {
                                e.target.style.background = 'transparent';
                            }
                        }}
                    >
                        <span>{item.icon}</span>
                        <span>{item.name}</span>
                        {item.id === 'alerts' && alerts.length > 0 && (
                            <span style={{
                                background: '#ef4444',
                                color: 'white',
                                fontSize: '0.7rem',
                                padding: '0.1rem 0.4rem',
                                borderRadius: '10px',
                                minWidth: '1.2rem',
                                textAlign: 'center'
                            }}>
                                {alerts.length}
                            </span>
                        )}
                    </button>
                ))}
            </nav>

            {/* Contenu principal */}
            <main style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
                {currentView === 'dashboard' && <DashboardView equipment={equipment} alerts={alerts} plugins={plugins} />}
                {currentView === 'map' && <MapView
                    mapRef={mapRef}
                    map={map}
                    setMap={setMap}
                    equipment={equipment}
                    selectedEquipment={selectedEquipment}
                    setSelectedEquipment={setSelectedEquipment}
                    activeTool={activeTool}
                    setActiveTool={setActiveTool}
                    measurements={measurements}
                    setMeasurements={setMeasurements}
                    visibilityAnalysis={visibilityAnalysis}
                    setVisibilityAnalysis={setVisibilityAnalysis}
                    getEquipmentStyle={getEquipmentStyle}
                    isOfflineMode={isOfflineMode}
                />}
                {currentView === 'equipment' && <EquipmentView equipment={equipment} setSelectedEquipment={setSelectedEquipment} />}
                {currentView === 'plugins' && <PluginsView plugins={plugins} />}
                {currentView === 'alerts' && <AlertsView alerts={alerts} />}
                {currentView === 'settings' && <SettingsView isOfflineMode={isOfflineMode} setIsOfflineMode={setIsOfflineMode} />}
            </main>

            {/* Footer */}
            <footer style={{
                background: '#1f2937',
                color: 'white',
                padding: '0.75rem 2rem',
                fontSize: '0.85rem'
            }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
                        <span>🗺️ C2-EW Platform v2.1.0</span>
                        <span>🇲🇦 Territoire: Maroc</span>
                        <span>📡 {equipment.filter(e => e.status === 'operational').length}/{equipment.length} Équipements</span>
                        <span>⚡ OpenLayers 8+</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                        <span>🕐 {new Date().toLocaleString()}</span>
                        <div style={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            background: connectionStatus === 'online' ? '#10b981' : '#f59e0b'
                        }}></div>
                    </div>
                </div>
            </footer>
        </div>
    );
};

// Composant Dashboard
const DashboardView = ({ equipment, alerts, plugins }) => {
    const operationalCount = equipment.filter(e => e.status === 'operational').length;
    const maintenanceCount = equipment.filter(e => e.status === 'maintenance').length;
    const activePlugins = plugins.filter(p => p.status === 'active').length;

    return (
        <div style={{ flex: 1, padding: '2rem', background: '#f8fafc', overflowY: 'auto' }}>
            <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
                <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '2rem' }}>
                    📊 Tableau de Bord C2-EW
                </h2>

                {/* Statistiques principales */}
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
                    <div style={{
                        background: 'white',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <div>
                                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Équipements Actifs</p>
                                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981', margin: '0.5rem 0 0 0' }}>
                                    {operationalCount}
                                </p>
                            </div>
                            <div style={{ fontSize: '2.5rem' }}>📡</div>
                        </div>
                        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                            Sur {equipment.length} équipements total
                        </div>
                    </div>

                    <div style={{
                        background: 'white',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <div>
                                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>En Maintenance</p>
                                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b', margin: '0.5rem 0 0 0' }}>
                                    {maintenanceCount}
                                </p>
                            </div>
                            <div style={{ fontSize: '2.5rem' }}>⚠️</div>
                        </div>
                        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                            Intervention requise
                        </div>
                    </div>

                    <div style={{
                        background: 'white',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <div>
                                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Plugins Actifs</p>
                                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6', margin: '0.5rem 0 0 0' }}>
                                    {activePlugins}
                                </p>
                            </div>
                            <div style={{ fontSize: '2.5rem' }}>🔌</div>
                        </div>
                        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                            Modules opérationnels
                        </div>
                    </div>

                    <div style={{
                        background: 'white',
                        padding: '1.5rem',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <div>
                                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Alertes</p>
                                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ef4444', margin: '0.5rem 0 0 0' }}>
                                    {alerts.length}
                                </p>
                            </div>
                            <div style={{ fontSize: '2.5rem' }}>🚨</div>
                        </div>
                        <div style={{ marginTop: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                            Notifications actives
                        </div>
                    </div>
                </div>

                {/* Équipements récents */}
                <div style={{
                    background: 'white',
                    borderRadius: '12px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    border: '1px solid #e5e7eb',
                    marginBottom: '2rem'
                }}>
                    <div style={{ padding: '1.5rem', borderBottom: '1px solid #e5e7eb' }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                            🔄 Activité Récente des Équipements
                        </h3>
                    </div>
                    <div style={{ padding: '1rem' }}>
                        {equipment.slice(0, 4).map(eq => (
                            <div key={eq.id} style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                padding: '1rem',
                                borderBottom: '1px solid #f3f4f6'
                            }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                                    <div style={{
                                        width: '12px',
                                        height: '12px',
                                        borderRadius: '50%',
                                        background: eq.status === 'operational' ? '#10b981' : '#f59e0b'
                                    }}></div>
                                    <div>
                                        <div style={{ fontWeight: '600', color: '#1f2937' }}>{eq.name}</div>
                                        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>{eq.type.toUpperCase()}</div>
                                    </div>
                                </div>
                                <div style={{ textAlign: 'right', fontSize: '0.875rem', color: '#6b7280' }}>
                                    <div>Dernière mise à jour</div>
                                    <div style={{ fontWeight: '500' }}>{eq.lastUpdate}</div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Alertes récentes */}
                <div style={{
                    background: 'white',
                    borderRadius: '12px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    border: '1px solid #e5e7eb'
                }}>
                    <div style={{ padding: '1.5rem', borderBottom: '1px solid #e5e7eb' }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                            🚨 Alertes Récentes
                        </h3>
                    </div>
                    <div style={{ padding: '1rem' }}>
                        {alerts.map(alert => (
                            <div key={alert.id} style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '1rem',
                                padding: '1rem',
                                borderBottom: '1px solid #f3f4f6'
                            }}>
                                <div style={{
                                    width: '8px',
                                    height: '8px',
                                    borderRadius: '50%',
                                    background: alert.type === 'warning' ? '#f59e0b' :
                                               alert.type === 'success' ? '#10b981' : '#3b82f6'
                                }}></div>
                                <div style={{ flex: 1 }}>
                                    <div style={{ fontWeight: '600', color: '#1f2937' }}>{alert.title}</div>
                                    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>{alert.message}</div>
                                </div>
                                <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                                    {alert.timestamp}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default C2EWApp;