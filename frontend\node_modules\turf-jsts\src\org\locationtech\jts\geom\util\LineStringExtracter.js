import LineString from '../LineString'
import GeometryCollection from '../GeometryCollection'
import ArrayList from '../../../../../java/util/ArrayList'
import GeometryFilter from '../GeometryFilter'

export default class LineStringExtracter {
  constructor (comps) {
    this._comps = comps || null
  }
  filter (geom) {
    if (geom instanceof LineString) this._comps.add(geom)
  }
  interfaces_ () {
    return [GeometryFilter]
  }
  getClass () {
    return LineStringExtracter
  }
  static getGeometry (geom) {
    return geom.getFactory().buildGeometry(LineStringExtracter.getLines(geom))
  }
  static getLines () {
    if (arguments.length === 1) {
      const geom = arguments[0]
      return LineStringExtracter.getLines(geom, new ArrayList())
    } else if (arguments.length === 2) {
      const geom = arguments[0]
      const lines = arguments[1]
      if (geom instanceof LineString) {
        lines.add(geom)
      } else if (geom instanceof GeometryCollection) {
        geom.apply(new LineStringExtracter(lines))
      }
      return lines
    }
  }
}
