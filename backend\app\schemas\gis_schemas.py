"""
Schémas Pydantic pour l'API géospatiale
Module SIG C2-EW
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union
from uuid import UUID
from datetime import datetime
from enum import Enum

# ===================================
# Enums
# ===================================

class MeasurementType(str, Enum):
    distance = "distance"
    area = "area"
    perimeter = "perimeter"
    volume = "volume"

class SyncStrategy(str, Enum):
    full = "full"
    incremental = "incremental"
    conflict_resolution = "conflict_resolution"

class RegionType(str, Enum):
    mainland = "mainland"
    sahara = "sahara"
    maritime = "maritime"

# ===================================
# Schémas de base
# ===================================

class CoordinatePoint(BaseModel):
    longitude: float = Field(..., ge=-180, le=180, description="Longitude en degrés")
    latitude: float = Field(..., ge=-90, le=90, description="Latitude en degrés")

class BoundingBox(BaseModel):
    west: float = Field(..., ge=-180, le=180)
    south: float = Field(..., ge=-90, le=90)
    east: float = Field(..., ge=-180, le=180)
    north: float = Field(..., ge=-90, le=90)
    
    @validator('east')
    def east_must_be_greater_than_west(cls, v, values):
        if 'west' in values and v <= values['west']:
            raise ValueError('east must be greater than west')
        return v
    
    @validator('north')
    def north_must_be_greater_than_south(cls, v, values):
        if 'south' in values and v <= values['south']:
            raise ValueError('north must be greater than south')
        return v

# ===================================
# Schémas de recherche spatiale
# ===================================

class SpatialSearchRequest(BaseModel):
    longitude: float = Field(..., ge=-180, le=180)
    latitude: float = Field(..., ge=-90, le=90)
    radius: float = Field(1000.0, gt=0, le=50000, description="Rayon de recherche en mètres")
    layer_types: Optional[List[str]] = Field(None, description="Types de couches à rechercher")
    feature_types: Optional[List[str]] = Field(None, description="Types d'entités à rechercher")
    limit: int = Field(100, gt=0, le=1000, description="Nombre maximum de résultats")

class SpatialFeatureResult(BaseModel):
    feature_id: str
    layer_name: str
    feature_name: Optional[str]
    feature_type: str
    distance_m: float
    geometry: Dict[str, Any]

class SpatialSearchResponse(BaseModel):
    status: str
    results: List[SpatialFeatureResult]
    total_found: int
    search_parameters: Dict[str, Any]

# ===================================
# Schémas d'analyse de visibilité
# ===================================

class VisibilityAnalysisRequest(BaseModel):
    name: Optional[str] = Field(None, description="Nom de l'analyse")
    description: Optional[str] = Field(None, description="Description de l'analyse")
    observer_longitude: float = Field(..., ge=-180, le=180)
    observer_latitude: float = Field(..., ge=-90, le=90)
    observer_height: float = Field(1.75, ge=0, le=1000, description="Hauteur observateur en mètres")
    max_distance: float = Field(10000.0, gt=0, le=100000, description="Distance maximale en mètres")
    target_height: float = Field(0.0, ge=0, le=1000, description="Hauteur cible en mètres")
    grid_resolution: float = Field(100.0, gt=0, le=1000, description="Résolution grille en mètres")
    earth_curvature: bool = Field(True, description="Prendre en compte la courbure terrestre")
    save_analysis: bool = Field(False, description="Sauvegarder l'analyse en base")

class VisibilityResults(BaseModel):
    visible_area_geojson: Optional[Dict[str, Any]]
    total_area_m2: float
    visible_percentage: float
    calculation_time_seconds: float

class VisibilityAnalysisResponse(BaseModel):
    status: str
    analysis_id: Optional[str]
    observer_point: Dict[str, Any]
    parameters: Dict[str, Any]
    results: VisibilityResults

# ===================================
# Schémas de mesures spatiales
# ===================================

class MeasurementRequest(BaseModel):
    name: Optional[str] = Field(None, description="Nom de la mesure")
    measurement_type: MeasurementType
    geometry: Union[str, Dict[str, Any]] = Field(..., description="Géométrie (WKT ou GeoJSON)")
    notes: Optional[str] = Field(None, description="Notes sur la mesure")

class MeasurementResult(BaseModel):
    id: str
    name: str
    type: str
    value: float
    unit: str
    geometry_geojson: Dict[str, Any]
    notes: Optional[str]
    created_at: str

class MeasurementResponse(BaseModel):
    status: str
    measurement: MeasurementResult

# ===================================
# Schémas de synchronisation
# ===================================

class SyncRequest(BaseModel):
    client_id: str = Field(..., description="Identifiant unique du client")
    strategy: SyncStrategy = Field(SyncStrategy.incremental, description="Stratégie de synchronisation")
    tables: Optional[List[str]] = Field(None, description="Tables à synchroniser (toutes si None)")
    extent: Optional[List[float]] = Field(None, description="Emprise géographique [ouest, sud, est, nord]")
    
    @validator('extent')
    def validate_extent(cls, v):
        if v is not None:
            if len(v) != 4:
                raise ValueError('extent must have exactly 4 values [west, south, east, north]')
            west, south, east, north = v
            if west >= east or south >= north:
                raise ValueError('invalid extent bounds')
        return v

class SyncTableResult(BaseModel):
    records_synced: int
    total_available: Optional[int] = None
    error: Optional[str] = None

class SyncResponse(BaseModel):
    session_id: str
    status: str
    duration_seconds: float
    total_records: int
    tables: Dict[str, SyncTableResult]
    error: Optional[str] = None

class SyncChangeRequest(BaseModel):
    table: str
    operation: str  # insert, update, delete
    record_id: str
    data: Dict[str, Any]

# ===================================
# Schémas du territoire marocain
# ===================================

class MoroccoRegionInfo(BaseModel):
    region_id: str
    region_name: str
    region_name_ar: Optional[str]
    region_type: RegionType
    administrative_level: int

class MoroccoTerritoryValidation(BaseModel):
    is_in_morocco: bool
    coordinates: List[float]
    region_info: Optional[MoroccoRegionInfo]

class MoroccoTerritoryFeature(BaseModel):
    type: str = "Feature"
    properties: Dict[str, Any]
    geometry: Dict[str, Any]

class MoroccoTerritoryCollection(BaseModel):
    type: str = "FeatureCollection"
    features: List[MoroccoTerritoryFeature]
    metadata: Dict[str, Any]

# ===================================
# Schémas de calculs géométriques
# ===================================

class DistanceCalculation(BaseModel):
    distance: float
    unit: str
    points: Dict[str, List[float]]

class AzimuthCalculation(BaseModel):
    azimuth: float
    unit: str
    points: Dict[str, List[float]]

class AreaCalculation(BaseModel):
    area: float
    unit: str
    geometry_type: str

# ===================================
# Schémas de couches spatiales
# ===================================

class SpatialLayerCreate(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    layer_type: str = Field(..., description="Type de couche (vector, raster, mbtiles)")
    data_source: str = Field(..., max_length=500)
    srid: int = Field(4326, description="Système de référence spatial")
    geometry_type: Optional[str] = Field(None, description="Type de géométrie")
    visible: bool = Field(True)
    opacity: float = Field(1.0, ge=0.0, le=1.0)
    z_index: int = Field(0)
    style_config: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

class SpatialLayerResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    layer_type: str
    data_source: str
    srid: int
    geometry_type: Optional[str]
    visible: bool
    opacity: float
    z_index: int
    style_config: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

# ===================================
# Schémas d'entités spatiales
# ===================================

class SpatialFeatureCreate(BaseModel):
    layer_id: str
    name: Optional[str] = Field(None, max_length=255)
    feature_type: str = Field(..., max_length=100)
    geometry: Union[str, Dict[str, Any]] = Field(..., description="Géométrie (WKT ou GeoJSON)")
    properties: Optional[Dict[str, Any]] = None
    style: Optional[Dict[str, Any]] = None

class SpatialFeatureResponse(BaseModel):
    id: str
    layer_id: str
    name: Optional[str]
    feature_type: str
    geometry_geojson: Dict[str, Any]
    properties: Optional[Dict[str, Any]]
    style: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

# ===================================
# Schémas de réponse générique
# ===================================

class APIResponse(BaseModel):
    status: str
    message: Optional[str] = None
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class PaginatedResponse(BaseModel):
    status: str
    data: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int

# ===================================
# Schémas de santé de l'API
# ===================================

class HealthCheckResponse(BaseModel):
    status: str
    postgis_version: Optional[str] = None
    error: Optional[str] = None
    timestamp: str
