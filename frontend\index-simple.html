<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>C2-EW Platform - Module SIG</title>
    <meta name="description" content="Système de Commandement et Contrôle - Module SIG pour le Maroc" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    
    <!-- OpenLayers CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ol@8.2.0/ol.css" />
    
    <!-- Styles de base -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f8fafc;
        }
        
        #root {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }
        
        /* Styles pour la carte OpenLayers */
        .ol-viewport {
            position: relative;
        }
        
        .ol-control {
            position: absolute;
            background-color: rgba(255,255,255,.4);
            border-radius: 4px;
            padding: 2px;
        }
        
        .ol-control:hover {
            background-color: rgba(255,255,255,.6);
        }
        
        /* Styles de chargement */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Styles pour les notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }
        
        .notification.success {
            background-color: #10b981;
        }
        
        .notification.error {
            background-color: #ef4444;
        }
        
        .notification.warning {
            background-color: #f59e0b;
        }
        
        .notification.info {
            background-color: #3b82f6;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* Styles responsifs */
        @media (max-width: 768px) {
            .ol-control {
                font-size: 14px;
            }
            
            .notification {
                top: 10px;
                right: 10px;
                left: 10px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- Indicateur de chargement initial -->
        <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
            color: white;
            flex-direction: column;
        ">
            <div class="loading-spinner" style="margin-bottom: 20px;"></div>
            <h2 style="margin-bottom: 10px;">C2-EW Platform</h2>
            <p style="opacity: 0.8;">Chargement du module SIG...</p>
        </div>
    </div>
    
    <!-- Scripts -->
    <script type="module" src="/src/main-simple.jsx"></script>
    
    <!-- Script de vérification de l'API -->
    <script>
        // Vérifier la disponibilité de l'API backend
        async function checkAPI() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    console.log('✅ API Backend disponible');
                } else {
                    console.warn('⚠️ API Backend non disponible');
                }
            } catch (error) {
                console.warn('⚠️ Impossible de contacter l\'API Backend');
            }
        }
        
        // Vérifier au chargement
        window.addEventListener('load', checkAPI);
        
        // Afficher les informations de débogage
        console.log('🚀 C2-EW Platform - Module SIG');
        console.log('📍 Frontend: http://localhost:3001');
        console.log('📡 Backend: http://localhost:8000');
        console.log('🗺️ OpenLayers: v8.2.0');
        console.log('🇲🇦 Territoire: Maroc');
    </script>
</body>
</html>
