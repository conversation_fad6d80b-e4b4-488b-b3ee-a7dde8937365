"""
Service de gestion des tuiles MBTiles
Module SIG C2-EW - Support tuiles vectorielles et raster hors ligne
"""

import asyncio
import sqlite3
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from uuid import UUID
import gzip
import io

from fastapi import HTTPException
from fastapi.responses import Response
import aiofiles

from ..models.gis_models import MBTilesDataset
from ..core.database import get_async_session
from ..core.config import settings

logger = logging.getLogger(__name__)

class TileService:
    """Service de gestion des tuiles MBTiles"""
    
    def __init__(self):
        self.mbtiles_connections = {}
        self.tile_cache = {}  # Cache en mémoire pour les tuiles fréquentes
        self.max_cache_size = 1000
        
    async def initialize(self):
        """Initialisation du service de tuiles"""
        try:
            # Créer le répertoire des tuiles s'il n'existe pas
            tiles_dir = Path(settings.MBTILES_DIR)
            tiles_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info("✅ Service de tuiles initialisé")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation du service de tuiles: {e}")
            raise
    
    async def get_mbtiles_connection(self, dataset_name: str) -> sqlite3.Connection:
        """Obtenir une connexion à un fichier MBTiles"""
        
        if dataset_name not in self.mbtiles_connections:
            # Chercher le dataset en base
            async with get_async_session() as session:
                dataset = await session.query(MBTilesDataset).filter(
                    MBTilesDataset.name == dataset_name,
                    MBTilesDataset.is_active == True
                ).first()
                
                if not dataset:
                    raise HTTPException(status_code=404, detail=f"Dataset MBTiles '{dataset_name}' non trouvé")
                
                # Vérifier que le fichier existe
                mbtiles_path = Path(dataset.file_path)
                if not mbtiles_path.exists():
                    raise HTTPException(status_code=404, detail=f"Fichier MBTiles non trouvé: {dataset.file_path}")
                
                # Créer la connexion SQLite
                conn = sqlite3.connect(str(mbtiles_path), check_same_thread=False)
                conn.row_factory = sqlite3.Row
                
                self.mbtiles_connections[dataset_name] = {
                    'connection': conn,
                    'dataset': dataset
                }
        
        return self.mbtiles_connections[dataset_name]['connection']
    
    async def get_tile(
        self, 
        dataset_name: str, 
        z: int, 
        x: int, 
        y: int,
        format_override: Optional[str] = None
    ) -> Tuple[bytes, str]:
        """Obtenir une tuile depuis un dataset MBTiles"""
        
        try:
            # Vérifier le cache
            cache_key = f"{dataset_name}_{z}_{x}_{y}"
            if cache_key in self.tile_cache:
                return self.tile_cache[cache_key]
            
            # Obtenir la connexion
            conn = await self.get_mbtiles_connection(dataset_name)
            dataset_info = self.mbtiles_connections[dataset_name]['dataset']
            
            # Vérifier les limites de zoom
            if z < dataset_info.min_zoom or z > dataset_info.max_zoom:
                raise HTTPException(status_code=404, detail="Niveau de zoom non disponible")
            
            # Requête pour obtenir la tuile
            # Note: MBTiles utilise le schéma TMS (y inversé par rapport à XYZ)
            tms_y = (2 ** z) - 1 - y
            
            cursor = conn.cursor()
            cursor.execute("""
                SELECT tile_data FROM tiles 
                WHERE zoom_level = ? AND tile_column = ? AND tile_row = ?
            """, (z, x, tms_y))
            
            result = cursor.fetchone()
            
            if not result:
                raise HTTPException(status_code=404, detail="Tuile non trouvée")
            
            tile_data = result[0]
            
            # Déterminer le type de contenu
            content_type = self._get_content_type(dataset_info.format, format_override)
            
            # Décompresser si nécessaire (tuiles vectorielles souvent compressées)
            if dataset_info.format == 'pbf':
                # Les tuiles vectorielles peuvent être compressées en gzip
                try:
                    # Tenter de décompresser
                    decompressed = gzip.decompress(tile_data)
                    tile_data = decompressed
                except:
                    # Si la décompression échoue, utiliser les données telles quelles
                    pass
            
            # Mettre en cache si la tuile est petite
            if len(tile_data) < 100000:  # 100KB
                if len(self.tile_cache) >= self.max_cache_size:
                    # Supprimer une entrée aléatoire du cache
                    self.tile_cache.pop(next(iter(self.tile_cache)))
                
                self.tile_cache[cache_key] = (tile_data, content_type)
            
            # Mettre à jour la date d'accès
            await self._update_last_accessed(dataset_name)
            
            return tile_data, content_type
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ Erreur obtention tuile {dataset_name}/{z}/{x}/{y}: {e}")
            raise HTTPException(status_code=500, detail=f"Erreur serveur: {str(e)}")
    
    def _get_content_type(self, format_type: str, format_override: Optional[str] = None) -> str:
        """Déterminer le type de contenu MIME"""
        
        format_to_use = format_override or format_type
        
        content_types = {
            'pbf': 'application/x-protobuf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'webp': 'image/webp',
            'mvt': 'application/x-protobuf'  # Mapbox Vector Tiles
        }
        
        return content_types.get(format_to_use.lower(), 'application/octet-stream')
    
    async def get_tileset_metadata(self, dataset_name: str) -> Dict[str, Any]:
        """Obtenir les métadonnées d'un tileset MBTiles"""
        
        try:
            conn = await self.get_mbtiles_connection(dataset_name)
            dataset_info = self.mbtiles_connections[dataset_name]['dataset']
            
            # Lire les métadonnées depuis la table metadata
            cursor = conn.cursor()
            cursor.execute("SELECT name, value FROM metadata")
            
            metadata = {}
            for row in cursor.fetchall():
                name, value = row
                # Tenter de parser le JSON si possible
                try:
                    metadata[name] = json.loads(value)
                except:
                    metadata[name] = value
            
            # Ajouter les informations du dataset
            result = {
                'name': dataset_info.name,
                'description': dataset_info.description,
                'format': dataset_info.format,
                'minzoom': dataset_info.min_zoom,
                'maxzoom': dataset_info.max_zoom,
                'bounds': await self._get_bounds_array(dataset_info),
                'center': await self._calculate_center(dataset_info),
                'metadata': metadata,
                'tile_stats': await self._get_tile_stats(conn),
                'last_accessed': dataset_info.last_accessed.isoformat() if dataset_info.last_accessed else None
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erreur obtention métadonnées {dataset_name}: {e}")
            raise HTTPException(status_code=500, detail=f"Erreur obtention métadonnées: {str(e)}")
    
    async def _get_bounds_array(self, dataset: MBTilesDataset) -> List[float]:
        """Convertir les bounds PostGIS en array [ouest, sud, est, nord]"""
        
        if not dataset.bounds:
            return [-180, -85, 180, 85]  # Bounds par défaut
        
        try:
            # Utiliser PostGIS pour extraire les coordonnées
            async with get_async_session() as session:
                result = await session.execute(text("""
                    SELECT 
                        ST_XMin(bounds) as west,
                        ST_YMin(bounds) as south,
                        ST_XMax(bounds) as east,
                        ST_YMax(bounds) as north
                    FROM mbtiles_datasets 
                    WHERE id = :dataset_id
                """), {'dataset_id': dataset.id})
                
                row = result.fetchone()
                if row:
                    return [row.west, row.south, row.east, row.north]
                
        except Exception as e:
            logger.error(f"❌ Erreur extraction bounds: {e}")
        
        return [-180, -85, 180, 85]
    
    async def _calculate_center(self, dataset: MBTilesDataset) -> List[float]:
        """Calculer le centre du tileset"""
        
        bounds = await self._get_bounds_array(dataset)
        center_lon = (bounds[0] + bounds[2]) / 2
        center_lat = (bounds[1] + bounds[3]) / 2
        
        # Zoom par défaut basé sur l'étendue
        zoom = min(dataset.max_zoom, max(dataset.min_zoom, 8))
        
        return [center_lon, center_lat, zoom]
    
    async def _get_tile_stats(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Obtenir les statistiques des tuiles"""
        
        try:
            cursor = conn.cursor()
            
            # Compter le nombre total de tuiles
            cursor.execute("SELECT COUNT(*) FROM tiles")
            total_tiles = cursor.fetchone()[0]
            
            # Obtenir la distribution par niveau de zoom
            cursor.execute("""
                SELECT zoom_level, COUNT(*) as count 
                FROM tiles 
                GROUP BY zoom_level 
                ORDER BY zoom_level
            """)
            
            zoom_distribution = {}
            for row in cursor.fetchall():
                zoom_distribution[row[0]] = row[1]
            
            # Calculer la taille totale
            cursor.execute("SELECT SUM(LENGTH(tile_data)) FROM tiles")
            total_size = cursor.fetchone()[0] or 0
            
            return {
                'total_tiles': total_tiles,
                'total_size_bytes': total_size,
                'zoom_distribution': zoom_distribution
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur calcul statistiques tuiles: {e}")
            return {}
    
    async def _update_last_accessed(self, dataset_name: str):
        """Mettre à jour la date de dernier accès"""
        
        try:
            async with get_async_session() as session:
                await session.execute(text("""
                    UPDATE mbtiles_datasets 
                    SET last_accessed = NOW() 
                    WHERE name = :name
                """), {'name': dataset_name})
                
                await session.commit()
                
        except Exception as e:
            logger.error(f"❌ Erreur mise à jour last_accessed: {e}")
    
    async def list_datasets(self) -> List[Dict[str, Any]]:
        """Lister tous les datasets MBTiles disponibles"""
        
        try:
            async with get_async_session() as session:
                datasets = await session.query(MBTilesDataset).filter(
                    MBTilesDataset.is_active == True
                ).all()
                
                result = []
                for dataset in datasets:
                    bounds = await self._get_bounds_array(dataset)
                    center = await self._calculate_center(dataset)
                    
                    dataset_info = {
                        'id': str(dataset.id),
                        'name': dataset.name,
                        'description': dataset.description,
                        'format': dataset.format,
                        'minzoom': dataset.min_zoom,
                        'maxzoom': dataset.max_zoom,
                        'bounds': bounds,
                        'center': center,
                        'file_size': dataset.file_size,
                        'last_accessed': dataset.last_accessed.isoformat() if dataset.last_accessed else None,
                        'created_at': dataset.created_at.isoformat()
                    }
                    
                    result.append(dataset_info)
                
                return result
                
        except Exception as e:
            logger.error(f"❌ Erreur listage datasets: {e}")
            return []
    
    async def create_dataset(
        self,
        name: str,
        description: str,
        file_path: str,
        format_type: str = 'pbf'
    ) -> Dict[str, Any]:
        """Créer un nouveau dataset MBTiles"""
        
        try:
            # Vérifier que le fichier existe
            mbtiles_path = Path(file_path)
            if not mbtiles_path.exists():
                raise HTTPException(status_code=400, detail=f"Fichier MBTiles non trouvé: {file_path}")
            
            # Analyser le fichier MBTiles
            conn = sqlite3.connect(str(mbtiles_path))
            cursor = conn.cursor()
            
            # Obtenir les métadonnées
            cursor.execute("SELECT name, value FROM metadata")
            metadata = dict(cursor.fetchall())
            
            # Obtenir les limites de zoom
            cursor.execute("SELECT MIN(zoom_level), MAX(zoom_level) FROM tiles")
            min_zoom, max_zoom = cursor.fetchone()
            
            # Obtenir les bounds si disponibles
            bounds_wkt = None
            if 'bounds' in metadata:
                try:
                    bounds_coords = [float(x) for x in metadata['bounds'].split(',')]
                    bounds_wkt = f"POLYGON(({bounds_coords[0]} {bounds_coords[1]}, {bounds_coords[2]} {bounds_coords[1]}, {bounds_coords[2]} {bounds_coords[3]}, {bounds_coords[0]} {bounds_coords[3]}, {bounds_coords[0]} {bounds_coords[1]}))"
                except:
                    pass
            
            conn.close()
            
            # Créer l'enregistrement en base
            async with get_async_session() as session:
                dataset = MBTilesDataset(
                    name=name,
                    description=description,
                    file_path=str(mbtiles_path),
                    file_size=mbtiles_path.stat().st_size,
                    format=format_type,
                    min_zoom=min_zoom or 0,
                    max_zoom=max_zoom or 18,
                    bounds=bounds_wkt,
                    metadata=metadata,
                    is_active=True
                )
                
                session.add(dataset)
                await session.commit()
                await session.refresh(dataset)
                
                return {
                    'id': str(dataset.id),
                    'name': dataset.name,
                    'description': dataset.description,
                    'format': dataset.format,
                    'minzoom': dataset.min_zoom,
                    'maxzoom': dataset.max_zoom,
                    'file_size': dataset.file_size,
                    'metadata': metadata
                }
                
        except Exception as e:
            logger.error(f"❌ Erreur création dataset: {e}")
            raise HTTPException(status_code=500, detail=f"Erreur création dataset: {str(e)}")
    
    async def delete_dataset(self, dataset_name: str) -> bool:
        """Supprimer un dataset MBTiles"""
        
        try:
            async with get_async_session() as session:
                dataset = await session.query(MBTilesDataset).filter(
                    MBTilesDataset.name == dataset_name
                ).first()
                
                if not dataset:
                    raise HTTPException(status_code=404, detail=f"Dataset '{dataset_name}' non trouvé")
                
                # Fermer la connexion si elle existe
                if dataset_name in self.mbtiles_connections:
                    self.mbtiles_connections[dataset_name]['connection'].close()
                    del self.mbtiles_connections[dataset_name]
                
                # Supprimer de la base
                await session.delete(dataset)
                await session.commit()
                
                # Optionnellement supprimer le fichier
                # (à faire avec précaution)
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Erreur suppression dataset: {e}")
            raise HTTPException(status_code=500, detail=f"Erreur suppression dataset: {str(e)}")
    
    async def clear_cache(self):
        """Vider le cache des tuiles"""
        self.tile_cache.clear()
        logger.info("🧹 Cache des tuiles vidé")
    
    async def close(self):
        """Fermer toutes les connexions"""
        try:
            for dataset_name, info in self.mbtiles_connections.items():
                info['connection'].close()
            
            self.mbtiles_connections.clear()
            self.tile_cache.clear()
            
            logger.info("✅ Service de tuiles fermé")
            
        except Exception as e:
            logger.error(f"❌ Erreur fermeture service tuiles: {e}")

# Instance globale du service
tile_service = TileService()
