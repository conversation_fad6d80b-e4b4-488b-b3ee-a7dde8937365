"""Create GIS tables for spatial module

Revision ID: 001_gis_tables
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from geoalchemy2 import Geometry, Geography
import uuid

# revision identifiers, used by Alembic.
revision = '001_gis_tables'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create GIS tables and spatial extensions"""
    
    # Enable PostGIS extensions
    op.execute("CREATE EXTENSION IF NOT EXISTS postgis")
    op.execute("CREATE EXTENSION IF NOT EXISTS postgis_topology")
    op.execute("CREATE EXTENSION IF NOT EXISTS postgis_raster")
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    op.execute('CREATE EXTENSION IF NOT EXISTS pg_trgm')
    
    # Create spatial_layers table
    op.create_table(
        'spatial_layers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('description', sa.Text),
        sa.Column('layer_type', sa.String(50), nullable=False),
        sa.Column('data_source', sa.String(500), nullable=False),
        sa.Column('srid', sa.Integer, default=4326),
        sa.Column('geometry_type', sa.String(50)),
        sa.Column('visible', sa.Boolean, default=True),
        sa.Column('opacity', sa.Float, default=1.0),
        sa.Column('z_index', sa.Integer, default=0),
        sa.Column('style_config', postgresql.JSONB),
        sa.Column('metadata', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.CheckConstraint('opacity >= 0.0 AND opacity <= 1.0', name='chk_opacity'),
        sa.CheckConstraint("layer_type IN ('vector', 'raster', 'mbtiles')", name='chk_layer_type')
    )
    
    # Create spatial_features table
    op.create_table(
        'spatial_features',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('layer_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('spatial_layers.id', ondelete='CASCADE'), nullable=False),
        sa.Column('geometry', Geometry('GEOMETRY', srid=4326, spatial_index=True)),
        sa.Column('geography', Geography('GEOMETRY', srid=4326, spatial_index=True)),
        sa.Column('name', sa.String(255), index=True),
        sa.Column('feature_type', sa.String(100), nullable=False, index=True),
        sa.Column('properties', postgresql.JSONB),
        sa.Column('style', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id'))
    )
    
    # Create morocco_territory table
    op.create_table(
        'morocco_territory',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('region_name', sa.String(255), nullable=False, index=True),
        sa.Column('region_name_ar', sa.String(255)),
        sa.Column('region_type', sa.String(50), nullable=False, index=True),
        sa.Column('geometry', Geometry('MULTIPOLYGON', srid=4326, spatial_index=True), nullable=False),
        sa.Column('administrative_level', sa.Integer, default=1),
        sa.Column('parent_region_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('morocco_territory.id')),
        sa.Column('official_code', sa.String(20), index=True),
        sa.Column('population', sa.Integer),
        sa.Column('area_km2', sa.Float),
        sa.Column('is_official', sa.Boolean, default=True),
        sa.Column('source_authority', sa.String(255), default='Ministère de l\'Intérieur'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.CheckConstraint('administrative_level BETWEEN 1 AND 3', name='chk_admin_level'),
        sa.CheckConstraint("region_type IN ('mainland', 'sahara', 'maritime')", name='chk_region_type')
    )
    
    # Create mbtiles_datasets table
    op.create_table(
        'mbtiles_datasets',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('name', sa.String(255), unique=True, nullable=False, index=True),
        sa.Column('description', sa.Text),
        sa.Column('file_path', sa.String(500), nullable=False),
        sa.Column('file_size', sa.BigInteger, default=0),
        sa.Column('format', sa.String(10), default='pbf'),
        sa.Column('min_zoom', sa.Integer, default=0),
        sa.Column('max_zoom', sa.Integer, default=18),
        sa.Column('bounds', Geometry('POLYGON', srid=4326)),
        sa.Column('metadata', postgresql.JSONB),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('last_accessed', sa.DateTime(timezone=True)),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.CheckConstraint('min_zoom >= 0 AND min_zoom <= 22', name='chk_min_zoom'),
        sa.CheckConstraint('max_zoom >= 0 AND max_zoom <= 22', name='chk_max_zoom'),
        sa.CheckConstraint('min_zoom <= max_zoom', name='chk_zoom_range'),
        sa.CheckConstraint("format IN ('pbf', 'png', 'jpg')", name='chk_format')
    )
    
    # Create visibility_analyses table
    op.create_table(
        'visibility_analyses',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('description', sa.Text),
        sa.Column('observer_point', Geometry('POINT', srid=4326, spatial_index=True), nullable=False),
        sa.Column('observer_height', sa.Float, default=1.75),
        sa.Column('max_distance', sa.Float, default=10000.0),
        sa.Column('target_height', sa.Float, default=0.0),
        sa.Column('earth_curvature', sa.Boolean, default=True),
        sa.Column('dem_dataset', sa.String(255)),
        sa.Column('dem_resolution', sa.Float),
        sa.Column('visible_area', Geometry('MULTIPOLYGON', srid=4326, spatial_index=True)),
        sa.Column('total_area', sa.Float),
        sa.Column('visible_percentage', sa.Float),
        sa.Column('calculation_time', sa.Float),
        sa.Column('grid_resolution', sa.Float, default=100.0),
        sa.Column('parameters', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.CheckConstraint('observer_height >= 0', name='chk_observer_height'),
        sa.CheckConstraint('max_distance > 0', name='chk_max_distance'),
        sa.CheckConstraint('grid_resolution > 0', name='chk_grid_resolution')
    )
    
    # Create spatial_measurements table
    op.create_table(
        'spatial_measurements',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('name', sa.String(255), index=True),
        sa.Column('measurement_type', sa.String(50), nullable=False, index=True),
        sa.Column('geometry', Geometry('GEOMETRY', srid=4326, spatial_index=True), nullable=False),
        sa.Column('value', sa.Float, nullable=False),
        sa.Column('unit', sa.String(20), nullable=False),
        sa.Column('perimeter', sa.Float),
        sa.Column('area', sa.Float),
        sa.Column('volume', sa.Float),
        sa.Column('projection_used', sa.String(50)),
        sa.Column('calculation_method', sa.String(100)),
        sa.Column('notes', sa.Text),
        sa.Column('properties', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id')),
        sa.CheckConstraint("measurement_type IN ('distance', 'area', 'volume', 'perimeter')", name='chk_measurement_type'),
        sa.CheckConstraint('value >= 0', name='chk_value')
    )
    
    # Create spatial_cache table
    op.create_table(
        'spatial_cache',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('cache_key', sa.String(255), unique=True, nullable=False, index=True),
        sa.Column('cache_type', sa.String(50), nullable=False, index=True),
        sa.Column('data', postgresql.JSONB),
        sa.Column('geometry_data', Geometry('GEOMETRY', srid=4326)),
        sa.Column('source_id', postgresql.UUID(as_uuid=True)),
        sa.Column('expires_at', sa.DateTime(timezone=True)),
        sa.Column('last_accessed', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('access_count', sa.Integer, default=0),
        sa.Column('data_size', sa.Integer, default=0),
        sa.Column('priority', sa.Integer, default=1),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.CheckConstraint("cache_type IN ('layer', 'feature', 'tile', 'analysis')", name='chk_cache_type'),
        sa.CheckConstraint('priority >= 1', name='chk_priority')
    )
    
    # Create sync_logs table
    op.create_table(
        'sync_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('sync_type', sa.String(50), nullable=False, index=True),
        sa.Column('table_name', sa.String(100), nullable=False, index=True),
        sa.Column('record_id', postgresql.UUID(as_uuid=True)),
        sa.Column('status', sa.String(20), nullable=False, index=True),
        sa.Column('error_message', sa.Text),
        sa.Column('local_data', postgresql.JSONB),
        sa.Column('remote_data', postgresql.JSONB),
        sa.Column('conflict_resolution', sa.String(50)),
        sa.Column('resolved_data', postgresql.JSONB),
        sa.Column('sync_session_id', postgresql.UUID(as_uuid=True)),
        sa.Column('retry_count', sa.Integer, default=0),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.CheckConstraint("sync_type IN ('upload', 'download', 'conflict')", name='chk_sync_type'),
        sa.CheckConstraint("status IN ('pending', 'success', 'error', 'conflict')", name='chk_status'),
        sa.CheckConstraint("conflict_resolution IN ('local_wins', 'remote_wins', 'merge')", name='chk_conflict_resolution')
    )


def downgrade():
    """Drop GIS tables"""
    
    # Drop tables in reverse order
    op.drop_table('sync_logs')
    op.drop_table('spatial_cache')
    op.drop_table('spatial_measurements')
    op.drop_table('visibility_analyses')
    op.drop_table('mbtiles_datasets')
    op.drop_table('morocco_territory')
    op.drop_table('spatial_features')
    op.drop_table('spatial_layers')
    
    # Note: We don't drop PostGIS extensions as they might be used by other applications
