{"name": "wkt-parser", "version": "1.5.2", "description": "wkt-parser ===", "main": "dist/wkt.cjs", "module": "index.js", "type": "module", "scripts": {"test": "node test.js", "build": "rollup -c", "pretest": "npm run build", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/proj4js/wkt-parser.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/proj4js/wkt-parser/issues"}, "homepage": "https://github.com/proj4js/wkt-parser#readme", "devDependencies": {"@eslint/eslintrc": "^3.3.1", "eslint": "^9.26.0", "js-struct-compare": "^1.1.0", "rollup": "^4.24.0", "tape": "^4.6.3"}}