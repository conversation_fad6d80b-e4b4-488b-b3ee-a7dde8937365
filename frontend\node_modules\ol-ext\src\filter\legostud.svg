<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
        xmlns:osb="http://www.openswatchbook.org/uri/2009/osb"
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:cc="http://creativecommons.org/ns#"
        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
        width="79.111137mm"
        height="69.723396mm"
        viewBox="0 0 280.31505 247.0514"
        id="svg2"
        version="1.1"
        inkscape:version="0.91 r13725"
        sodipodi:docname="legostud.svg">
  <defs
     id="defs4">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4479">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop4481" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop4483" />
    </linearGradient>
    <linearGradient
       id="linearGradient4264"
       osb:paint="gradient">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop4266" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop4268" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4165">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop4167" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop4169" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4165"
       id="radialGradient4171"
       cx="247.58907"
       cy="806.17749"
       fx="247.58907"
       fy="806.17749"
       r="16.712261"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.1673248,0,0,1.1673248,-43.580899,-140.69401)" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4209"
       x="-0.023999365"
       width="1.0479987"
       y="-0.024000635"
       height="1.0480013">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.36829105"
         id="feGaussianBlur4211" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4260"
       x="-0.035885267"
       width="1.0717705"
       y="-0.036115468"
       height="1.0722309">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.3757032"
         id="feGaussianBlur4262" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4406"
       x="-0.023999998"
       width="1.048"
       y="-0.024000002"
       height="1.048">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.55023435"
         id="feGaussianBlur4408" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4479"
       id="radialGradient4485"
       cx="243.34979"
       cy="799.60266"
       fx="243.34979"
       fy="799.60266"
       r="26.382999"
       gradientTransform="matrix(0.94262735,-0.96925492,1.4109273,1.372166,-1114.2197,-61.71694)"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4507"
       x="-0.011999792"
       width="1.0239996"
       y="-0.012000208"
       height="1.0240004">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.26382543"
         id="feGaussianBlur4509" />
    </filter>
    <linearGradient
       y2="0"
       x2="1"
       y1="0"
       x1="0"
       id="grad">
      <stop
         id="stop4520"
         stop-opacity="0.25"
         stop-color="#000000"
         offset="20%" />
      <stop
         id="stop4522"
         stop-opacity="0.5"
         stop-color="#000000"
         offset="80%" />
    </linearGradient>
    <path
       d="M 0,0 5,10 Q 0,0 -5,10 Z"
       id="arrow"
       inkscape:connector-curvature="0"
       style="stroke:none" />
    <text
       font-style="italic"
       font-size="16"
       text-align="middle"
       dy="8.2103413e-035"
       y="0"
       x="0"
       transform="matrix(0.69282032,-0.4,0.5,0.8660254,0,0)"
       id="stud_text"
       style="font-style:italic;font-size:16px;stroke:none">LEGO</text>
    <g
       id="stud">
      <ellipse
         id="ellipse4527"
         ry="17.6777"
         rx="30.618601"
         cy="3"
         cx="6"
         style="fill:#000000;fill-opacity:0.25;stroke:none" />
      <path
         id="path4529"
         d="m -30.6186,-17 a 30.6186,17.6777 0 0 1 61.2372,0 l 0,17 a 30.6186,17.6777 0 0 1 -61.2372,0 z"
         inkscape:connector-curvature="0" />
      <path
         id="path4531"
         d="m -30.6186,-17 a 30.6186,17.6777 0 0 0 61.2372,0 l 0,17 a 30.6186,17.6777 0 0 1 -61.2372,0 z"
         inkscape:connector-curvature="0"
         style="fill:url(#grad)" />
      <use
         id="use4533"
         xlink:href="#stud_text"
         transform="translate(1,-16)"
         style="fill:#000000"
         x="0"
         y="0"
         width="100%"
         height="100%" />
      <use
         id="use4535"
         xlink:href="#stud_text"
         transform="translate(0,-17)"
         x="0"
         y="0"
         width="100%"
         height="100%" />
    </g>
    <g
       id="brick">
      <path
         id="path4538"
         d="m 0,40 69.282,-40 0,-96 L 0,-136 l -69.282,40 0,96 z"
         inkscape:connector-curvature="0" />
      <path
         id="path4540"
         d="m 0,40 0,-96 69.282,-40 0,96 z"
         inkscape:connector-curvature="0"
         style="fill:#000000;fill-opacity:0.5" />
      <path
         id="path4542"
         d="m 0,40 0,-96 -69.282,-40 0,96 z"
         inkscape:connector-curvature="0"
         style="fill:#000000;fill-opacity:0.25" />
      <use
         id="use4544"
         xlink:href="#stud"
         transform="translate(0,-96)"
         x="0"
         y="0"
         width="100%"
         height="100%" />
    </g>
    <g
       id="plate">
      <path
         id="path4547"
         d="m 0,40 69.282,-40 0,-32 -138.564,-80 -69.282,40 0,32 z"
         inkscape:connector-curvature="0" />
      <path
         id="path4549"
         d="m 0,40 0,-32 69.282,-40 0,32 z"
         inkscape:connector-curvature="0"
         style="fill:#000000;fill-opacity:0.5" />
      <path
         id="path4551"
         d="m 0,40 0,-32 -138.564,-80 0,32 z"
         inkscape:connector-curvature="0"
         style="fill:#000000;fill-opacity:0.25" />
      <use
         id="use4553"
         xlink:href="#stud"
         transform="translate(-69.282,-72)"
         x="0"
         y="0"
         width="100%"
         height="100%" />
      <use
         id="use4555"
         xlink:href="#stud"
         transform="translate(0,-32)"
         x="0"
         y="0"
         width="100%"
         height="100%" />
    </g>
    <g
       font-size="16"
       id="main"
       style="font-size:16px;font-family:sans-serif;text-anchor:middle;fill:#000000;stroke:#000000;stroke-linecap:round;stroke-linejoin:round">
      <g
         id="g4558"
         transform="translate(10,-5)">
        <use
           id="use4560"
           xlink:href="#brick"
           transform="translate(69.282,-40)"
           style="fill:#ffcc00"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <text
           id="text4562"
           y="-186"
           x="69"
           style="text-anchor:middle;stroke:none">4.8 mm</text>
        <line
           id="line4564"
           y2="-180"
           x2="100"
           y1="-180"
           x1="39" />
        <use
           id="use4566"
           transform="matrix(0,-1,1,0,39,-180)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4568"
           transform="matrix(0,1,-1,0,100,-180)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4570"
           y2="-185"
           x2="39"
           y1="-161"
           x1="39" />
        <line
           id="line4572"
           y2="-185"
           x2="100"
           y1="-161"
           x1="100" />
        <text
           id="text4574"
           y="-170"
           x="160"
           style="text-anchor:start;stroke:none">1.7 mm</text>
        <line
           id="line4576"
           y2="-159"
           x2="147"
           y1="-176"
           x1="147" />
        <use
           id="use4578"
           transform="translate(147,-176)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4580"
           transform="matrix(-1,0,0,-1,147,-159)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4582"
           y2="-181"
           x2="156"
           y1="-150"
           x1="102" />
        <line
           id="line4584"
           y2="-164"
           x2="156"
           y1="-133"
           x1="102" />
        <text
           id="text4586"
           y="-80"
           x="150"
           style="text-anchor:start;stroke:none">
          <tspan
             id="tspan4588">H = 9.6 mm</tspan>
          <tspan
             id="tspan4590"
             dy="1.8479102e-034"
             x="165">= 3 × h</tspan>
          <tspan
             id="tspan4592"
             dy="1.7902963e-034"
             x="165">= 1.2 × P</tspan>
        </text>
        <line
           id="line4594"
           y2="-35"
           x2="147"
           y1="-131"
           x1="147" />
        <use
           id="use4596"
           transform="translate(147,-131)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4598"
           transform="matrix(-1,0,0,-1,147,-35)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <text
           id="text4600"
           y="0"
           x="130"
           style="text-anchor:start;stroke:none">
          <tspan
             id="tspan4602">P − 0.2 mm</tspan>
          <tspan
             id="tspan4604"
             dy="1.8528684e-034"
             x="130">= 7.8 mm</tspan>
        </text>
        <line
           id="line4606"
           y2="-35"
           x2="147"
           y1="5"
           x1="78" />
        <use
           id="use4608"
           transform="matrix(-0.5,-0.8660254,0.8660254,-0.5,78,5)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4610"
           transform="matrix(0.5,0.8660254,-0.8660254,0.5,147,-35)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4612"
           y2="-126"
           x2="156"
           y1="-134"
           x1="142" />
        <line
           id="line4614"
           y2="-30"
           x2="156"
           y1="-38"
           x1="142" />
        <line
           id="line4616"
           y2="10"
           x2="87"
           y1="2"
           x1="73" />
      </g>
      <g
         id="g4618">
        <use
           id="use4620"
           xlink:href="#plate"
           style="fill:#ff0000"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <text
           id="text4622"
           y="-190"
           x="-170"
           style="text-anchor:start;stroke:none">
          <tspan
             id="tspan4624">P = 8.0 mm</tspan>
          <tspan
             id="tspan4626"
             dy="1.8722218e-034"
             x="-155">= 5/6 × H</tspan>
          <tspan
             id="tspan4628"
             dy="1.8727315e-034"
             x="-155">= 2.5 × h</tspan>
        </text>
        <line
           id="line4630"
           y2="-190"
           x2="-69"
           y1="-150"
           x1="0" />
        <use
           id="use4632"
           transform="matrix(-0.5,0.8660254,-0.8660254,-0.5,0,-150)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4634"
           transform="matrix(0.5,-0.8660254,0.8660254,0.5,-69,-190)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4636"
           y2="-160"
           x2="0"
           y1="-50"
           x1="0" />
        <line
           id="line4638"
           y2="-200"
           x2="-69"
           y1="-90"
           x1="-69" />
        <text
           id="text4640"
           y="-130"
           x="-32"
           style="text-anchor:middle;stroke:none">3.2 mm</text>
        <line
           id="line4642"
           y2="-120"
           x2="-48"
           y1="-105"
           x1="-24" />
        <use
           id="use4644"
           transform="matrix(-0.5,0.8660254,-0.8660254,-0.5,-24,-105)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4646"
           transform="matrix(0.5,-0.8660254,0.8660254,0.5,-48,-120)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4648"
           y2="-110"
           x2="-24"
           y1="-62"
           x1="-24" />
        <line
           id="line4650"
           y2="-125"
           x2="-48"
           y1="-78"
           x1="-48" />
        <text
           id="text4652"
           y="20"
           x="-90"
           style="text-anchor:end;stroke:none">
          <tspan
             id="tspan4654">2 × P − 0.2 mm</tspan>
          <tspan
             id="tspan4656"
             dy="1.8926102e-034"
             x="-90">= 15.8 mm</tspan>
        </text>
        <line
           id="line4658"
           y2="-35"
           x2="-147"
           y1="45"
           x1="-9" />
        <use
           id="use4660"
           transform="matrix(-0.5,0.8660254,-0.8660254,-0.5,-9,45)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4662"
           transform="matrix(0.5,-0.8660254,0.8660254,0.5,-147,-35)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <text
           id="text4664"
           y="-60"
           x="-247"
           style="text-anchor:start;stroke:none">
          <tspan
             id="tspan4666">h = 3.2 mm</tspan>
          <tspan
             id="tspan4668"
             dy="1.8926892e-034"
             x="-235">= 1/3 × H</tspan>
          <tspan
             id="tspan4670"
             dy="1.8927287e-034"
             x="-235">= 0.4 × P</tspan>
        </text>
        <line
           id="line4672"
           y2="-35"
           x2="-147"
           y1="-67"
           x1="-147" />
        <use
           id="use4674"
           transform="translate(-147,-67)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <use
           id="use4676"
           transform="matrix(-1,0,0,-1,-147,-35)"
           xlink:href="#arrow"
           x="0"
           y="0"
           width="100%"
           height="100%" />
        <line
           id="line4678"
           y2="-62"
           x2="-156"
           y1="-70"
           x1="-142" />
        <line
           id="line4680"
           y2="-30"
           x2="-156"
           y1="-38"
           x1="-142" />
        <line
           id="line4682"
           y2="50"
           x2="-17"
           y1="42"
           x1="-3" />
      </g>
    </g>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter5268"
       x="-0.0091189481"
       width="1.0182379"
       y="-0.017542355"
       height="1.0350847">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.06438475"
         id="feGaussianBlur5270" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter5328"
       x="-0.018060395"
       width="1.0361208"
       y="-0.035760824"
       height="1.0715218">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.1314258"
         id="feGaussianBlur5330" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter5602"
       x="-0.019410826"
       width="1.0388217"
       y="-0.03143103"
       height="1.0628622">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.15183545"
         id="feGaussianBlur5604" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter5676"
       x="-0.019262506"
       width="1.038525"
       y="-0.031827867"
       height="1.0636557">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.1528711"
         id="feGaussianBlur5678" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4406-3"
       x="-0.023999998"
       width="1.048"
       y="-0.024000002"
       height="1.048">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.55023435"
         id="feGaussianBlur4408-6" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4165"
       id="radialGradient4171-0"
       cx="247.58907"
       cy="806.17749"
       fx="247.58907"
       fy="806.17749"
       r="16.712261"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.1673248,0,0,1.1673248,-43.580899,-140.69401)" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4209-7"
       x="-0.023999365"
       width="1.0479987"
       y="-0.024000635"
       height="1.0480013">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.36829105"
         id="feGaussianBlur4211-6" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4260-5"
       x="-0.035885267"
       width="1.0717705"
       y="-0.036115468"
       height="1.0722309">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.3757032"
         id="feGaussianBlur4262-7" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4479"
       id="radialGradient4485-6"
       cx="243.34979"
       cy="799.60266"
       fx="243.34979"
       fy="799.60266"
       r="26.382999"
       gradientTransform="matrix(0.94262735,-0.96925492,1.4109273,1.372166,-1114.2197,-61.71694)"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4507-13"
       x="-0.011999792"
       width="1.0239996"
       y="-0.012000208"
       height="1.0240004">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.26382543"
         id="feGaussianBlur4509-2" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4406-3-61"
       x="-0.023999998"
       width="1.048"
       y="-0.024000002"
       height="1.048">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.55023435"
         id="feGaussianBlur4408-6-3" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4165"
       id="radialGradient4171-0-4"
       cx="247.58907"
       cy="806.17749"
       fx="247.58907"
       fy="806.17749"
       r="16.712261"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.1673248,0,0,1.1673248,-43.580899,-140.69401)" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4209-7-7"
       x="-0.023999365"
       width="1.0479987"
       y="-0.024000635"
       height="1.0480013">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.36829105"
         id="feGaussianBlur4211-6-4" />
    </filter>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4260-5-2"
       x="-0.035885267"
       width="1.0717705"
       y="-0.036115468"
       height="1.0722309">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.3757032"
         id="feGaussianBlur4262-7-25" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4479"
       id="radialGradient4485-6-5"
       cx="243.34979"
       cy="799.60266"
       fx="243.34979"
       fy="799.60266"
       r="26.382999"
       gradientTransform="matrix(0.94262735,-0.96925492,1.4109273,1.372166,-1114.2197,-61.71694)"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter4507-13-6"
       x="-0.011999792"
       width="1.0239996"
       y="-0.012000208"
       height="1.0240004">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.26382543"
         id="feGaussianBlur4509-2-7" />
    </filter>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="3.2311604"
     inkscape:cx="130.15593"
     inkscape:cy="112.1381"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:window-width="1280"
     inkscape:window-height="972"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     showguides="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0" />
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Calque 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-26.160786,-625.08677)">
    <rect
       y="665.62579"
       x="57.765621"
       height="81.142677"
       width="217.85828"
       id="rect5502"
       style="font-style:normal;font-weight:normal;font-size:medium;line-height:125%;font-family:sans-serif;letter-spacing:0px;word-spacing:0px;opacity:1;fill:#ff0000;fill-opacity:1;stroke:#ffffff;stroke-width:0.83908439;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1" />
    <g
       transform="translate(-148.38464,-2.1702488)"
       id="g5868-1-0"
       inkscape:export-xdpi="99.888031"
       inkscape:export-ydpi="99.888031">
      <path
         inkscape:export-ydpi="99.888062"
         inkscape:export-xdpi="99.888062"
         id="path4296-5-6"
         d="m 216.28711,772.07227 0,55.02343 55.02344,0 0,-55.02343 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.25;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4406-3-61);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         inkscape:connector-curvature="0" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-9-7-6"
         d="m 245.4367,781.96308 a 18.414436,18.414436 0 0 0 -18.41601,18.41406 18.414436,18.414436 0 0 0 18.41601,18.41407 18.414436,18.414436 0 0 0 18.41407,-18.41407 18.414436,18.414436 0 0 0 -18.41407,-18.41406 z m -1.70312,2 a 14.545857,14.545857 0 0 1 14.54687,14.54492 14.545857,14.545857 0 0 1 -14.54687,14.54688 14.545857,14.545857 0 0 1 -14.54492,-14.54688 14.545857,14.545857 0 0 1 14.54492,-14.54492 z"
         style="opacity:1;fill:url(#radialGradient4171-0-4);fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4209-7-7)" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-6-4-7"
         d="m 243.65555,784.04016 a 14.545857,14.545857 0 0 0 -14.54493,14.54493 14.545857,14.545857 0 0 0 4.41993,10.42187 14.545857,14.545857 0 0 1 -3.95508,-9.95703 14.545857,14.545857 0 0 1 14.54492,-14.54492 14.545857,14.545857 0 0 1 10.11719,4.11328 14.545857,14.545857 0 0 0 -10.58203,-4.57813 z"
         style="opacity:0.7;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4260-5-2)" />
      <path
         sodipodi:nodetypes="ccccccccc"
         inkscape:connector-curvature="0"
         id="path4296-1-2-1"
         d="m 216.96679,773.22058 0,0.74972 0,52.01445 52.766,-52.76417 z m 1.50126,1.49943 49.76348,0 -49.76348,49.76531 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.7;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:url(#radialGradient4485-6-5);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.60997915;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4507-13-6);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate" />
    </g>
    <g
       id="g5868"
       inkscape:export-xdpi="99.888031"
       inkscape:export-ydpi="99.888031">
      <path
         inkscape:export-ydpi="99.888062"
         inkscape:export-xdpi="99.888062"
         id="path4296"
         d="m 216.28711,772.07227 0,55.02343 55.02344,0 0,-55.02343 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.25;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4406);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         inkscape:connector-curvature="0" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-9"
         d="m 245.4367,781.96308 a 18.414436,18.414436 0 0 0 -18.41601,18.41406 18.414436,18.414436 0 0 0 18.41601,18.41407 18.414436,18.414436 0 0 0 18.41407,-18.41407 18.414436,18.414436 0 0 0 -18.41407,-18.41406 z m -1.70312,2 a 14.545857,14.545857 0 0 1 14.54687,14.54492 14.545857,14.545857 0 0 1 -14.54687,14.54688 14.545857,14.545857 0 0 1 -14.54492,-14.54688 14.545857,14.545857 0 0 1 14.54492,-14.54492 z"
         style="opacity:1;fill:url(#radialGradient4171);fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4209)" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-6"
         d="m 243.65555,784.04016 a 14.545857,14.545857 0 0 0 -14.54493,14.54493 14.545857,14.545857 0 0 0 4.41993,10.42187 14.545857,14.545857 0 0 1 -3.95508,-9.95703 14.545857,14.545857 0 0 1 14.54492,-14.54492 14.545857,14.545857 0 0 1 10.11719,4.11328 14.545857,14.545857 0 0 0 -10.58203,-4.57813 z"
         style="opacity:0.7;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4260)" />
      <path
         sodipodi:nodetypes="ccccccccc"
         inkscape:connector-curvature="0"
         id="path4296-1"
         d="m 216.96679,773.22058 0,0.74972 0,52.01445 52.766,-52.76417 z m 1.50126,1.49943 49.76348,0 -49.76348,49.76531 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.7;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:url(#radialGradient4485);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.60997915;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4507);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate" />
    </g>
    <g
       id="g5332"
       transform="matrix(1.1226559,0,0,1.3295087,-29.131334,-264.53007)">
      <path
         id="path4898-7"
         d="m 245.24023,795.35547 c -0.32818,0 -0.62759,0.0839 -0.89648,0.25 -0.26889,0.16226 -0.51759,0.40997 -0.74414,0.74609 -0.35993,0.53702 -0.64229,1.21012 -0.84766,2.01758 -0.20326,0.80359 -0.30468,1.65051 -0.30468,2.54297 0,1.07404 0.16742,1.91293 0.50195,2.51562 0.17136,0.3068 0.37667,0.53291 0.61523,0.6836 -0.16749,-0.1426 -0.32194,-0.31551 -0.45117,-0.54688 -0.33453,-0.60269 -0.50195,-1.44158 -0.50195,-2.51562 0,-0.89246 0.10143,-1.73938 0.30469,-2.54297 0.20537,-0.80746 0.48772,-1.48056 0.84765,-2.01758 0.22655,-0.33612 0.47525,-0.58383 0.74414,-0.74609 0.26889,-0.1661 0.56831,-0.25 0.89649,-0.25 0.29641,0 0.5786,0.0699 0.84961,0.20898 0.11658,0.0577 0.22984,0.13395 0.34179,0.2168 -0.16403,-0.14275 -0.33146,-0.26718 -0.50586,-0.35352 -0.27101,-0.13908 -0.5532,-0.20898 -0.84961,-0.20898 z m 4.94336,0 c -0.39805,0 -0.7615,0.14783 -1.09179,0.44531 -0.33029,0.29363 -0.62955,0.74101 -0.89844,1.33984 -0.22866,0.50999 -0.40976,1.09123 -0.54102,1.74415 -0.12916,0.65293 -0.19336,1.29209 -0.19336,1.91796 0,1.09336 0.16565,1.95425 0.49805,2.58399 0.17479,0.32903 0.38458,0.56731 0.62695,0.72461 -0.17215,-0.15097 -0.32987,-0.33749 -0.46289,-0.58789 -0.3324,-0.62974 -0.49804,-1.49063 -0.49804,-2.58399 0,-0.62587 0.0642,-1.26504 0.19336,-1.91797 0.13126,-0.65292 0.31235,-1.23416 0.54101,-1.74414 0.26889,-0.59883 0.56815,-1.04621 0.89844,-1.33984 0.33029,-0.29748 0.69375,-0.44531 1.0918,-0.44531 0.27698,0 0.52484,0.0759 0.74609,0.21875 -0.26064,-0.22728 -0.56071,-0.35547 -0.91016,-0.35547 z m -14.67187,0.15625 -0.91797,8.65234 0.17773,0 0.9043,-8.51562 0.4668,0 0.0137,-0.13672 -0.64453,0 z m 3.62695,0 -0.92383,8.65234 0.17774,0 0.91015,-8.51562 2.81836,0 0.0156,-0.13672 -2.99805,0 z m 11.64453,1.0664 c 0.1016,0.10262 0.19504,0.22559 0.27344,0.38868 0.21172,0.43657 0.31836,1.05378 0.31836,1.85351 0,0.45589 -0.0338,0.91525 -0.10156,1.375 -0.0677,0.45589 -0.16577,0.89136 -0.29492,1.3086 -0.19267,0.62203 -0.4261,1.09139 -0.69922,1.4082 -0.27101,0.31293 -0.58051,0.46875 -0.92774,0.46875 -0.17133,0 -0.32221,-0.0505 -0.45898,-0.13672 0.17429,0.17561 0.37854,0.27344 0.62304,0.27344 0.34723,0 0.65673,-0.15582 0.92774,-0.46875 0.27312,-0.31681 0.50655,-0.78618 0.69922,-1.40821 0.12915,-0.41723 0.22722,-0.8527 0.29492,-1.30859 0.0678,-0.45975 0.10156,-0.91911 0.10156,-1.375 0,-0.79973 -0.10664,-1.41694 -0.31836,-1.85351 -0.11844,-0.24641 -0.26587,-0.41698 -0.4375,-0.5254 z m -4.60156,0.125 c 0.20848,0.18276 0.39913,0.41306 0.56055,0.72071 l 0.01,-0.0996 c -0.15558,-0.24079 -0.32281,-0.44931 -0.51562,-0.5918 -0.0176,-0.0133 -0.0369,-0.017 -0.0547,-0.0293 z m -6.60547,2.35547 -0.0137,0.13672 2.08594,0 0.0156,-0.13672 -2.08789,0 z m 5.42188,0.58594 -0.0977,0.96094 0.17773,0 0.084,-0.82422 1.50391,0 0.0156,-0.13672 -1.68359,0 z m 0.93945,1.09766 -0.23047,2.1289 c -0.17573,0.1661 -0.36852,0.29395 -0.57812,0.38282 -0.20961,0.085 -0.42791,0.12695 -0.65235,0.12695 -0.25615,0 -0.47288,-0.0733 -0.65625,-0.20508 0.21121,0.22788 0.48368,0.3418 0.82031,0.3418 0.22444,0 0.44274,-0.0419 0.65235,-0.12696 0.2096,-0.0889 0.40239,-0.21671 0.57812,-0.38281 l 0.2461,-2.26562 -0.17969,0 z m -6.79688,2.4375 -0.0137,0.13672 2.23633,0 0.0137,-0.13672 -2.23633,0 z m -3.61914,0.0117 -0.0137,0.13671 2.12891,0 0.0156,-0.13671 -2.13086,0 z"
         style="font-style:italic;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:125%;font-family:sans-serif;-inkscape-font-specification:'sans-serif Italic';letter-spacing:0px;word-spacing:0px;opacity:0.5;fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-width:0.2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1;filter:url(#filter5268)"
         inkscape:connector-curvature="0" />
      <path
         id="path5096"
         d="m 236.28711,795.95312 -0.7793,7.375 0.24805,0 0.7793,-7.375 -0.24805,0 z m 5.98047,0 -0.0723,0.67969 -2.35937,0 -0.27344,2.5625 0.24805,0 0.24023,-2.25781 2.35938,0 0.10546,-0.98438 -0.24804,0 z m 9.36523,0.34571 c 0.0286,0.0465 0.0609,0.0823 0.0879,0.13281 0.33453,0.62201 0.50196,1.47093 0.50196,2.54883 0,0.66452 -0.0644,1.31765 -0.19141,1.95898 -0.12704,0.64133 -0.30974,1.22258 -0.54687,1.74414 -0.27101,0.6027 -0.57027,1.05208 -0.89844,1.34571 -0.32606,0.29361 -0.68969,0.43945 -1.08985,0.43945 -0.42003,0 -0.77338,-0.17011 -1.06445,-0.49805 0.32517,0.52581 0.74732,0.80274 1.2793,0.80274 0.40016,0 0.76378,-0.14585 1.08984,-0.43946 0.32817,-0.29363 0.62743,-0.743 0.89844,-1.3457 0.23713,-0.52156 0.41983,-1.10281 0.54687,-1.74414 0.12703,-0.64133 0.19141,-1.29446 0.19141,-1.95898 0,-1.0779 -0.16742,-1.92682 -0.50195,-2.54883 -0.0914,-0.17092 -0.19296,-0.31319 -0.30274,-0.4375 z m -4.60742,0.10937 -0.11719,1.12891 c 0.073,0.10398 0.14757,0.20362 0.21289,0.32812 l 0.12891,-1.24609 c -0.0731,-0.0809 -0.14933,-0.14225 -0.22461,-0.21094 z m -1.60937,0.0352 c -0.33665,0 -0.63249,0.11783 -0.88868,0.35352 -0.25618,0.2318 -0.48636,0.59137 -0.6875,1.08203 -0.17149,0.41725 -0.30675,0.91059 -0.40625,1.47851 -0.0974,0.56406 -0.14453,1.13932 -0.14453,1.72656 0,0.80747 0.1133,1.41666 0.33985,1.82618 0.059,0.10566 0.12721,0.1954 0.20117,0.27343 -0.21635,-0.40908 -0.32617,-1.00584 -0.32617,-1.79492 0,-0.58724 0.0471,-1.1625 0.14453,-1.72656 0.0995,-0.56792 0.23476,-1.06127 0.40625,-1.47852 0.20114,-0.49066 0.43132,-0.85023 0.6875,-1.08203 0.25619,-0.23568 0.55202,-0.35351 0.88867,-0.35351 0.29006,0 0.56412,0.0959 0.82031,0.28906 0.11151,0.0824 0.21347,0.18948 0.3125,0.30469 -0.15847,-0.24921 -0.32978,-0.46339 -0.52734,-0.60938 -0.25619,-0.19316 -0.53025,-0.28906 -0.82031,-0.28906 z m 4.91406,0 c -0.35147,0 -0.66275,0.15581 -0.93164,0.46875 -0.26889,0.30907 -0.50216,0.78245 -0.70117,1.41992 -0.12704,0.40566 -0.22527,0.83708 -0.29297,1.29297 -0.0657,0.45589 -0.0977,0.91529 -0.0977,1.37891 0,0.8036 0.10485,1.42281 0.31445,1.85937 0.0711,0.14656 0.15561,0.26015 0.2461,0.35742 -0.01,-0.019 -0.0216,-0.0329 -0.0312,-0.0527 -0.2096,-0.43656 -0.31446,-1.05578 -0.31446,-1.85938 0,-0.46362 0.032,-0.92301 0.0977,-1.3789 0.0677,-0.45589 0.16593,-0.88731 0.29297,-1.29297 0.19901,-0.63747 0.43228,-1.11085 0.70117,-1.41992 0.26889,-0.31294 0.58017,-0.46875 0.93164,-0.46875 0.25109,0 0.46174,0.10403 0.63867,0.29297 -0.20888,-0.39422 -0.49094,-0.59766 -0.85351,-0.59766 z m -8.53516,3.05664 -0.0723,0.67969 -2.26368,0 -0.33203,3.13672 0.24805,0 0.29883,-2.83203 2.26367,0 0.10547,-0.98438 -0.24805,0 z m 5.01758,0.58594 -0.37695,3.50195 c -0.2816,0.28204 -0.58947,0.49976 -0.92188,0.6543 -0.33241,0.15066 -0.66531,0.22656 -1.00195,0.22656 -0.4443,0 -0.81665,-0.16771 -1.11719,-0.50391 0.3311,0.53237 0.77289,0.8086 1.33203,0.8086 0.33664,0 0.66955,-0.0759 1.00196,-0.22656 0.33241,-0.15454 0.64027,-0.37226 0.92187,-0.6543 l 0.41016,-3.80664 -0.24805,0 z m -1.50195,0.65625 -0.0312,0.30469 0.80468,0 0.0332,-0.30469 -0.80664,0 z m -3.80078,2.8789 -0.0723,0.67969 -2.81055,0 -0.0332,0.30469 3.05859,0 0.10352,-0.98438 -0.24609,0 z m -3.72657,0.0117 -0.0723,0.66797 -2.70508,0 -0.0332,0.30469 2.95312,0 0.10547,-0.97266 -0.24805,0 z"
         style="font-style:italic;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:125%;font-family:sans-serif;-inkscape-font-specification:'sans-serif Italic';letter-spacing:0px;word-spacing:0px;opacity:0.5;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;filter:url(#filter5328)"
         inkscape:connector-curvature="0" />
    </g>
    <path
       sodipodi:nodetypes="cssccsscccsccccscccssccsccccccccccscccscscccscccccccscccccccscccscccscccccscsccccccccccccccc"
       id="path5423"
       d="m 104.89822,791.46407 c 0.0801,0.0663 0.16145,0.13186 0.23242,0.21289 0.38967,0.4449 0.58594,1.04084 0.58594,1.78906 0,0.73309 -0.15579,1.36289 -0.46484,1.88867 -0.30571,0.5258 -0.72869,0.88602 -1.26954,1.07813 0.42327,0.18202 0.73344,0.46538 0.93164,0.84961 0.19821,0.38424 0.29883,0.89147 0.29883,1.52344 0,0.60668 -0.0981,1.17619 -0.29297,1.70703 -0.19147,0.53084 -0.46922,0.98685 -0.83203,1.37109 -0.27547,0.29324 -0.60336,0.51079 -0.98633,0.65234 -0.38296,0.14661 -0.82564,0.22071 -1.32617,0.22071 -0.31577,0 -0.63453,-0.0485 -0.95703,-0.14453 -0.15662,-0.0442 -0.31565,-0.0994 -0.47461,-0.16602 l -0.006,0.0508 c 0.33257,0.18706 0.65992,0.32502 0.98243,0.41602 0.32249,0.0961 0.64125,0.14453 0.95703,0.14453 0.50053,0 0.94321,-0.0741 1.32617,-0.2207 0.38296,-0.14156 0.71086,-0.35911 0.98633,-0.65235 0.3628,-0.38423 0.64055,-0.84025 0.83203,-1.37109 0.19483,-0.53085 0.29297,-1.10035 0.29297,-1.70703 0,-0.63197 -0.10063,-1.13921 -0.29883,-1.52344 -0.1982,-0.38424 -0.50837,-0.6676 -0.93164,-0.84961 0.54084,-0.19212 0.96383,-0.55233 1.26953,-1.07812 0.30905,-0.52579 0.46484,-1.15559 0.46484,-1.88868 0,-0.74823 -0.19626,-1.34415 -0.58593,-1.78906 -0.20565,-0.23478 -0.45209,-0.40262 -0.73438,-0.51367 z m -8.10938,0.0508 -1.25195,9.46082 0.54102,0 1.25195,-9.46082 z m -4.58984,0.0137 c 0.24292,0.18913 0.47005,0.41261 0.66211,0.70899 0.53077,0.81397 0.79688,1.92735 0.79688,3.33789 0,0.86958 -0.10118,1.72326 -0.30274,2.5625 -0.20155,0.83925 -0.49095,1.60068 -0.86719,2.2832 -0.42999,0.7887 -0.90509,1.37553 -1.42578,1.75977 -0.51734,0.38424 -1.09361,0.57617 -1.72851,0.57617 -0.37359,0 -0.70636,-0.0859 -1.01563,-0.22071 0.42915,0.33503 0.92861,0.52149 1.51758,0.52149 0.63491,0 1.21118,-0.19194 1.72852,-0.57617 0.52069,-0.38424 0.99579,-0.97108 1.42578,-1.75977 0.37624,-0.68252 0.66563,-1.44395 0.86718,-2.2832 0.20156,-0.83924 0.30274,-1.69292 0.30274,-2.5625 0,-1.41055 -0.26611,-2.52392 -0.79688,-3.33789 -0.31626,-0.48804 -0.70972,-0.81255 -1.16406,-1.00977 z m 11.13281,0.70899 c -0.32249,0 -0.6529,0.054 -0.99218,0.16015 -0.15641,0.0471 -0.31657,0.10758 -0.47852,0.17774 l -0.0664,0.58593 c 0.36281,-0.20727 0.71095,-0.36177 1.04688,-0.46289 0.33929,-0.10617 0.6697,-0.16015 0.99219,-0.16015 0.17244,0 0.32535,0.0273 0.46679,0.0703 -0.23358,-0.24575 -0.55628,-0.37109 -0.96875,-0.37109 z m -12.67773,0.0156 c -0.55764,0 -1.04993,0.20572 -1.47656,0.61524 -0.42663,0.40447 -0.79752,1.02323 -1.11328,1.85742 -0.20156,0.53085 -0.35541,1.09483 -0.4629,1.6914 -0.10416,0.59658 -0.15624,1.19801 -0.15624,1.80469 0,1.05159 0.16547,1.8623 0.49804,2.4336 0.22791,0.38759 0.52393,0.63304 0.87891,0.75781 -0.13888,-0.12558 -0.26747,-0.27084 -0.37695,-0.45703 -0.33258,-0.5713 -0.49805,-1.38201 -0.49805,-2.4336 0,-0.60668 0.0521,-1.20811 0.15625,-1.80468 0.10749,-0.59657 0.26133,-1.16056 0.46289,-1.69141 0.31577,-0.83419 0.68665,-1.45296 1.11328,-1.85742 0.42663,-0.40951 0.91892,-0.61524 1.47656,-0.61524 0.19185,0 0.36453,0.0402 0.5293,0.0977 -0.28484,-0.25761 -0.62491,-0.39844 -1.03125,-0.39844 z m 11.68164,4.86133 -0.0371,0.30078 0.875,0 c 0.21444,0 0.40312,0.0322 0.57422,0.0879 -0.25937,-0.25663 -0.61645,-0.38867 -1.07617,-0.38867 l -0.33594,0 z m -1.8125,3.93359 -0.008,0.0547 c 0.0942,0.0577 0.18876,0.10037 0.2832,0.14649 -0.0921,-0.0653 -0.18369,-0.12483 -0.27539,-0.20118 z m -1.36133,0.51563 -0.12695,0.97265 -4.14649,0 -0.0391,0.30079 4.6875,0 0.16602,-1.27344 -0.54102,0 z"
       style="font-style:italic;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:125%;font-family:sans-serif;-inkscape-font-specification:'sans-serif Italic';letter-spacing:0px;word-spacing:0px;opacity:0.5;fill:#000000;fill-opacity:1;stroke:none;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;filter:url(#filter5602)"
       inkscape:connector-curvature="0" />
    <path
       sodipodi:nodetypes="scccsccscccsccsscccccccsccccccccccccccscsccccscsccccscccsccscccsccccscsccccccscsccccccccc"
       id="path5423-3-3"
       d="m 90.46658,790.68087 c -0.63154,0 -1.21032,0.19469 -1.73438,0.58398 -0.52404,0.38423 -0.99914,0.96831 -1.42578,1.75195 -0.3628,0.66735 -0.6472,1.42879 -0.85547,2.28321 -0.20491,0.85441 -0.30859,1.69074 -0.30859,2.50976 0,1.43077 0.26361,2.55874 0.79102,3.38282 0.0431,0.0669 0.095,0.11431 0.14062,0.17578 -0.46959,-0.80995 -0.71289,-1.88038 -0.71289,-3.23047 0,-0.81903 0.10368,-1.65535 0.30859,-2.50977 0.20827,-0.85441 0.49267,-1.61586 0.85547,-2.2832 0.42664,-0.78364 0.90174,-1.36773 1.42578,-1.75195 0.52406,-0.3893 1.10284,-0.58399 1.73438,-0.58399 0.85021,0 1.52538,0.36567 2.04297,1.06445 -0.0307,-0.0524 -0.0532,-0.11389 -0.0859,-0.16406 -0.53075,-0.81902 -1.25533,-1.22851 -2.17578,-1.22851 z m 12.86328,0 c -0.33593,0 -0.67798,0.0422 -1.02734,0.12304 -0.34601,0.0809 -0.70469,0.20151 -1.07422,0.36328 l -0.1543,1.36524 c 0.0924,-0.0528 0.18092,-0.0927 0.27149,-0.13867 l 0.10156,-0.89844 c 0.36952,-0.16178 0.72821,-0.28244 1.07422,-0.36328 0.34936,-0.0808 0.69141,-0.12305 1.02734,-0.12305 0.66514,0 1.19234,0.22307 1.58203,0.66797 0.0225,0.0257 0.0374,0.0572 0.0586,0.084 -0.0787,-0.14832 -0.16821,-0.2875 -0.27735,-0.41211 -0.38969,-0.4449 -0.91689,-0.66796 -1.58203,-0.66796 z m -7.74414,0.20507 -1.45703,11.32227 0.26172,0 1.41406,-10.99414 0.76172,0 0.043,-0.32813 -1.02344,0 z m 8.7168,1.72461 c 0.10148,0.2186 0.15625,0.48024 0.15625,0.79297 0,0.65724 -0.15993,1.17826 -0.48243,1.5625 -0.31912,0.38423 -0.75876,0.57617 -1.3164,0.57617 l -0.92774,0 -0.15429,1.24414 0.25976,0 0.11328,-0.91601 0.92774,0 c 0.55764,0 0.99728,-0.19194 1.31641,-0.57617 0.32249,-0.38424 0.48242,-0.90527 0.48242,-1.5625 0,-0.47523 -0.11643,-0.84177 -0.35157,-1.09961 -0.007,-0.008 -0.0161,-0.0137 -0.0234,-0.0215 z m -12.41407,0.25977 c 0.30812,0.5663 0.4668,1.34344 0.4668,2.3457 0,0.59657 -0.0527,1.1972 -0.16015,1.79883 -0.1075,0.59657 -0.26384,1.16688 -0.46876,1.71289 -0.3057,0.81397 -0.67602,1.4292 -1.10937,1.84375 -0.42998,0.40951 -0.91977,0.61328 -1.4707,0.61328 -0.51257,0 -0.92303,-0.2136 -1.24219,-0.61523 0.0144,0.0269 0.024,0.0601 0.0391,0.0859 0.33593,0.57128 0.81048,0.85742 1.42188,0.85742 0.55093,0 1.04072,-0.20377 1.4707,-0.61328 0.43335,-0.41456 0.80367,-1.02978 1.10938,-1.84375 0.20491,-0.54602 0.36125,-1.11632 0.46874,-1.71289 0.1075,-0.60163 0.16016,-1.20226 0.16016,-1.79883 0,-1.04653 -0.16798,-1.8545 -0.5039,-2.42578 -0.055,-0.0943 -0.11937,-0.16912 -0.18165,-0.24805 z m 11.89844,4.66797 c 0.11882,0.24454 0.1836,0.54026 0.1836,0.89453 0,0.81901 -0.20595,1.48627 -0.61915,2.00195 -0.40983,0.51063 -0.94564,0.76563 -1.60742,0.76563 -0.33592,0 -0.66578,-0.0631 -0.98828,-0.18946 -0.31913,-0.13143 -0.63735,-0.32888 -0.95312,-0.59179 l -0.18555,1.44922 c 0.0828,0.0465 0.16399,0.0806 0.24609,0.12109 l 0.15821,-1.24219 c 0.31577,0.2629 0.63399,0.46036 0.95312,0.5918 0.3225,0.12639 0.65235,0.18945 0.98828,0.18945 0.66178,0 1.19759,-0.255 1.60743,-0.76562 0.41319,-0.51568 0.61914,-1.18294 0.61914,-2.00196 0,-0.52579 -0.13055,-0.93171 -0.39258,-1.21484 -0.003,-0.003 -0.007,-0.005 -0.01,-0.008 z m -8.20703,3.39648 -0.043,0.32813 3.40235,0 0.043,-0.32813 -3.40234,0 z"
       style="font-style:italic;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:12.6600647px;line-height:125%;font-family:sans-serif;-inkscape-font-specification:'sans-serif Italic';letter-spacing:0px;word-spacing:0px;opacity:0.4;fill:#ffffff;fill-opacity:1;stroke:#ffffff;stroke-width:0.2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1;filter:url(#filter5676)"
       inkscape:connector-curvature="0" />
    <g
       transform="translate(-71.352998,-1.0760495)"
       id="g5868-1"
       inkscape:export-xdpi="99.888031"
       inkscape:export-ydpi="99.888031">
      <path
         inkscape:export-ydpi="99.888062"
         inkscape:export-xdpi="99.888062"
         id="path4296-5"
         d="m 216.28711,772.07227 0,55.02343 55.02344,0 0,-55.02343 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.25;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:none;fill-opacity:1;fill-rule:nonzero;stroke:#000000;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4406-3);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         inkscape:connector-curvature="0" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-9-7"
         d="m 245.4367,781.96308 a 18.414436,18.414436 0 0 0 -18.41601,18.41406 18.414436,18.414436 0 0 0 18.41601,18.41407 18.414436,18.414436 0 0 0 18.41407,-18.41407 18.414436,18.414436 0 0 0 -18.41407,-18.41406 z m -1.70312,2 a 14.545857,14.545857 0 0 1 14.54687,14.54492 14.545857,14.545857 0 0 1 -14.54687,14.54688 14.545857,14.545857 0 0 1 -14.54492,-14.54688 14.545857,14.545857 0 0 1 14.54492,-14.54492 z"
         style="opacity:1;fill:url(#radialGradient4171-0);fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4209-7)" />
      <path
         inkscape:connector-curvature="0"
         id="path4148-6-4"
         d="m 243.65555,784.04016 a 14.545857,14.545857 0 0 0 -14.54493,14.54493 14.545857,14.545857 0 0 0 4.41993,10.42187 14.545857,14.545857 0 0 1 -3.95508,-9.95703 14.545857,14.545857 0 0 1 14.54492,-14.54492 14.545857,14.545857 0 0 1 10.11719,4.11328 14.545857,14.545857 0 0 0 -10.58203,-4.57813 z"
         style="opacity:0.7;fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:3;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4260-5)" />
      <path
         sodipodi:nodetypes="ccccccccc"
         inkscape:connector-curvature="0"
         id="path4296-1-2"
         d="m 216.96679,773.22058 0,0.74972 0,52.01445 52.766,-52.76417 z m 1.50126,1.49943 49.76348,0 -49.76348,49.76531 z"
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:0.7;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:url(#radialGradient4485-6);fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.60997915;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;filter:url(#filter4507-13);color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate" />
    </g>
  </g>
</svg>
