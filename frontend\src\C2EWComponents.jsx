/**
 * Composants pour l'application C2-EW
 */

import React, { useEffect, useRef } from 'react';
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import LineString from 'ol/geom/LineString';
import Polygon from 'ol/geom/Polygon';
import { fromLonLat, toLonLat } from 'ol/proj';
import { Draw } from 'ol/interaction';
import { getLength, getArea } from 'ol/sphere';

// Composant Vue Carte avec fonctionnalité hors ligne
export const MapView = ({ 
    mapRef, 
    map, 
    setMap, 
    equipment, 
    selectedEquipment, 
    setSelectedEquipment,
    activeTool,
    setActiveTool,
    measurements,
    setMeasurements,
    visibilityAnalysis,
    setVisibilityAnalysis,
    getEquipmentStyle,
    isOfflineMode 
}) => {
    // Initialisation de la carte
    useEffect(() => {
        if (!mapRef.current) return;

        // Source de tuiles - avec support hors ligne
        let tileSource;
        if (isOfflineMode) {
            // Mode hors ligne - utilise des tuiles en cache ou une source locale
            tileSource = new XYZ({
                url: '/offline-tiles/{z}/{x}/{y}.png', // Tuiles pré-téléchargées
                maxZoom: 18
            });
        } else {
            // Mode en ligne - OpenStreetMap
            tileSource = new OSM();
        }

        const osmLayer = new TileLayer({
            source: tileSource
        });

        const equipmentSource = new VectorSource();
        const equipmentLayer = new VectorLayer({
            source: equipmentSource,
            style: (feature) => getEquipmentStyle(feature.get('equipment'))
        });

        const measurementSource = new VectorSource();
        const measurementLayer = new VectorLayer({
            source: measurementSource,
            style: {
                fill: { color: 'rgba(33, 150, 243, 0.2)' },
                stroke: { color: '#2196F3', width: 2 }
            }
        });

        // Création de la carte
        const newMap = new Map({
            target: mapRef.current,
            layers: [osmLayer, measurementLayer, equipmentLayer],
            view: new View({
                center: fromLonLat([-7.6, 33.6]), // Centre sur Casablanca
                zoom: 8
            })
        });

        // Ajouter les équipements
        equipment.forEach(eq => {
            const feature = new Feature({
                geometry: new Point(fromLonLat([eq.longitude, eq.latitude])),
                equipment: eq
            });
            equipmentSource.addFeature(feature);
        });

        // Gestionnaire de clic
        newMap.on('click', (event) => {
            const features = newMap.getFeaturesAtPixel(event.pixel);
            if (features.length > 0) {
                const equipment = features[0].get('equipment');
                if (equipment) {
                    setSelectedEquipment(equipment);
                }
            }
        });

        setMap(newMap);

        return () => {
            newMap.setTarget(null);
        };
    }, [equipment, isOfflineMode]);

    // Outils de mesure
    const startMeasurement = (type) => {
        if (!map) return;

        // Nettoyer les interactions précédentes
        map.getInteractions().forEach(interaction => {
            if (interaction instanceof Draw) {
                map.removeInteraction(interaction);
            }
        });

        const source = map.getLayers().getArray()[1].getSource();
        
        let geometryType = type === 'distance' ? 'LineString' : 'Polygon';

        const draw = new Draw({
            source: source,
            type: geometryType
        });

        draw.on('drawend', (event) => {
            const geometry = event.feature.getGeometry();
            let measurement;

            if (type === 'distance') {
                const length = getLength(geometry);
                measurement = {
                    type: 'distance',
                    value: length,
                    text: length > 1000 ? `${(length / 1000).toFixed(2)} km` : `${length.toFixed(2)} m`
                };
            } else if (type === 'area') {
                const area = getArea(geometry);
                measurement = {
                    type: 'area',
                    value: area,
                    text: area > 1000000 ? `${(area / 1000000).toFixed(2)} km²` : `${area.toFixed(2)} m²`
                };
            }

            setMeasurements(prev => [...prev, measurement]);
            map.removeInteraction(draw);
            setActiveTool(null);
        });

        map.addInteraction(draw);
        setActiveTool(type);
    };

    // Analyse de visibilité
    const startVisibilityAnalysis = () => {
        if (!map) return;

        const draw = new Draw({
            source: new VectorSource(),
            type: 'Point'
        });

        draw.on('drawend', (event) => {
            const coordinate = event.feature.getGeometry().getCoordinates();
            const lonLat = toLonLat(coordinate);
            
            const analysis = {
                observer: lonLat,
                visibleArea: Math.random() * 100,
                maxDistance: 5000 + Math.random() * 5000,
                timestamp: new Date().toLocaleString()
            };

            setVisibilityAnalysis(analysis);
            map.removeInteraction(draw);
        });

        map.addInteraction(draw);
    };

    return (
        <div style={{ flex: 1, display: 'flex' }}>
            {/* Sidebar gauche - Outils SIG */}
            <div style={{
                width: '320px',
                background: '#f8fafc',
                borderRight: '1px solid #e5e7eb',
                padding: '1.5rem',
                overflowY: 'auto'
            }}>
                <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1.5rem', color: '#1f2937' }}>
                    🛠️ Outils SIG Avancés
                </h3>

                {/* Mode hors ligne */}
                {isOfflineMode && (
                    <div style={{
                        background: '#fef3c7',
                        border: '1px solid #f59e0b',
                        borderRadius: '8px',
                        padding: '1rem',
                        marginBottom: '1.5rem'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                            <span>📱</span>
                            <strong style={{ color: '#92400e' }}>Mode Hors Ligne</strong>
                        </div>
                        <p style={{ fontSize: '0.875rem', color: '#92400e', margin: 0 }}>
                            Utilisation des cartes en cache. Fonctionnalités SIG disponibles.
                        </p>
                    </div>
                )}

                {/* Outils de mesure */}
                <div style={{ marginBottom: '2rem' }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem', color: '#374151' }}>
                        📏 Outils de Mesure
                    </h4>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                        <button
                            onClick={() => startMeasurement('distance')}
                            style={{
                                padding: '0.75rem',
                                background: activeTool === 'distance' ? '#3b82f6' : '#e5e7eb',
                                color: activeTool === 'distance' ? 'white' : '#374151',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '0.9rem',
                                fontWeight: '500',
                                transition: 'all 0.2s'
                            }}
                        >
                            📐 Mesurer Distance
                        </button>
                        <button
                            onClick={() => startMeasurement('area')}
                            style={{
                                padding: '0.75rem',
                                background: activeTool === 'area' ? '#3b82f6' : '#e5e7eb',
                                color: activeTool === 'area' ? 'white' : '#374151',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '0.9rem',
                                fontWeight: '500',
                                transition: 'all 0.2s'
                            }}
                        >
                            📐 Mesurer Surface
                        </button>
                    </div>
                </div>

                {/* Analyse de visibilité */}
                <div style={{ marginBottom: '2rem' }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem', color: '#374151' }}>
                        👁️ Analyse Spatiale
                    </h4>
                    <button
                        onClick={startVisibilityAnalysis}
                        style={{
                            width: '100%',
                            padding: '0.75rem',
                            background: '#10b981',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            transition: 'all 0.2s'
                        }}
                    >
                        🔍 Analyse de Visibilité
                    </button>
                </div>

                {/* Résultats des mesures */}
                {measurements.length > 0 && (
                    <div style={{ marginBottom: '2rem' }}>
                        <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem', color: '#374151' }}>
                            📊 Résultats de Mesure
                        </h4>
                        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                            {measurements.map((measurement, index) => (
                                <div key={index} style={{
                                    padding: '0.75rem',
                                    background: 'white',
                                    border: '1px solid #e5e7eb',
                                    borderRadius: '6px',
                                    marginBottom: '0.5rem',
                                    fontSize: '0.875rem'
                                }}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                        <strong>{measurement.type === 'distance' ? '📏' : '📐'}</strong>
                                        <span>{measurement.text}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button
                            onClick={() => setMeasurements([])}
                            style={{
                                width: '100%',
                                padding: '0.5rem',
                                background: '#ef4444',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '0.8rem',
                                marginTop: '0.5rem'
                            }}
                        >
                            🗑️ Effacer Mesures
                        </button>
                    </div>
                )}

                {/* Analyse de visibilité */}
                {visibilityAnalysis && (
                    <div style={{ marginBottom: '2rem' }}>
                        <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem', color: '#374151' }}>
                            👁️ Résultat Visibilité
                        </h4>
                        <div style={{
                            padding: '1rem',
                            background: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '6px',
                            fontSize: '0.875rem'
                        }}>
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Zone visible:</strong> {visibilityAnalysis.visibleArea.toFixed(1)}%
                            </div>
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Distance max:</strong> {visibilityAnalysis.maxDistance.toFixed(0)}m
                            </div>
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Position:</strong> {visibilityAnalysis.observer[1].toFixed(4)}°N, {visibilityAnalysis.observer[0].toFixed(4)}°E
                            </div>
                            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                                <strong>Calculé:</strong> {visibilityAnalysis.timestamp}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Carte principale */}
            <div style={{ flex: 1, position: 'relative' }}>
                <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
                
                {/* Légende des équipements */}
                <div style={{
                    position: 'absolute',
                    top: '1rem',
                    right: '1rem',
                    background: 'white',
                    padding: '1.5rem',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    maxWidth: '280px',
                    border: '1px solid #e5e7eb'
                }}>
                    <h3 style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '1rem', color: '#1f2937' }}>
                        🛡️ Équipements C2-EW
                    </h3>
                    <div style={{ fontSize: '0.875rem' }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#2196F3', marginRight: '0.75rem' }}></div>
                            <span>📡 COMINT - Interception</span>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#FF9800', marginRight: '0.75rem' }}></div>
                            <span>📊 ELINT - Analyse</span>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#F44336', marginRight: '0.75rem' }}></div>
                            <span>🛡️ Anti-Drone</span>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#9C27B0', marginRight: '0.75rem' }}></div>
                            <span>📵 Brouilleur</span>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#4CAF50', marginRight: '0.75rem' }}></div>
                            <span>🔍 Capteur</span>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#00BCD4', marginRight: '0.75rem' }}></div>
                            <span>📡 Radar</span>
                        </div>
                    </div>
                    <div style={{ marginTop: '1rem', paddingTop: '1rem', borderTop: '1px solid #e5e7eb', fontSize: '0.75rem', color: '#6b7280' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span>Total: {equipment.length}</span>
                            <span>Actifs: {equipment.filter(e => e.status === 'operational').length}</span>
                        </div>
                    </div>
                </div>

                {/* Indicateur de mode */}
                <div style={{
                    position: 'absolute',
                    bottom: '1rem',
                    left: '1rem',
                    background: isOfflineMode ? '#fef3c7' : '#dcfce7',
                    border: `1px solid ${isOfflineMode ? '#f59e0b' : '#10b981'}`,
                    color: isOfflineMode ? '#92400e' : '#166534',
                    padding: '0.5rem 1rem',
                    borderRadius: '6px',
                    fontSize: '0.875rem',
                    fontWeight: '500'
                }}>
                    {isOfflineMode ? '📱 Mode Hors Ligne' : '🌐 Mode En Ligne'}
                </div>
            </div>

            {/* Sidebar droite - Détails équipement */}
            {selectedEquipment && (
                <div style={{
                    width: '350px',
                    background: '#f8fafc',
                    borderLeft: '1px solid #e5e7eb',
                    padding: '1.5rem',
                    overflowY: 'auto'
                }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                            📋 Détails Équipement
                        </h3>
                        <button
                            onClick={() => setSelectedEquipment(null)}
                            style={{
                                background: 'none',
                                border: 'none',
                                fontSize: '1.5rem',
                                cursor: 'pointer',
                                color: '#6b7280',
                                padding: '0.25rem'
                            }}
                        >
                            ×
                        </button>
                    </div>

                    <div style={{ background: 'white', padding: '1.5rem', borderRadius: '8px', border: '1px solid #e5e7eb' }}>
                        <h4 style={{ fontSize: '1.125rem', fontWeight: 'bold', marginBottom: '1rem', color: '#1f2937' }}>
                            {selectedEquipment.name}
                        </h4>
                        
                        <div style={{ marginBottom: '1rem' }}>
                            <strong>Type:</strong> 
                            <span style={{ marginLeft: '0.5rem', textTransform: 'uppercase', fontWeight: '500' }}>
                                {selectedEquipment.type}
                            </span>
                        </div>
                        
                        <div style={{ marginBottom: '1rem' }}>
                            <strong>Statut:</strong> 
                            <span style={{
                                marginLeft: '0.5rem',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '12px',
                                fontSize: '0.75rem',
                                fontWeight: '600',
                                background: selectedEquipment.status === 'operational' ? '#dcfce7' : '#fef3c7',
                                color: selectedEquipment.status === 'operational' ? '#166534' : '#92400e'
                            }}>
                                {selectedEquipment.status === 'operational' ? '✅ Opérationnel' : '⚠️ Maintenance'}
                            </span>
                        </div>
                        
                        <div style={{ marginBottom: '1rem' }}>
                            <strong>Position:</strong>
                            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.25rem' }}>
                                📍 {selectedEquipment.latitude.toFixed(4)}°N, {selectedEquipment.longitude.toFixed(4)}°E
                            </div>
                        </div>

                        <div style={{ marginBottom: '1rem' }}>
                            <strong>Dernière mise à jour:</strong>
                            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.25rem' }}>
                                🕐 {selectedEquipment.lastUpdate}
                            </div>
                        </div>

                        <div style={{ marginTop: '1.5rem' }}>
                            <strong>Propriétés Techniques:</strong>
                            <div style={{ marginTop: '0.75rem', fontSize: '0.875rem' }}>
                                {Object.entries(selectedEquipment.properties).map(([key, value]) => (
                                    <div key={key} style={{ 
                                        marginBottom: '0.5rem',
                                        padding: '0.5rem',
                                        background: '#f8fafc',
                                        borderRadius: '4px',
                                        border: '1px solid #e5e7eb'
                                    }}>
                                        <strong style={{ textTransform: 'capitalize' }}>
                                            {key.replace('_', ' ')}:
                                        </strong>
                                        <div style={{ marginTop: '0.25rem', color: '#374151' }}>
                                            {Array.isArray(value) ? value.join(', ') : value}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// Composant Vue Équipements
export const EquipmentView = ({ equipment, setSelectedEquipment }) => {
    return (
        <div style={{ flex: 1, padding: '2rem', background: '#f8fafc', overflowY: 'auto' }}>
            <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
                <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '2rem' }}>
                    📡 Gestion des Équipements C2-EW
                </h2>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))', gap: '1.5rem' }}>
                    {equipment.map(eq => (
                        <div key={eq.id} style={{
                            background: 'white',
                            borderRadius: '12px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e5e7eb',
                            overflow: 'hidden',
                            cursor: 'pointer',
                            transition: 'transform 0.2s, box-shadow 0.2s'
                        }}
                        onClick={() => setSelectedEquipment(eq)}
                        onMouseOver={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 15px -3px rgba(0, 0, 0, 0.1)';
                        }}
                        onMouseOut={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                        }}
                        >
                            <div style={{ padding: '1.5rem' }}>
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                                        {eq.name}
                                    </h3>
                                    <div style={{
                                        width: '12px',
                                        height: '12px',
                                        borderRadius: '50%',
                                        background: eq.status === 'operational' ? '#10b981' : '#f59e0b'
                                    }}></div>
                                </div>

                                <div style={{ marginBottom: '1rem' }}>
                                    <span style={{
                                        background: '#e5e7eb',
                                        color: '#374151',
                                        padding: '0.25rem 0.75rem',
                                        borderRadius: '12px',
                                        fontSize: '0.75rem',
                                        fontWeight: '600',
                                        textTransform: 'uppercase'
                                    }}>
                                        {eq.type}
                                    </span>
                                </div>

                                <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1rem' }}>
                                    📍 {eq.latitude.toFixed(4)}°N, {eq.longitude.toFixed(4)}°E
                                </div>

                                <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1rem' }}>
                                    🕐 Dernière mise à jour: {eq.lastUpdate}
                                </div>

                                <div style={{ fontSize: '0.875rem', color: '#374151' }}>
                                    {eq.properties.description}
                                </div>

                                <div style={{ marginTop: '1rem', paddingTop: '1rem', borderTop: '1px solid #e5e7eb' }}>
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                        <span style={{
                                            fontSize: '0.75rem',
                                            fontWeight: '600',
                                            color: eq.status === 'operational' ? '#166534' : '#92400e',
                                            textTransform: 'uppercase'
                                        }}>
                                            {eq.status === 'operational' ? '✅ Opérationnel' : '⚠️ Maintenance'}
                                        </span>
                                        <button style={{
                                            background: '#3b82f6',
                                            color: 'white',
                                            border: 'none',
                                            padding: '0.5rem 1rem',
                                            borderRadius: '6px',
                                            fontSize: '0.75rem',
                                            cursor: 'pointer'
                                        }}>
                                            Voir Détails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

// Composant Vue Plugins
export const PluginsView = ({ plugins }) => {
    return (
        <div style={{ flex: 1, padding: '2rem', background: '#f8fafc', overflowY: 'auto' }}>
            <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
                <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '2rem' }}>
                    🔌 Modules et Plugins C2-EW
                </h2>

                <div style={{ display: 'grid', gap: '1.5rem' }}>
                    {plugins.map(plugin => (
                        <div key={plugin.id} style={{
                            background: 'white',
                            borderRadius: '12px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e5e7eb',
                            padding: '1.5rem'
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
                                <div>
                                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                                        {plugin.name}
                                    </h3>
                                    <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                                        Version {plugin.version}
                                    </p>
                                </div>
                                <div style={{
                                    padding: '0.5rem 1rem',
                                    borderRadius: '12px',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    background: plugin.status === 'active' ? '#dcfce7' : '#fef3c7',
                                    color: plugin.status === 'active' ? '#166534' : '#92400e'
                                }}>
                                    {plugin.status === 'active' ? '✅ Actif' : '⏸️ Inactif'}
                                </div>
                            </div>

                            <p style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '1rem' }}>
                                {plugin.description}
                            </p>

                            <div style={{ display: 'flex', gap: '0.75rem' }}>
                                <button style={{
                                    background: plugin.status === 'active' ? '#ef4444' : '#10b981',
                                    color: 'white',
                                    border: 'none',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer'
                                }}>
                                    {plugin.status === 'active' ? 'Désactiver' : 'Activer'}
                                </button>
                                <button style={{
                                    background: '#6b7280',
                                    color: 'white',
                                    border: 'none',
                                    padding: '0.5rem 1rem',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer'
                                }}>
                                    Configuration
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

// Composant Vue Alertes
export const AlertsView = ({ alerts }) => {
    return (
        <div style={{ flex: 1, padding: '2rem', background: '#f8fafc', overflowY: 'auto' }}>
            <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
                <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '2rem' }}>
                    🚨 Centre d'Alertes et Notifications
                </h2>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                    {alerts.map(alert => (
                        <div key={alert.id} style={{
                            background: 'white',
                            borderRadius: '12px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e5e7eb',
                            padding: '1.5rem'
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                                <div style={{
                                    width: '12px',
                                    height: '12px',
                                    borderRadius: '50%',
                                    background: alert.type === 'warning' ? '#f59e0b' :
                                               alert.type === 'success' ? '#10b981' : '#3b82f6'
                                }}></div>
                                <div style={{ flex: 1 }}>
                                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                                        {alert.title}
                                    </h3>
                                    <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
                                        {alert.timestamp}
                                    </p>
                                </div>
                                <div style={{
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '12px',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    textTransform: 'uppercase',
                                    background: alert.type === 'warning' ? '#fef3c7' :
                                               alert.type === 'success' ? '#dcfce7' : '#dbeafe',
                                    color: alert.type === 'warning' ? '#92400e' :
                                           alert.type === 'success' ? '#166534' : '#1e40af'
                                }}>
                                    {alert.type}
                                </div>
                            </div>

                            <p style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '1rem' }}>
                                {alert.message}
                            </p>

                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                                    Équipement ID: {alert.equipment_id}
                                </span>
                                <div style={{ display: 'flex', gap: '0.5rem' }}>
                                    <button style={{
                                        background: '#3b82f6',
                                        color: 'white',
                                        border: 'none',
                                        padding: '0.5rem 1rem',
                                        borderRadius: '6px',
                                        fontSize: '0.75rem',
                                        cursor: 'pointer'
                                    }}>
                                        Voir Équipement
                                    </button>
                                    <button style={{
                                        background: '#6b7280',
                                        color: 'white',
                                        border: 'none',
                                        padding: '0.5rem 1rem',
                                        borderRadius: '6px',
                                        fontSize: '0.75rem',
                                        cursor: 'pointer'
                                    }}>
                                        Marquer Lu
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

// Composant Vue Paramètres
export const SettingsView = ({ isOfflineMode, setIsOfflineMode }) => {
    return (
        <div style={{ flex: 1, padding: '2rem', background: '#f8fafc', overflowY: 'auto' }}>
            <div style={{ maxWidth: '800px', margin: '0 auto' }}>
                <h2 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '2rem' }}>
                    ⚙️ Paramètres et Configuration
                </h2>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                    {/* Mode hors ligne */}
                    <div style={{
                        background: 'white',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb',
                        padding: '1.5rem'
                    }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                            📱 Mode Hors Ligne
                        </h3>
                        <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1rem' }}>
                            Activez le mode hors ligne pour utiliser l'application sans connexion internet.
                            Les cartes et données seront mises en cache localement.
                        </p>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                            <button
                                onClick={() => setIsOfflineMode(!isOfflineMode)}
                                style={{
                                    background: isOfflineMode ? '#10b981' : '#6b7280',
                                    color: 'white',
                                    border: 'none',
                                    padding: '0.75rem 1.5rem',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem',
                                    cursor: 'pointer',
                                    fontWeight: '500'
                                }}
                            >
                                {isOfflineMode ? '✅ Mode Hors Ligne Activé' : '📱 Activer Mode Hors Ligne'}
                            </button>
                            <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                                {isOfflineMode ? 'Utilisation des données en cache' : 'Connexion internet requise'}
                            </span>
                        </div>
                    </div>

                    {/* Configuration SIG */}
                    <div style={{
                        background: 'white',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb',
                        padding: '1.5rem'
                    }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                            🗺️ Configuration SIG
                        </h3>
                        <div style={{ display: 'grid', gap: '1rem' }}>
                            <div>
                                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                                    Système de Coordonnées
                                </label>
                                <select style={{
                                    width: '100%',
                                    padding: '0.5rem',
                                    border: '1px solid #d1d5db',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem'
                                }}>
                                    <option>WGS84 (EPSG:4326)</option>
                                    <option>Web Mercator (EPSG:3857)</option>
                                    <option>UTM Zone 29N (EPSG:32629)</option>
                                </select>
                            </div>
                            <div>
                                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                                    Unités de Mesure
                                </label>
                                <select style={{
                                    width: '100%',
                                    padding: '0.5rem',
                                    border: '1px solid #d1d5db',
                                    borderRadius: '6px',
                                    fontSize: '0.875rem'
                                }}>
                                    <option>Métrique (m, km)</option>
                                    <option>Impérial (ft, mi)</option>
                                    <option>Nautique (nm)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Informations système */}
                    <div style={{
                        background: 'white',
                        borderRadius: '12px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb',
                        padding: '1.5rem'
                    }}>
                        <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                            ℹ️ Informations Système
                        </h3>
                        <div style={{ display: 'grid', gap: '0.75rem', fontSize: '0.875rem' }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <span style={{ color: '#6b7280' }}>Version C2-EW:</span>
                                <span style={{ fontWeight: '500' }}>v2.1.0</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <span style={{ color: '#6b7280' }}>Module SIG:</span>
                                <span style={{ fontWeight: '500' }}>OpenLayers 8.2.0</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <span style={{ color: '#6b7280' }}>Territoire:</span>
                                <span style={{ fontWeight: '500' }}>🇲🇦 Maroc</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                <span style={{ color: '#6b7280' }}>Statut Connexion:</span>
                                <span style={{ fontWeight: '500', color: isOfflineMode ? '#f59e0b' : '#10b981' }}>
                                    {isOfflineMode ? '📱 Hors ligne' : '🌐 En ligne'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
