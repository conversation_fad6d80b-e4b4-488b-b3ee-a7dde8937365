{"name": "openlayers-ext", "version": "3.1.8", "description": "A set of cool extensions for OpenLayers (ol).", "main": "index.js", "peerDependencies": {"ol": ">= 4.6.5"}, "devDependencies": {"gulp": "^3.9.1", "gulp-autoprefixer": "^5.0.0", "gulp-clean": "^0.4.0", "gulp-concat": "^2.6.0", "gulp-cssmin": "^0.2.0", "gulp-header": "^1.8.8", "gulp-minify": "^2.1.0", "gulp-watch": "^5.0.0", "minimist": "^1.2.0", "plugin-error": "^1.0.1", "through2": "^2.0.3"}, "scripts": {"test": "npm test npmtest.js", "prepack": "gulp lib"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Viglino"}, "bugs": {"url": "https://github.com/Viglino/ol-ext/issues"}, "homepage": "https://github.com/Viglino/ol-ext#,", "keywords": ["ol3", "openlayers", "popup", "menu", "symbol", "renderer", "filter", "canvas", "interaction", "split", "statistic", "charts", "pie", "LayerSwitcher", "toolbar", "animation"], "repository": {"type": "git", "url": "git+https://github.com/Viglino/ol-ext.git"}, "license": "BSD-3-<PERSON><PERSON>"}