"""
Schémas Pydantic pour les tuiles MBTiles
Module SIG C2-EW
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

# ===================================
# Enums
# ===================================

class TileFormat(str, Enum):
    pbf = "pbf"
    png = "png"
    jpg = "jpg"
    jpeg = "jpeg"
    webp = "webp"
    mvt = "mvt"

# ===================================
# Schémas de base
# ===================================

class TileCoordinate(BaseModel):
    z: int = Field(..., ge=0, le=22, description="Niveau de zoom")
    x: int = Field(..., ge=0, description="Coordonnée X de la tuile")
    y: int = Field(..., ge=0, description="Coordonnée Y de la tuile")

class BoundsArray(BaseModel):
    bounds: List[float] = Field(..., min_items=4, max_items=4, description="[ouest, sud, est, nord]")
    
    @validator('bounds')
    def validate_bounds(cls, v):
        if len(v) != 4:
            raise ValueError('bounds must have exactly 4 values [west, south, east, north]')
        west, south, east, north = v
        if west >= east or south >= north:
            raise ValueError('invalid bounds: west >= east or south >= north')
        if west < -180 or east > 180 or south < -90 or north > 90:
            raise ValueError('bounds values out of valid range')
        return v

class CenterPoint(BaseModel):
    center: List[float] = Field(..., min_items=3, max_items=3, description="[longitude, latitude, zoom]")
    
    @validator('center')
    def validate_center(cls, v):
        if len(v) != 3:
            raise ValueError('center must have exactly 3 values [lon, lat, zoom]')
        lon, lat, zoom = v
        if lon < -180 or lon > 180:
            raise ValueError('longitude out of range [-180, 180]')
        if lat < -90 or lat > 90:
            raise ValueError('latitude out of range [-90, 90]')
        if zoom < 0 or zoom > 22:
            raise ValueError('zoom out of range [0, 22]')
        return v

# ===================================
# Schémas de métadonnées
# ===================================

class TileStats(BaseModel):
    total_tiles: int = Field(..., ge=0)
    total_size_bytes: int = Field(..., ge=0)
    zoom_distribution: Dict[int, int] = Field(default_factory=dict)

class VectorLayer(BaseModel):
    id: str = Field(..., description="Identifiant de la couche")
    description: Optional[str] = Field(None, description="Description de la couche")
    minzoom: Optional[int] = Field(None, ge=0, le=22)
    maxzoom: Optional[int] = Field(None, ge=0, le=22)
    fields: Optional[Dict[str, str]] = Field(None, description="Champs et leurs types")

class TilesetMetadata(BaseModel):
    name: str = Field(..., description="Nom du tileset")
    description: Optional[str] = Field(None, description="Description du tileset")
    format: TileFormat = Field(..., description="Format des tuiles")
    minzoom: int = Field(..., ge=0, le=22)
    maxzoom: int = Field(..., ge=0, le=22)
    bounds: List[float] = Field(..., description="Emprise [ouest, sud, est, nord]")
    center: List[float] = Field(..., description="Centre [longitude, latitude, zoom]")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Métadonnées MBTiles")
    tile_stats: TileStats = Field(..., description="Statistiques des tuiles")
    last_accessed: Optional[str] = Field(None, description="Dernière date d'accès")
    
    @validator('bounds')
    def validate_bounds(cls, v):
        if len(v) != 4:
            raise ValueError('bounds must have exactly 4 values')
        return v
    
    @validator('center')
    def validate_center(cls, v):
        if len(v) != 3:
            raise ValueError('center must have exactly 3 values')
        return v

# ===================================
# Schémas TileJSON
# ===================================

class TileJSON(BaseModel):
    tilejson: str = Field("3.0.0", description="Version de la spécification TileJSON")
    name: Optional[str] = Field(None, description="Nom du tileset")
    description: Optional[str] = Field(None, description="Description du tileset")
    version: str = Field("1.0.0", description="Version du tileset")
    attribution: Optional[str] = Field(None, description="Attribution des données")
    template: Optional[str] = Field(None, description="Template d'URL")
    legend: Optional[str] = Field(None, description="Légende HTML")
    scheme: str = Field("xyz", description="Schéma de tuiles (xyz ou tms)")
    tiles: List[str] = Field(..., description="URLs des tuiles")
    grids: Optional[List[str]] = Field(None, description="URLs des grilles UTFGrid")
    data: Optional[List[str]] = Field(None, description="URLs des données")
    minzoom: int = Field(0, ge=0, le=22)
    maxzoom: int = Field(22, ge=0, le=22)
    bounds: List[float] = Field([-180, -85.0511, 180, 85.0511], description="Emprise")
    center: List[float] = Field([0, 0, 0], description="Centre par défaut")
    format: Optional[str] = Field(None, description="Format des tuiles")
    vector_layers: Optional[List[VectorLayer]] = Field(None, description="Couches vectorielles")
    
    @validator('bounds')
    def validate_bounds(cls, v):
        if len(v) != 4:
            raise ValueError('bounds must have exactly 4 values')
        return v
    
    @validator('center')
    def validate_center(cls, v):
        if len(v) != 3:
            raise ValueError('center must have exactly 3 values')
        return v

# ===================================
# Schémas de création/gestion
# ===================================

class TilesetCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Nom unique du tileset")
    description: Optional[str] = Field(None, max_length=1000, description="Description du tileset")
    file_path: str = Field(..., description="Chemin vers le fichier MBTiles")
    format: TileFormat = Field(TileFormat.pbf, description="Format des tuiles")
    
    @validator('name')
    def validate_name(cls, v):
        # Nom doit être un identifiant valide (lettres, chiffres, tirets, underscores)
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('name must contain only letters, numbers, hyphens and underscores')
        return v

class TilesetUpdate(BaseModel):
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = Field(None)

class TilesetInfo(BaseModel):
    id: str
    name: str
    description: Optional[str]
    format: str
    minzoom: int
    maxzoom: int
    bounds: List[float]
    center: List[float]
    file_size: int
    last_accessed: Optional[str]
    created_at: str

class TilesetList(BaseModel):
    status: str
    datasets: List[TilesetInfo]
    total: int

class TilesetResponse(BaseModel):
    status: str
    message: Optional[str] = None
    dataset: Optional[Dict[str, Any]] = None

# ===================================
# Schémas de statistiques
# ===================================

class FormatStats(BaseModel):
    count: int = Field(..., ge=0)
    total_size: int = Field(..., ge=0)

class GlobalTileStats(BaseModel):
    total_datasets: int = Field(..., ge=0)
    total_size_bytes: int = Field(..., ge=0)
    total_size_mb: float = Field(..., ge=0)
    format_distribution: Dict[str, FormatStats]
    cache_size: int = Field(..., ge=0)

class TileStatsResponse(BaseModel):
    status: str
    stats: GlobalTileStats

# ===================================
# Schémas de cache
# ===================================

class CacheInfo(BaseModel):
    cache_size: int = Field(..., ge=0, description="Nombre d'entrées en cache")
    max_cache_size: int = Field(..., ge=0, description="Taille maximale du cache")
    hit_rate: Optional[float] = Field(None, ge=0, le=1, description="Taux de succès du cache")
    memory_usage_mb: Optional[float] = Field(None, ge=0, description="Utilisation mémoire en MB")

class CacheStatsResponse(BaseModel):
    status: str
    cache_info: CacheInfo

# ===================================
# Schémas de proxy
# ===================================

class ProxyProvider(str, Enum):
    osm = "osm"
    satellite = "satellite"
    terrain = "terrain"

class ProxyRequest(BaseModel):
    provider: ProxyProvider
    z: int = Field(..., ge=0, le=22)
    x: int = Field(..., ge=0)
    y: int = Field(..., ge=0)
    cache_duration: Optional[int] = Field(3600, ge=0, description="Durée de cache en secondes")

# ===================================
# Schémas de santé
# ===================================

class TileHealthCheck(BaseModel):
    status: str
    datasets_count: int = Field(..., ge=0)
    cache_size: int = Field(..., ge=0)
    connections_count: int = Field(..., ge=0)
    error: Optional[str] = None

# ===================================
# Schémas d'erreur
# ===================================

class TileError(BaseModel):
    error: str
    detail: Optional[str] = None
    tile_coordinate: Optional[TileCoordinate] = None
    dataset_name: Optional[str] = None

class TileNotFound(BaseModel):
    error: str = "Tile not found"
    message: str
    z: int
    x: int
    y: int
    dataset: str

# ===================================
# Schémas de réponse générique
# ===================================

class TileAPIResponse(BaseModel):
    status: str
    message: Optional[str] = None
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
