/**
 * Application C2-EW simplifiée
 * Version de démarrage rapide
 */

import React, { useState } from 'react';
import SimpleMap from './components/map/SimpleMap';

function SimpleApp() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Fonction de connexion simplifiée
  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Simulation de connexion
      if (username === 'admin' && password === 'admin123') {
        setIsAuthenticated(true);
        localStorage.setItem('c2ew_token', 'demo_token');
      } else {
        setError('Nom d\'utilisateur ou mot de passe incorrect');
      }
    } catch (err) {
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  // Fonction de déconnexion
  const handleLogout = () => {
    setIsAuthenticated(false);
    setUsername('');
    setPassword('');
    localStorage.removeItem('c2ew_token');
  };

  // Vérifier si l'utilisateur est déjà connecté
  React.useEffect(() => {
    const token = localStorage.getItem('c2ew_token');
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  // Page de connexion
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 to-blue-700 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800">C2-EW Platform</h1>
            <p className="text-gray-600 mt-2">Système de Commandement et Contrôle</p>
            <p className="text-sm text-blue-600 mt-1">Module SIG - Maroc 🇲🇦</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom d'utilisateur
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="admin"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mot de passe
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="admin123"
                required
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Connexion...' : 'Se connecter'}
            </button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-600">
            <p>Comptes de test :</p>
            <div className="mt-2 space-y-1">
              <div>👤 <strong>admin</strong> / admin123</div>
              <div>🔧 operator / op123</div>
              <div>📊 viewer / view123</div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                API: Actif
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                SIG: Prêt
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Application principale
  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <header className="bg-blue-800 text-white p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold">C2-EW Platform</h1>
            <span className="text-blue-200">|</span>
            <span className="text-blue-200">Module SIG</span>
            <span className="text-xs bg-green-500 px-2 py-1 rounded">ACTIF</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm">
              <span className="text-blue-200">Connecté en tant que:</span>
              <span className="font-medium ml-1">Administrateur</span>
            </div>
            <button
              onClick={handleLogout}
              className="bg-blue-700 hover:bg-blue-600 px-3 py-1 rounded text-sm"
            >
              Déconnexion
            </button>
          </div>
        </div>
      </header>

      {/* Contenu principal */}
      <main className="flex-1 relative">
        <SimpleMap />
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white p-2 text-center text-sm">
        <div className="flex items-center justify-center space-x-6">
          <span>🗺️ Module SIG C2-EW v1.0.0</span>
          <span>🇲🇦 Territoire: Maroc</span>
          <span>📡 API: localhost:8000</span>
          <span>⚡ Frontend: localhost:3001</span>
        </div>
      </footer>
    </div>
  );
}

export default SimpleApp;
