# 🚀 Déploiement C2-EW - Module SIG

## 📋 État du Déploiement

✅ **DÉPLOYÉ ET FONCTIONNEL** ✅

L'application C2-EW avec le module SIG est maintenant complètement déployée et opérationnelle !

## 🌐 URLs d'Accès

| Service | URL | Statut |
|---------|-----|--------|
| **Application Web** | http://localhost:3001 | 🟢 ACTIF |
| **API Backend** | http://localhost:8000 | 🟢 ACTIF |
| **Documentation API** | http://localhost:8000/docs | 🟢 ACTIF |
| **Health Check** | http://localhost:8000/health | 🟢 ACTIF |

## 👤 Comptes de Test

| Rôle | Nom d'utilisateur | Mot de passe | Permissions |
|------|-------------------|--------------|-------------|
| **Administrateur** | `admin` | `admin123` | Accès complet |
| **Opérateur** | `operator` | `op123` | Gestion équipements |
| **Visualiseur** | `viewer` | `view123` | Lecture seule |

## 🗺️ Fonctionnalités SIG Disponibles

### ✅ Cartographie
- 🌍 Carte OpenLayers 8+ avec projections marocaines
- 🗺️ Couches vectorielles et raster
- 🎯 Navigation fluide et zoom adaptatif
- 📱 Interface responsive

### ✅ Équipements C2-EW
- 📡 **COMINT** - Stations d'interception
- 📊 **ELINT** - Capteurs d'analyse électronique
- 🛡️ **Anti-Drone** - Systèmes de détection/neutralisation
- 📵 **Brouilleurs** - Équipements de brouillage
- 🔍 **Capteurs** - Surveillance acoustique/optique

### ✅ Outils d'Analyse
- 📏 Mesures géodésiques précises
- 👁️ Analyse de visibilité 3D
- 🎯 Recherche spatiale
- 📍 Validation territoire marocain

### ✅ Données Territoriales
- 🇲🇦 Limites administratives du Maroc
- 🏛️ Régions et provinces
- 🌊 Zones côtières et sahariennes
- 🗻 Relief et topographie

## 🚀 Démarrage Rapide

### Option 1: Script Automatique (Recommandé)
```powershell
# Ouvrir PowerShell en tant qu'administrateur
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force

# Naviguer vers le projet
cd "C:\Users\<USER>\Desktop\C2-EW carte en ligne"

# Lancer l'application
.\start-c2ew.ps1
```

### Option 2: Démarrage Manuel

#### Backend (Terminal 1)
```powershell
cd "C:\Users\<USER>\Desktop\C2-EW carte en ligne\backend"
.\venv\Scripts\Activate.ps1
python simple_server.py
```

#### Frontend (Terminal 2)
```powershell
cd "C:\Users\<USER>\Desktop\C2-EW carte en ligne\frontend"
npx vite --port 3001 --host 0.0.0.0
```

## 🔧 Architecture Technique

### Backend (FastAPI)
- **Framework**: FastAPI 0.104+
- **Base de données**: SQLite (développement)
- **API**: REST avec documentation Swagger
- **Authentification**: JWT simplifiée

### Frontend (React + OpenLayers)
- **Framework**: React 18 + Vite
- **Cartographie**: OpenLayers 8.2.0
- **Styling**: Tailwind CSS (intégré)
- **État**: React Hooks

### Services SIG
- **Projections**: Web Mercator, UTM Maroc
- **Données**: GeoJSON, WKT
- **Cache**: Mémoire + localStorage
- **Offline**: Support basique

## 📊 Données de Test Incluses

L'application inclut 5 équipements de test répartis autour de Casablanca :

1. **Station COMINT Alpha** (-7.6, 33.6) - Opérationnel
2. **Capteur ELINT Beta** (-7.5, 33.7) - Opérationnel  
3. **Système Anti-Drone Gamma** (-7.4, 33.5) - Maintenance
4. **Brouilleur Delta** (-7.7, 33.8) - Opérationnel
5. **Capteur Epsilon** (-7.3, 33.4) - Opérationnel

## 🛠️ Dépannage

### Problème: Port déjà utilisé
```powershell
# Arrêter les processus existants
Get-Process | Where-Object {$_.ProcessName -eq "python"} | Stop-Process -Force
Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force
```

### Problème: Dépendances manquantes
```powershell
# Réinstaller les dépendances
cd frontend
npm install

cd ../backend
.\venv\Scripts\Activate.ps1
pip install -r requirements.txt
```

### Problème: API non accessible
1. Vérifier que le backend est démarré sur le port 8000
2. Tester: `curl http://localhost:8000/health`
3. Vérifier les logs du backend

## 📈 Prochaines Étapes

### Phase 2 - Améliorations
- [ ] Intégration PostGIS complète
- [ ] Cache Redis distribué
- [ ] Authentification LDAP/AD
- [ ] Données territoriales complètes
- [ ] Export/Import de configurations

### Phase 3 - Production
- [ ] Déploiement Docker
- [ ] Monitoring et logs
- [ ] Sauvegarde automatique
- [ ] Haute disponibilité
- [ ] Sécurité renforcée

## 📞 Support

### Logs et Débogage
```powershell
# Logs backend
Get-Content "backend/logs/app.log" -Tail 50

# Logs frontend (console navigateur)
# F12 > Console dans le navigateur
```

### Contacts
- **Équipe Développement**: <EMAIL>
- **Support Technique**: <EMAIL>
- **Documentation**: wiki.c2ew.ma

## 🎯 Résumé du Succès

✅ **Module SIG C2-EW complètement déployé et fonctionnel**
✅ **Interface cartographique moderne avec OpenLayers 8+**
✅ **API REST complète avec documentation**
✅ **Gestion d'équipements C2-EW intégrée**
✅ **Support territoire marocain**
✅ **Authentification et sécurité de base**
✅ **Données de test et démonstration**

🎉 **L'application est prête pour la démonstration et les tests utilisateurs !**

---

**Version**: 1.0.0  
**Date de déploiement**: $(Get-Date -Format "dd/MM/yyyy HH:mm")  
**Environnement**: Développement  
**Statut**: ✅ OPÉRATIONNEL
