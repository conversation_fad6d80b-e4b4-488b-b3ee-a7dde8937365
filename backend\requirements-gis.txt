# ===================================
# Dépendances SIG pour Module Cartographique C2-EW
# ===================================

# === Core GIS Libraries ===
# PostGIS et géospatial
geoalchemy2==0.14.2          # ORM géospatial pour SQLAlchemy
shapely==2.0.2               # Manipulation géométries
geojson==3.1.0               # Support GeoJSON
pyproj==3.6.1                # Projections cartographiques
fiona==1.9.5                 # Lecture/écriture formats géospatiaux

# GDAL/OGR pour traitement raster/vector
gdal==3.7.3                  # Geospatial Data Abstraction Library
rasterio==1.3.9              # Traitement raster Python
geopandas==0.14.1            # DataFrames géospatiaux

# === Database Libraries ===
# SQLite spatial
pyspatialite==5.0.1          # SpatiaLite pour SQLite
sqlite-utils==3.35           # Utilitaires SQLite

# PostgreSQL/PostGIS
asyncpg==0.29.0              # Driver PostgreSQL async
psycopg2-binary==2.9.9       # Driver PostgreSQL sync (backup)

# === Tile Processing ===
# MBTiles et tuiles
mbtiles==1.3.1               # Support MBTiles
mercantile==1.2.1            # Utilitaires tuiles web
tiletanic==1.2.0             # Génération tuiles

# === Spatial Analysis ===
# Algorithmes spatiaux avancés
scikit-image==0.22.0         # Traitement d'images pour MNT
scipy==1.11.4                # Calculs scientifiques
numpy==1.25.2                # Calculs numériques optimisés

# Analyse de visibilité
elevation==1.1.3             # Données d'élévation
srtm.py==0.3.7               # Données SRTM

# === Performance & Caching ===
# Cache et performance
redis==5.0.1                 # Cache Redis
hiredis==2.2.3               # Parser Redis optimisé
lru-dict==1.3.0              # Cache LRU

# Traitement parallèle
joblib==1.3.2                # Parallélisation
multiprocessing-logging==0.3.4  # Logs multiprocessing

# === FastAPI Extensions ===
# Extensions FastAPI pour SIG
fastapi-geoalchemy==0.1.0    # Extension géospatiale FastAPI
fastapi-cache2==0.2.1        # Cache FastAPI
fastapi-limiter==0.1.5       # Rate limiting

# === Utilities ===
# Utilitaires géospatiaux
geopy==2.4.1                 # Géocodage et calculs
haversine==2.8.1             # Calculs de distance
utm==0.7.0                   # Conversion UTM

# Validation et sérialisation
pydantic-geojson==0.1.1      # Validation GeoJSON avec Pydantic
geojson-pydantic==1.0.1      # Modèles Pydantic pour GeoJSON

# === Development & Testing ===
# Tests géospatiaux
pytest-asyncio==0.21.1       # Tests async
pytest-mock==3.12.0          # Mocking pour tests
factory-boy==3.3.0           # Factory pour tests

# Profiling et debugging
memory-profiler==0.61.0      # Profiling mémoire
line-profiler==4.1.1         # Profiling ligne par ligne

# === Optional Advanced Features ===
# Machine Learning géospatial (optionnel)
# scikit-learn==1.3.2        # ML pour analyse spatiale
# tensorflow==2.15.0         # Deep learning géospatial

# Traitement d'images satellite (optionnel)
# pillow==10.1.0             # Traitement d'images
# opencv-python==4.8.1.78   # Vision par ordinateur

# === Version Constraints ===
# Contraintes de versions pour compatibilité
sqlalchemy>=1.4.0,<2.1.0    # Compatible avec GeoAlchemy2
fastapi>=0.104.0             # Version récente FastAPI
pydantic>=2.0.0,<3.0.0       # Pydantic v2

# === System Dependencies Notes ===
# Les dépendances système suivantes doivent être installées :
# 
# Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install -y \
#     gdal-bin \
#     libgdal-dev \
#     libspatialite7 \
#     libspatialite-dev \
#     spatialite-bin \
#     libproj-dev \
#     proj-data \
#     proj-bin \
#     libgeos-dev
#
# Windows:
# - Installer OSGeo4W ou utiliser conda-forge
# - conda install -c conda-forge gdal geos proj spatialite
#
# macOS:
# brew install gdal geos proj spatialite-tools
