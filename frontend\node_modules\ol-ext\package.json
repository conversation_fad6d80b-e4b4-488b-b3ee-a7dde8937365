{"name": "ol-ext", "version": "4.0.33", "description": "A set of cool extensions for OpenLayers (ol) in node modules structure", "main": "dist/ol-ext.js", "style": "dist/ol-ext.css", "peerDependencies": {"ol": ">= 5.3.0"}, "devDependencies": {"eslint": "^5.10.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^5.0.0", "gulp-clean": "^0.4.0", "gulp-cli": "^2.2.0", "gulp-concat": "^2.6.1", "gulp-cssmin": "^0.2.0", "gulp-header": "^2.0.5", "gulp-minify": "^3.1.0", "gulp-watch": "^5.0.1", "gulp-jsdoc3": "^2.0.0", "live-server": "^1.2.0", "minimist": "^1.2.0", "plugin-error": "^1.0.1", "through2": "^2.0.3"}, "scripts": {"test": "npm test npmtest.js", "start": "node ./node_modules/gulp/bin/gulp.js --gulpfile ./gulpfile.cjs serve", "build": "node ./node_modules/gulp/bin/gulp.js --gulpfile ./gulpfile.cjs", "doc": "node ./node_modules/gulp/bin/gulp.js --gulpfile ./gulpfile.cjs doc", "lint": "eslint --fix src/ || exit 0", "prepack": "gulp prepublish --gulpfile ./gulpfile.cjs", "postpack": "gulp postpublish --gulpfile ./gulpfile.cjs"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Viglino"}, "contributors": [{"name": "ThomasG77", "url": "https://github.com/ThomasG77"}, {"name": "darkscript", "url": "https://github.com/darkscript"}], "bugs": {"url": "https://github.com/Viglino/ol-ext/issues"}, "homepage": "https://github.com/Viglino/ol-ext#,", "keywords": ["ol3", "openlayers", "popup", "menu", "symbol", "renderer", "filter", "canvas", "interaction", "split", "statistic", "charts", "pie", "LayerSwitcher", "toolbar", "animation"], "type": "module", "repository": {"type": "git", "url": "git+https://github.com/Viglino/ol-ext.git"}, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {}}