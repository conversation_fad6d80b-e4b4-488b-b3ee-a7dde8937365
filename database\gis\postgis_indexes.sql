-- ===================================
-- Index Spatiaux Optimisés pour PostGIS
-- Module SIG C2-EW
-- ===================================

-- ===================================
-- Index pour spatial_layers
-- ===================================

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_spatial_layer_name ON spatial_layers(name);
CREATE INDEX IF NOT EXISTS idx_spatial_layer_type ON spatial_layers(layer_type);
CREATE INDEX IF NOT EXISTS idx_spatial_layer_srid ON spatial_layers(srid);
CREATE INDEX IF NOT EXISTS idx_spatial_layer_visible ON spatial_layers(visible);
CREATE INDEX IF NOT EXISTS idx_spatial_layer_created ON spatial_layers(created_at);

-- Index composites
CREATE INDEX IF NOT EXISTS idx_spatial_layer_type_visible ON spatial_layers(layer_type, visible);

-- ===================================
-- Index pour spatial_features
-- ===================================

-- Index spatiaux (GIST pour géométries)
CREATE INDEX IF NOT EXISTS idx_spatial_feature_geometry ON spatial_features USING GIST(geometry);
CREATE INDEX IF NOT EXISTS idx_spatial_feature_geography ON spatial_features USING GIST(geography);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_spatial_feature_layer ON spatial_features(layer_id);
CREATE INDEX IF NOT EXISTS idx_spatial_feature_type ON spatial_features(feature_type);
CREATE INDEX IF NOT EXISTS idx_spatial_feature_name ON spatial_features(name);

-- Index composites
CREATE INDEX IF NOT EXISTS idx_spatial_feature_layer_type ON spatial_features(layer_id, feature_type);

-- Index GIN pour propriétés JSON
CREATE INDEX IF NOT EXISTS idx_spatial_feature_properties ON spatial_features USING GIN(properties);

-- ===================================
-- Index pour morocco_territory
-- ===================================

-- Index spatial principal (GIST)
CREATE INDEX IF NOT EXISTS idx_morocco_territory_geometry ON morocco_territory USING GIST(geometry);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_morocco_region_name ON morocco_territory(region_name);
CREATE INDEX IF NOT EXISTS idx_morocco_region_type ON morocco_territory(region_type);
CREATE INDEX IF NOT EXISTS idx_morocco_admin_level ON morocco_territory(administrative_level);
CREATE INDEX IF NOT EXISTS idx_morocco_official_code ON morocco_territory(official_code);
CREATE INDEX IF NOT EXISTS idx_morocco_parent_region ON morocco_territory(parent_region_id);

-- Index composites pour requêtes hiérarchiques
CREATE INDEX IF NOT EXISTS idx_morocco_admin_parent ON morocco_territory(administrative_level, parent_region_id);
CREATE INDEX IF NOT EXISTS idx_morocco_type_level ON morocco_territory(region_type, administrative_level);

-- Index pour recherche textuelle
CREATE INDEX IF NOT EXISTS idx_morocco_region_name_trgm ON morocco_territory USING GIN(region_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_morocco_region_name_ar_trgm ON morocco_territory USING GIN(region_name_ar gin_trgm_ops);

-- ===================================
-- Index pour mbtiles_datasets
-- ===================================

-- Index spatial pour emprise
CREATE INDEX IF NOT EXISTS idx_mbtiles_bounds ON mbtiles_datasets USING GIST(bounds);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_mbtiles_name ON mbtiles_datasets(name);
CREATE INDEX IF NOT EXISTS idx_mbtiles_format ON mbtiles_datasets(format);
CREATE INDEX IF NOT EXISTS idx_mbtiles_active ON mbtiles_datasets(is_active);
CREATE INDEX IF NOT EXISTS idx_mbtiles_accessed ON mbtiles_datasets(last_accessed);

-- Index composites pour zoom
CREATE INDEX IF NOT EXISTS idx_mbtiles_zoom_range ON mbtiles_datasets(min_zoom, max_zoom);
CREATE INDEX IF NOT EXISTS idx_mbtiles_active_format ON mbtiles_datasets(is_active, format);

-- Index GIN pour métadonnées
CREATE INDEX IF NOT EXISTS idx_mbtiles_metadata ON mbtiles_datasets USING GIN(metadata);

-- ===================================
-- Index pour visibility_analyses
-- ===================================

-- Index spatiaux
CREATE INDEX IF NOT EXISTS idx_visibility_observer_point ON visibility_analyses USING GIST(observer_point);
CREATE INDEX IF NOT EXISTS idx_visibility_visible_area ON visibility_analyses USING GIST(visible_area);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_visibility_name ON visibility_analyses(name);
CREATE INDEX IF NOT EXISTS idx_visibility_max_distance ON visibility_analyses(max_distance);
CREATE INDEX IF NOT EXISTS idx_visibility_created ON visibility_analyses(created_at);

-- Index composites pour performance
CREATE INDEX IF NOT EXISTS idx_visibility_distance_height ON visibility_analyses(max_distance, observer_height);

-- Index GIN pour paramètres
CREATE INDEX IF NOT EXISTS idx_visibility_parameters ON visibility_analyses USING GIN(parameters);

-- ===================================
-- Index pour spatial_measurements
-- ===================================

-- Index spatial
CREATE INDEX IF NOT EXISTS idx_measurement_geometry ON spatial_measurements USING GIST(geometry);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_measurement_type ON spatial_measurements(measurement_type);
CREATE INDEX IF NOT EXISTS idx_measurement_value ON spatial_measurements(value);
CREATE INDEX IF NOT EXISTS idx_measurement_unit ON spatial_measurements(unit);
CREATE INDEX IF NOT EXISTS idx_measurement_name ON spatial_measurements(name);

-- Index composites
CREATE INDEX IF NOT EXISTS idx_measurement_type_unit ON spatial_measurements(measurement_type, unit);

-- Index GIN pour propriétés
CREATE INDEX IF NOT EXISTS idx_measurement_properties ON spatial_measurements USING GIN(properties);

-- ===================================
-- Index pour spatial_cache
-- ===================================

-- Index spatial
CREATE INDEX IF NOT EXISTS idx_cache_geometry ON spatial_cache USING GIST(geometry_data);

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_cache_key ON spatial_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_type ON spatial_cache(cache_type);
CREATE INDEX IF NOT EXISTS idx_cache_expires ON spatial_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_cache_accessed ON spatial_cache(last_accessed);
CREATE INDEX IF NOT EXISTS idx_cache_source ON spatial_cache(source_id);

-- Index composites pour éviction de cache
CREATE INDEX IF NOT EXISTS idx_cache_priority_accessed ON spatial_cache(priority DESC, last_accessed ASC);
CREATE INDEX IF NOT EXISTS idx_cache_type_expires ON spatial_cache(cache_type, expires_at);

-- Index GIN pour données JSON
CREATE INDEX IF NOT EXISTS idx_cache_data ON spatial_cache USING GIN(data);

-- ===================================
-- Index pour sync_logs
-- ===================================

-- Index attributaires
CREATE INDEX IF NOT EXISTS idx_sync_type ON sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_status ON sync_logs(status);
CREATE INDEX IF NOT EXISTS idx_sync_table ON sync_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_record ON sync_logs(record_id);
CREATE INDEX IF NOT EXISTS idx_sync_session ON sync_logs(sync_session_id);
CREATE INDEX IF NOT EXISTS idx_sync_created ON sync_logs(created_at);

-- Index composites pour monitoring
CREATE INDEX IF NOT EXISTS idx_sync_table_status ON sync_logs(table_name, status);
CREATE INDEX IF NOT EXISTS idx_sync_session_status ON sync_logs(sync_session_id, status);
CREATE INDEX IF NOT EXISTS idx_sync_type_created ON sync_logs(sync_type, created_at);

-- Index GIN pour données JSON
CREATE INDEX IF NOT EXISTS idx_sync_local_data ON sync_logs USING GIN(local_data);
CREATE INDEX IF NOT EXISTS idx_sync_remote_data ON sync_logs USING GIN(remote_data);

-- ===================================
-- Index Partiels pour Performance
-- ===================================

-- Index partiels pour données actives seulement
CREATE INDEX IF NOT EXISTS idx_spatial_layer_active ON spatial_layers(name, layer_type) WHERE visible = TRUE;
CREATE INDEX IF NOT EXISTS idx_mbtiles_active_only ON mbtiles_datasets(name, format) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_morocco_official_only ON morocco_territory(region_name, region_type) WHERE is_official = TRUE;

-- Index partiels pour cache non expiré
CREATE INDEX IF NOT EXISTS idx_cache_valid ON spatial_cache(cache_key, cache_type) 
WHERE expires_at IS NULL OR expires_at > NOW();

-- Index partiels pour sync en erreur
CREATE INDEX IF NOT EXISTS idx_sync_errors ON sync_logs(table_name, created_at) 
WHERE status IN ('error', 'conflict');

-- ===================================
-- Statistiques et Maintenance
-- ===================================

-- Mise à jour des statistiques pour l'optimiseur
ANALYZE spatial_layers;
ANALYZE spatial_features;
ANALYZE morocco_territory;
ANALYZE mbtiles_datasets;
ANALYZE visibility_analyses;
ANALYZE spatial_measurements;
ANALYZE spatial_cache;
ANALYZE sync_logs;

-- ===================================
-- Fonctions d'Index Personnalisées
-- ===================================

-- Fonction pour index spatial optimisé selon la densité
CREATE OR REPLACE FUNCTION create_adaptive_spatial_index(
    table_name TEXT,
    geometry_column TEXT DEFAULT 'geometry'
) RETURNS VOID AS $$
DECLARE
    feature_count INTEGER;
    index_name TEXT;
BEGIN
    -- Compter les entités
    EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO feature_count;
    
    -- Nom de l'index
    index_name := format('idx_%s_%s_adaptive', table_name, geometry_column);
    
    -- Créer index adapté selon la densité
    IF feature_count > 100000 THEN
        -- Index GIST avec paramètres optimisés pour gros volumes
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIST(%I) WITH (fillfactor=90)', 
                      index_name, table_name, geometry_column);
    ELSE
        -- Index GIST standard
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I USING GIST(%I)', 
                      index_name, table_name, geometry_column);
    END IF;
    
    RAISE NOTICE 'Index spatial adaptatif créé: % pour % entités', index_name, feature_count;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- Triggers pour Maintenance Auto
-- ===================================

-- Trigger pour mise à jour automatique des statistiques spatiales
CREATE OR REPLACE FUNCTION update_spatial_stats() RETURNS TRIGGER AS $$
BEGIN
    -- Mise à jour des statistiques après modifications importantes
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN
        -- Programmer une mise à jour des statistiques (asynchrone)
        PERFORM pg_stat_reset_single_table_counters(TG_RELID);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Application du trigger sur les tables principales
CREATE TRIGGER trigger_spatial_stats_spatial_features
    AFTER INSERT OR UPDATE OR DELETE ON spatial_features
    FOR EACH STATEMENT EXECUTE FUNCTION update_spatial_stats();

CREATE TRIGGER trigger_spatial_stats_morocco_territory
    AFTER INSERT OR UPDATE OR DELETE ON morocco_territory
    FOR EACH STATEMENT EXECUTE FUNCTION update_spatial_stats();
