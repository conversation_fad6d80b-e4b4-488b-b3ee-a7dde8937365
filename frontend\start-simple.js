/**
 * Script de démarrage simplifié pour C2-EW
 * Utilise Vite avec configuration minimale
 */

import { createServer } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function startServer() {
  console.log('🚀 Démarrage du serveur C2-EW simplifié...');
  
  try {
    // Configuration Vite simplifiée
    const server = await createServer({
      root: __dirname,
      plugins: [react()],
      server: {
        port: 3001,
        host: '0.0.0.0',
        open: true
      },
      build: {
        rollupOptions: {
          input: {
            main: path.resolve(__dirname, 'index-simple.html')
          }
        }
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, 'src')
        }
      }
    });

    await server.listen();
    
    console.log('✅ Serveur démarré avec succès!');
    console.log('🌐 URL: http://localhost:3001');
    console.log('📡 API Backend: http://localhost:8000');
    console.log('🗺️ Module SIG: Prêt');
    
  } catch (error) {
    console.error('❌ Erreur lors du démarrage:', error);
    process.exit(1);
  }
}

startServer();
