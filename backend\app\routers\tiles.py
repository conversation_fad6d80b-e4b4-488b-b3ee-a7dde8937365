"""
Endpoints FastAPI pour les tuiles MBTiles
Module SIG C2-EW - Serveur de tuiles vectorielles et raster
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Response
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from uuid import UUID

from ..services.tile_service import tile_service
from ..core.auth import get_current_user
from ..schemas.tile_schemas import (
    TilesetMetadata, TilesetList, TilesetCreate, TilesetResponse
)

router = APIRouter(prefix="/api/v1/tiles", tags=["Tiles"])

# ===================================
# Endpoints de service de tuiles
# ===================================

@router.get("/{dataset_name}/{z}/{x}/{y}")
async def get_tile(
    dataset_name: str = Path(..., description="Nom du dataset MBTiles"),
    z: int = Path(..., ge=0, le=22, description="Niveau de zoom"),
    x: int = Path(..., ge=0, description="Coordonnée X de la tuile"),
    y: int = Path(..., ge=0, description="Coordonnée Y de la tuile"),
    format: Optional[str] = Query(None, description="Format de sortie (pbf, png, jpg)")
):
    """Obtenir une tuile depuis un dataset MBTiles"""
    try:
        tile_data, content_type = await tile_service.get_tile(
            dataset_name, z, x, y, format
        )
        
        # Headers pour le cache
        headers = {
            "Cache-Control": "public, max-age=3600",  # Cache 1 heure
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "Content-Type"
        }
        
        return Response(
            content=tile_data,
            media_type=content_type,
            headers=headers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur serveur: {str(e)}")

@router.get("/{dataset_name}/metadata", response_model=TilesetMetadata)
async def get_tileset_metadata(
    dataset_name: str = Path(..., description="Nom du dataset MBTiles"),
    current_user = Depends(get_current_user)
):
    """Obtenir les métadonnées d'un tileset"""
    try:
        metadata = await tile_service.get_tileset_metadata(dataset_name)
        return TilesetMetadata(**metadata)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur obtention métadonnées: {str(e)}")

@router.get("/{dataset_name}/tilejson")
async def get_tilejson(
    dataset_name: str = Path(..., description="Nom du dataset MBTiles"),
    request_host: str = Query("localhost:8000", description="Host pour les URLs de tuiles")
):
    """Obtenir la spécification TileJSON pour un dataset"""
    try:
        metadata = await tile_service.get_tileset_metadata(dataset_name)
        
        # Construire l'URL de base
        base_url = f"http://{request_host}/api/v1/tiles/{dataset_name}"
        
        # Spécification TileJSON 3.0.0
        tilejson = {
            "tilejson": "3.0.0",
            "name": metadata["name"],
            "description": metadata.get("description", ""),
            "version": "1.0.0",
            "attribution": metadata.get("metadata", {}).get("attribution", ""),
            "scheme": "xyz",
            "tiles": [f"{base_url}/{{z}}/{{x}}/{{y}}"],
            "minzoom": metadata["minzoom"],
            "maxzoom": metadata["maxzoom"],
            "bounds": metadata["bounds"],
            "center": metadata["center"],
            "format": metadata["format"],
            "vector_layers": metadata.get("metadata", {}).get("json", {}).get("vector_layers", []) if metadata["format"] == "pbf" else None
        }
        
        # Supprimer les clés None
        tilejson = {k: v for k, v in tilejson.items() if v is not None}
        
        return JSONResponse(content=tilejson)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur génération TileJSON: {str(e)}")

# ===================================
# Endpoints de gestion des datasets
# ===================================

@router.get("/", response_model=TilesetList)
async def list_tilesets(
    current_user = Depends(get_current_user)
):
    """Lister tous les datasets MBTiles disponibles"""
    try:
        datasets = await tile_service.list_datasets()
        
        return TilesetList(
            status="success",
            datasets=datasets,
            total=len(datasets)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur listage datasets: {str(e)}")

@router.post("/", response_model=TilesetResponse)
async def create_tileset(
    tileset_data: TilesetCreate,
    current_user = Depends(get_current_user)
):
    """Créer un nouveau dataset MBTiles"""
    try:
        dataset = await tile_service.create_dataset(
            name=tileset_data.name,
            description=tileset_data.description,
            file_path=tileset_data.file_path,
            format_type=tileset_data.format
        )
        
        return TilesetResponse(
            status="success",
            message=f"Dataset '{tileset_data.name}' créé avec succès",
            dataset=dataset
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur création dataset: {str(e)}")

@router.delete("/{dataset_name}")
async def delete_tileset(
    dataset_name: str = Path(..., description="Nom du dataset à supprimer"),
    current_user = Depends(get_current_user)
):
    """Supprimer un dataset MBTiles"""
    try:
        success = await tile_service.delete_dataset(dataset_name)
        
        if success:
            return {
                "status": "success",
                "message": f"Dataset '{dataset_name}' supprimé avec succès"
            }
        else:
            raise HTTPException(status_code=500, detail="Erreur lors de la suppression")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur suppression dataset: {str(e)}")

# ===================================
# Endpoints utilitaires
# ===================================

@router.post("/cache/clear")
async def clear_tile_cache(
    current_user = Depends(get_current_user)
):
    """Vider le cache des tuiles"""
    try:
        await tile_service.clear_cache()
        
        return {
            "status": "success",
            "message": "Cache des tuiles vidé avec succès"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur vidage cache: {str(e)}")

@router.get("/stats/global")
async def get_global_stats(
    current_user = Depends(get_current_user)
):
    """Obtenir les statistiques globales des tuiles"""
    try:
        datasets = await tile_service.list_datasets()
        
        total_datasets = len(datasets)
        total_size = sum(d.get('file_size', 0) for d in datasets)
        
        # Calculer les statistiques par format
        format_stats = {}
        for dataset in datasets:
            format_type = dataset.get('format', 'unknown')
            if format_type not in format_stats:
                format_stats[format_type] = {
                    'count': 0,
                    'total_size': 0
                }
            format_stats[format_type]['count'] += 1
            format_stats[format_type]['total_size'] += dataset.get('file_size', 0)
        
        return {
            "status": "success",
            "stats": {
                "total_datasets": total_datasets,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "format_distribution": format_stats,
                "cache_size": len(tile_service.tile_cache)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur obtention statistiques: {str(e)}")

# ===================================
# Endpoints de proxy pour tuiles externes
# ===================================

@router.get("/proxy/{provider}/{z}/{x}/{y}")
async def proxy_external_tile(
    provider: str = Path(..., description="Fournisseur de tuiles (osm, satellite)"),
    z: int = Path(..., ge=0, le=22),
    x: int = Path(..., ge=0),
    y: int = Path(..., ge=0),
    current_user = Depends(get_current_user)
):
    """Proxy pour tuiles externes (avec cache local)"""
    try:
        import aiohttp
        import asyncio
        
        # URLs des fournisseurs
        providers = {
            "osm": "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
            "satellite": "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
        }
        
        if provider not in providers:
            raise HTTPException(status_code=400, detail=f"Fournisseur '{provider}' non supporté")
        
        url = providers[provider].format(z=z, x=x, y=y)
        
        # Vérifier le cache local
        cache_key = f"proxy_{provider}_{z}_{x}_{y}"
        if cache_key in tile_service.tile_cache:
            tile_data, content_type = tile_service.tile_cache[cache_key]
            return Response(content=tile_data, media_type=content_type)
        
        # Télécharger la tuile
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    tile_data = await response.read()
                    content_type = response.headers.get('content-type', 'image/png')
                    
                    # Mettre en cache
                    if len(tile_data) < 100000:  # 100KB max
                        tile_service.tile_cache[cache_key] = (tile_data, content_type)
                    
                    headers = {
                        "Cache-Control": "public, max-age=86400",  # Cache 24h
                        "Access-Control-Allow-Origin": "*"
                    }
                    
                    return Response(
                        content=tile_data,
                        media_type=content_type,
                        headers=headers
                    )
                else:
                    raise HTTPException(status_code=404, detail="Tuile non trouvée")
                    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur proxy tuile: {str(e)}")

# ===================================
# Endpoints de santé
# ===================================

@router.get("/health")
async def tiles_health_check():
    """Vérification de santé du service de tuiles"""
    try:
        datasets = await tile_service.list_datasets()
        
        return {
            "status": "healthy",
            "datasets_count": len(datasets),
            "cache_size": len(tile_service.tile_cache),
            "connections_count": len(tile_service.mbtiles_connections)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
