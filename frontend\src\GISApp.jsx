/**
 * Application SIG C2-EW Complète
 * Version avec toutes les fonctionnalités visibles
 */

import React, { useState, useEffect, useRef } from 'react';
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import LineString from 'ol/geom/LineString';
import Polygon from 'ol/geom/Polygon';
import { Style, Circle, Fill, Stroke, Text, Icon } from 'ol/style';
import { fromLonLat, toLonLat } from 'ol/proj';
import { Draw, Modify, Snap } from 'ol/interaction';
import { getLength, getArea } from 'ol/sphere';
import 'ol/ol.css';

const GISApp = () => {
    const mapRef = useRef();
    const [map, setMap] = useState(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [activeTool, setActiveTool] = useState(null);
    const [measurements, setMeasurements] = useState([]);
    const [equipment, setEquipment] = useState([]);
    const [selectedEquipment, setSelectedEquipment] = useState(null);
    const [visibilityAnalysis, setVisibilityAnalysis] = useState(null);

    // Données d'équipements de test
    const testEquipment = [
        {
            id: "1",
            name: "Station COMINT Alpha",
            type: "comint",
            longitude: -7.6,
            latitude: 33.6,
            status: "operational",
            properties: {
                frequency_range: "30-3000 MHz",
                power: "100W",
                description: "Station d'interception COMINT principale"
            }
        },
        {
            id: "2",
            name: "Capteur ELINT Beta", 
            type: "elint",
            longitude: -7.5,
            latitude: 33.7,
            status: "operational",
            properties: {
                frequency_range: "1-18 GHz",
                sensitivity: "-80 dBm",
                description: "Capteur d'analyse ELINT haute fréquence"
            }
        },
        {
            id: "3",
            name: "Système Anti-Drone Gamma",
            type: "anti-drone",
            longitude: -7.4,
            latitude: 33.5,
            status: "maintenance",
            properties: {
                range: "5 km",
                detection_types: ["RF", "Radar"],
                description: "Système de détection et neutralisation de drones"
            }
        },
        {
            id: "4",
            name: "Brouilleur Delta",
            type: "jammer",
            longitude: -7.7,
            latitude: 33.8,
            status: "operational",
            properties: {
                power: "50W",
                bands: ["2.4GHz", "5.8GHz"],
                description: "Brouilleur multi-bandes"
            }
        },
        {
            id: "5",
            name: "Capteur Epsilon",
            type: "sensor",
            longitude: -7.3,
            latitude: 33.4,
            status: "operational",
            properties: {
                type: "acoustic",
                range: "2 km",
                description: "Capteur acoustique de surveillance"
            }
        }
    ];

    // Styles pour les équipements
    const getEquipmentStyle = (equipment) => {
        const colors = {
            comint: '#2196F3',
            elint: '#FF9800',
            'anti-drone': '#F44336',
            jammer: '#9C27B0',
            sensor: '#4CAF50'
        };

        const color = colors[equipment.type] || '#757575';
        const isOperational = equipment.status === 'operational';

        return new Style({
            image: new Circle({
                radius: 12,
                fill: new Fill({
                    color: isOperational ? color : '#BDBDBD'
                }),
                stroke: new Stroke({
                    color: '#FFFFFF',
                    width: 3
                })
            }),
            text: new Text({
                text: equipment.name,
                font: 'bold 12px Arial',
                fill: new Fill({ color: '#000000' }),
                stroke: new Stroke({ color: '#FFFFFF', width: 3 }),
                offsetY: -25
            })
        });
    };

    // Connexion
    const handleLogin = async (e) => {
        e.preventDefault();
        if (username === 'admin' && password === 'admin123') {
            setIsAuthenticated(true);
            setEquipment(testEquipment);
        } else {
            alert('Identifiants incorrects');
        }
    };

    // Initialisation de la carte
    useEffect(() => {
        if (!mapRef.current || !isAuthenticated) return;

        // Couches
        const osmLayer = new TileLayer({
            source: new OSM()
        });

        const equipmentSource = new VectorSource();
        const equipmentLayer = new VectorLayer({
            source: equipmentSource,
            style: (feature) => getEquipmentStyle(feature.get('equipment'))
        });

        const measurementSource = new VectorSource();
        const measurementLayer = new VectorLayer({
            source: measurementSource,
            style: new Style({
                fill: new Fill({ color: 'rgba(33, 150, 243, 0.2)' }),
                stroke: new Stroke({ color: '#2196F3', width: 2 }),
                image: new Circle({
                    radius: 5,
                    fill: new Fill({ color: '#2196F3' }),
                    stroke: new Stroke({ color: '#fff', width: 2 })
                })
            })
        });

        // Création de la carte
        const newMap = new Map({
            target: mapRef.current,
            layers: [osmLayer, measurementLayer, equipmentLayer],
            view: new View({
                center: fromLonLat([-7.6, 33.6]),
                zoom: 8
            })
        });

        // Ajouter les équipements
        equipment.forEach(eq => {
            const feature = new Feature({
                geometry: new Point(fromLonLat([eq.longitude, eq.latitude])),
                equipment: eq
            });
            equipmentSource.addFeature(feature);
        });

        // Gestionnaire de clic
        newMap.on('click', (event) => {
            const features = newMap.getFeaturesAtPixel(event.pixel);
            if (features.length > 0) {
                const equipment = features[0].get('equipment');
                if (equipment) {
                    setSelectedEquipment(equipment);
                }
            }
        });

        setMap(newMap);

        return () => {
            newMap.setTarget(null);
        };
    }, [isAuthenticated, equipment]);

    // Outils de mesure
    const startMeasurement = (type) => {
        if (!map) return;

        // Nettoyer les interactions précédentes
        map.getInteractions().forEach(interaction => {
            if (interaction instanceof Draw) {
                map.removeInteraction(interaction);
            }
        });

        const source = map.getLayers().getArray()[1].getSource();
        
        let geometryType;
        switch (type) {
            case 'distance':
                geometryType = 'LineString';
                break;
            case 'area':
                geometryType = 'Polygon';
                break;
            default:
                return;
        }

        const draw = new Draw({
            source: source,
            type: geometryType
        });

        draw.on('drawend', (event) => {
            const geometry = event.feature.getGeometry();
            let measurement;

            if (type === 'distance') {
                const length = getLength(geometry);
                measurement = {
                    type: 'distance',
                    value: length,
                    text: length > 1000 ? `${(length / 1000).toFixed(2)} km` : `${length.toFixed(2)} m`
                };
            } else if (type === 'area') {
                const area = getArea(geometry);
                measurement = {
                    type: 'area',
                    value: area,
                    text: area > 1000000 ? `${(area / 1000000).toFixed(2)} km²` : `${area.toFixed(2)} m²`
                };
            }

            setMeasurements(prev => [...prev, measurement]);
            map.removeInteraction(draw);
            setActiveTool(null);
        });

        map.addInteraction(draw);
        setActiveTool(type);
    };

    // Analyse de visibilité
    const startVisibilityAnalysis = () => {
        if (!map) return;

        const draw = new Draw({
            source: new VectorSource(),
            type: 'Point'
        });

        draw.on('drawend', (event) => {
            const coordinate = event.feature.getGeometry().getCoordinates();
            const lonLat = toLonLat(coordinate);
            
            // Simulation d'analyse de visibilité
            const analysis = {
                observer: lonLat,
                visibleArea: 75.5,
                maxDistance: 5000,
                timestamp: new Date().toLocaleString()
            };

            setVisibilityAnalysis(analysis);
            map.removeInteraction(draw);
        });

        map.addInteraction(draw);
    };

    // Page de connexion
    if (!isAuthenticated) {
        return (
            <div style={{
                minHeight: '100vh',
                background: 'linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontFamily: 'Arial, sans-serif'
            }}>
                <div style={{
                    background: 'white',
                    padding: '2rem',
                    borderRadius: '8px',
                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                    width: '100%',
                    maxWidth: '400px'
                }}>
                    <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                        <h1 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '0.5rem' }}>
                            C2-EW Platform
                        </h1>
                        <p style={{ color: '#6b7280' }}>Module SIG Avancé</p>
                        <p style={{ color: '#2563eb', fontSize: '0.875rem' }}>🇲🇦 Territoire Marocain</p>
                    </div>

                    <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                        <div>
                            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                                Nom d'utilisateur
                            </label>
                            <input
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '0.5rem 0.75rem',
                                    border: '1px solid #d1d5db',
                                    borderRadius: '0.375rem',
                                    fontSize: '1rem'
                                }}
                                placeholder="admin"
                                required
                            />
                        </div>

                        <div>
                            <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>
                                Mot de passe
                            </label>
                            <input
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '0.5rem 0.75rem',
                                    border: '1px solid #d1d5db',
                                    borderRadius: '0.375rem',
                                    fontSize: '1rem'
                                }}
                                placeholder="admin123"
                                required
                            />
                        </div>

                        <button
                            type="submit"
                            style={{
                                width: '100%',
                                background: '#2563eb',
                                color: 'white',
                                padding: '0.5rem 1rem',
                                borderRadius: '0.375rem',
                                border: 'none',
                                fontSize: '1rem',
                                cursor: 'pointer'
                            }}
                        >
                            Se connecter
                        </button>
                    </form>

                    <div style={{ marginTop: '1.5rem', textAlign: 'center', fontSize: '0.875rem', color: '#6b7280' }}>
                        <p>Compte de test :</p>
                        <div style={{ marginTop: '0.5rem' }}>
                            <strong>admin</strong> / admin123
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Interface principale
    return (
        <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', fontFamily: 'Arial, sans-serif' }}>
            {/* Header */}
            <header style={{
                background: '#1e40af',
                color: 'white',
                padding: '1rem',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                    <h1 style={{ fontSize: '1.25rem', fontWeight: 'bold', margin: 0 }}>C2-EW Platform</h1>
                    <span style={{ color: '#bfdbfe' }}>|</span>
                    <span style={{ color: '#bfdbfe' }}>Module SIG Avancé</span>
                    <span style={{
                        fontSize: '0.75rem',
                        background: '#10b981',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '0.25rem'
                    }}>
                        ACTIF
                    </span>
                </div>
                
                <button
                    onClick={() => setIsAuthenticated(false)}
                    style={{
                        background: '#1d4ed8',
                        color: 'white',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '0.25rem',
                        cursor: 'pointer'
                    }}
                >
                    Déconnexion
                </button>
            </header>

            {/* Contenu principal */}
            <div style={{ flex: 1, display: 'flex' }}>
                {/* Sidebar gauche - Outils */}
                <div style={{
                    width: '300px',
                    background: '#f8fafc',
                    borderRight: '1px solid #e5e7eb',
                    padding: '1rem',
                    overflowY: 'auto'
                }}>
                    <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', marginBottom: '1rem', color: '#1f2937' }}>
                        🛠️ Outils SIG
                    </h3>

                    {/* Outils de mesure */}
                    <div style={{ marginBottom: '1.5rem' }}>
                        <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem', color: '#374151' }}>
                            📏 Mesures
                        </h4>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                            <button
                                onClick={() => startMeasurement('distance')}
                                style={{
                                    padding: '0.5rem',
                                    background: activeTool === 'distance' ? '#3b82f6' : '#e5e7eb',
                                    color: activeTool === 'distance' ? 'white' : '#374151',
                                    border: 'none',
                                    borderRadius: '0.25rem',
                                    cursor: 'pointer'
                                }}
                            >
                                📐 Mesurer Distance
                            </button>
                            <button
                                onClick={() => startMeasurement('area')}
                                style={{
                                    padding: '0.5rem',
                                    background: activeTool === 'area' ? '#3b82f6' : '#e5e7eb',
                                    color: activeTool === 'area' ? 'white' : '#374151',
                                    border: 'none',
                                    borderRadius: '0.25rem',
                                    cursor: 'pointer'
                                }}
                            >
                                📐 Mesurer Surface
                            </button>
                        </div>
                    </div>

                    {/* Analyse de visibilité */}
                    <div style={{ marginBottom: '1.5rem' }}>
                        <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem', color: '#374151' }}>
                            👁️ Analyse
                        </h4>
                        <button
                            onClick={startVisibilityAnalysis}
                            style={{
                                width: '100%',
                                padding: '0.5rem',
                                background: '#10b981',
                                color: 'white',
                                border: 'none',
                                borderRadius: '0.25rem',
                                cursor: 'pointer'
                            }}
                        >
                            🔍 Analyse de Visibilité
                        </button>
                    </div>

                    {/* Résultats des mesures */}
                    {measurements.length > 0 && (
                        <div style={{ marginBottom: '1.5rem' }}>
                            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem', color: '#374151' }}>
                                📊 Résultats
                            </h4>
                            <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
                                {measurements.map((measurement, index) => (
                                    <div key={index} style={{
                                        padding: '0.5rem',
                                        background: 'white',
                                        border: '1px solid #e5e7eb',
                                        borderRadius: '0.25rem',
                                        marginBottom: '0.25rem',
                                        fontSize: '0.875rem'
                                    }}>
                                        <strong>{measurement.type === 'distance' ? '📏' : '📐'}</strong> {measurement.text}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Analyse de visibilité */}
                    {visibilityAnalysis && (
                        <div style={{ marginBottom: '1.5rem' }}>
                            <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem', color: '#374151' }}>
                                👁️ Visibilité
                            </h4>
                            <div style={{
                                padding: '0.5rem',
                                background: 'white',
                                border: '1px solid #e5e7eb',
                                borderRadius: '0.25rem',
                                fontSize: '0.875rem'
                            }}>
                                <div><strong>Zone visible:</strong> {visibilityAnalysis.visibleArea}%</div>
                                <div><strong>Distance max:</strong> {visibilityAnalysis.maxDistance}m</div>
                                <div><strong>Calculé:</strong> {visibilityAnalysis.timestamp}</div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Carte principale */}
                <div style={{ flex: 1, position: 'relative' }}>
                    <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
                    
                    {/* Légende des équipements */}
                    <div style={{
                        position: 'absolute',
                        top: '1rem',
                        right: '1rem',
                        background: 'white',
                        padding: '1rem',
                        borderRadius: '0.5rem',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                        maxWidth: '250px'
                    }}>
                        <h3 style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                            🛡️ Équipements C2-EW
                        </h3>
                        <div style={{ fontSize: '0.875rem' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.25rem' }}>
                                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#2196F3', marginRight: '0.5rem' }}></div>
                                <span>📡 COMINT</span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.25rem' }}>
                                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#FF9800', marginRight: '0.5rem' }}></div>
                                <span>📊 ELINT</span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.25rem' }}>
                                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#F44336', marginRight: '0.5rem' }}></div>
                                <span>🛡️ Anti-Drone</span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.25rem' }}>
                                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#9C27B0', marginRight: '0.5rem' }}></div>
                                <span>📵 Brouilleur</span>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#4CAF50', marginRight: '0.5rem' }}></div>
                                <span>🔍 Capteur</span>
                            </div>
                        </div>
                        <div style={{ marginTop: '0.5rem', paddingTop: '0.5rem', borderTop: '1px solid #e5e7eb', fontSize: '0.75rem', color: '#6b7280' }}>
                            Total: {equipment.length} équipements
                        </div>
                    </div>
                </div>

                {/* Sidebar droite - Détails équipement */}
                {selectedEquipment && (
                    <div style={{
                        width: '300px',
                        background: '#f8fafc',
                        borderLeft: '1px solid #e5e7eb',
                        padding: '1rem',
                        overflowY: 'auto'
                    }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                            <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#1f2937' }}>
                                📋 Détails Équipement
                            </h3>
                            <button
                                onClick={() => setSelectedEquipment(null)}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    fontSize: '1.5rem',
                                    cursor: 'pointer',
                                    color: '#6b7280'
                                }}
                            >
                                ×
                            </button>
                        </div>

                        <div style={{ background: 'white', padding: '1rem', borderRadius: '0.5rem', border: '1px solid #e5e7eb' }}>
                            <h4 style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '0.5rem', color: '#1f2937' }}>
                                {selectedEquipment.name}
                            </h4>
                            
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Type:</strong> {selectedEquipment.type.toUpperCase()}
                            </div>
                            
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Statut:</strong> 
                                <span style={{
                                    marginLeft: '0.5rem',
                                    padding: '0.25rem 0.5rem',
                                    borderRadius: '0.25rem',
                                    fontSize: '0.75rem',
                                    background: selectedEquipment.status === 'operational' ? '#dcfce7' : '#fef3c7',
                                    color: selectedEquipment.status === 'operational' ? '#166534' : '#92400e'
                                }}>
                                    {selectedEquipment.status === 'operational' ? '✅ Opérationnel' : '⚠️ Maintenance'}
                                </span>
                            </div>
                            
                            <div style={{ marginBottom: '0.5rem' }}>
                                <strong>Position:</strong> {selectedEquipment.latitude.toFixed(4)}°N, {selectedEquipment.longitude.toFixed(4)}°E
                            </div>

                            <div style={{ marginTop: '1rem' }}>
                                <strong>Propriétés:</strong>
                                <div style={{ marginTop: '0.5rem', fontSize: '0.875rem' }}>
                                    {Object.entries(selectedEquipment.properties).map(([key, value]) => (
                                        <div key={key} style={{ marginBottom: '0.25rem' }}>
                                            <strong>{key.replace('_', ' ')}:</strong> {Array.isArray(value) ? value.join(', ') : value}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Footer */}
            <footer style={{
                background: '#1f2937',
                color: 'white',
                padding: '0.5rem',
                textAlign: 'center',
                fontSize: '0.875rem'
            }}>
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '1.5rem' }}>
                    <span>🗺️ Module SIG C2-EW v2.0.0</span>
                    <span>🇲🇦 Territoire: Maroc</span>
                    <span>📡 OpenLayers 8+</span>
                    <span>⚡ Temps réel</span>
                </div>
            </footer>
        </div>
    );
};

export default GISApp;
