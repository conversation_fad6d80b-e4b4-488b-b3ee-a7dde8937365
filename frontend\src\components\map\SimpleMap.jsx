/**
 * Carte simple pour C2-EW
 * Version simplifiée sans dépendances complexes
 */

import React, { useEffect, useRef, useState } from 'react';
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { Style, Circle, Fill, Stroke, Text } from 'ol/style';
import { fromLonLat } from 'ol/proj';
import 'ol/ol.css';

const SimpleMap = () => {
    const mapRef = useRef();
    const [map, setMap] = useState(null);
    const [equipment, setEquipment] = useState([]);

    // Données d'équipements de test
    const testEquipment = [
        {
            id: "1",
            name: "Station COMINT Alpha",
            type: "comint",
            longitude: -7.6,
            latitude: 33.6,
            status: "operational"
        },
        {
            id: "2",
            name: "Capteur ELINT Beta", 
            type: "elint",
            longitude: -7.5,
            latitude: 33.7,
            status: "operational"
        },
        {
            id: "3",
            name: "Système Anti-Drone Gamma",
            type: "anti-drone",
            longitude: -7.4,
            latitude: 33.5,
            status: "maintenance"
        },
        {
            id: "4",
            name: "Brouilleur Delta",
            type: "jammer",
            longitude: -7.7,
            latitude: 33.8,
            status: "operational"
        },
        {
            id: "5",
            name: "Capteur Epsilon",
            type: "sensor",
            longitude: -7.3,
            latitude: 33.4,
            status: "operational"
        }
    ];

    // Styles pour les différents types d'équipements
    const getEquipmentStyle = (equipment) => {
        const colors = {
            comint: '#2196F3',      // Bleu
            elint: '#FF9800',       // Orange
            'anti-drone': '#F44336', // Rouge
            jammer: '#9C27B0',      // Violet
            sensor: '#4CAF50'       // Vert
        };

        const color = colors[equipment.type] || '#757575';
        const isOperational = equipment.status === 'operational';

        return new Style({
            image: new Circle({
                radius: 8,
                fill: new Fill({
                    color: isOperational ? color : '#BDBDBD'
                }),
                stroke: new Stroke({
                    color: '#FFFFFF',
                    width: 2
                })
            }),
            text: new Text({
                text: equipment.name,
                font: '12px Arial',
                fill: new Fill({ color: '#000000' }),
                stroke: new Stroke({ color: '#FFFFFF', width: 2 }),
                offsetY: -20
            })
        });
    };

    // Initialisation de la carte
    useEffect(() => {
        if (!mapRef.current) return;

        // Couche de base OpenStreetMap
        const osmLayer = new TileLayer({
            source: new OSM()
        });

        // Couche vectorielle pour les équipements
        const equipmentSource = new VectorSource();
        const equipmentLayer = new VectorLayer({
            source: equipmentSource,
            style: (feature) => getEquipmentStyle(feature.get('equipment'))
        });

        // Création de la carte
        const newMap = new Map({
            target: mapRef.current,
            layers: [osmLayer, equipmentLayer],
            view: new View({
                center: fromLonLat([-7.6, 33.6]), // Casablanca
                zoom: 8
            })
        });

        setMap(newMap);

        // Ajouter les équipements de test
        testEquipment.forEach(eq => {
            const feature = new Feature({
                geometry: new Point(fromLonLat([eq.longitude, eq.latitude])),
                equipment: eq
            });
            equipmentSource.addFeature(feature);
        });

        setEquipment(testEquipment);

        // Gestionnaire de clic
        newMap.on('click', (event) => {
            const features = newMap.getFeaturesAtPixel(event.pixel);
            if (features.length > 0) {
                const equipment = features[0].get('equipment');
                if (equipment) {
                    alert(`Équipement: ${equipment.name}\nType: ${equipment.type}\nStatut: ${equipment.status}`);
                }
            }
        });

        // Nettoyage
        return () => {
            newMap.setTarget(null);
        };
    }, []);

    // Charger les équipements depuis l'API
    const loadEquipmentFromAPI = async () => {
        try {
            // Pour l'instant, on utilise les données de test
            // Plus tard, on pourra faire un appel à l'API
            console.log('Équipements chargés:', equipment);
        } catch (error) {
            console.error('Erreur chargement équipements:', error);
        }
    };

    useEffect(() => {
        loadEquipmentFromAPI();
    }, []);

    return (
        <div className="relative w-full h-full">
            {/* Carte */}
            <div 
                ref={mapRef} 
                className="w-full h-full"
                style={{ minHeight: '500px' }}
            />
            
            {/* Légende */}
            <div className="absolute top-4 right-4 bg-white p-4 rounded-lg shadow-lg max-w-xs">
                <h3 className="font-bold mb-2">Équipements C2-EW</h3>
                <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                        <span>COMINT</span>
                    </div>
                    <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-orange-500 mr-2"></div>
                        <span>ELINT</span>
                    </div>
                    <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                        <span>Anti-Drone</span>
                    </div>
                    <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-purple-500 mr-2"></div>
                        <span>Brouilleur</span>
                    </div>
                    <div className="flex items-center">
                        <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                        <span>Capteur</span>
                    </div>
                </div>
                <div className="mt-2 pt-2 border-t text-xs text-gray-600">
                    Total: {equipment.length} équipements
                </div>
            </div>

            {/* Contrôles */}
            <div className="absolute bottom-4 left-4 bg-white p-2 rounded-lg shadow-lg">
                <div className="flex space-x-2">
                    <button 
                        onClick={() => map?.getView().setZoom(map.getView().getZoom() + 1)}
                        className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        +
                    </button>
                    <button 
                        onClick={() => map?.getView().setZoom(map.getView().getZoom() - 1)}
                        className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        -
                    </button>
                    <button 
                        onClick={() => {
                            map?.getView().setCenter(fromLonLat([-7.6, 33.6]));
                            map?.getView().setZoom(8);
                        }}
                        className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                    >
                        🏠
                    </button>
                </div>
            </div>

            {/* Informations */}
            <div className="absolute top-4 left-4 bg-white p-3 rounded-lg shadow-lg">
                <h2 className="font-bold text-lg text-blue-600">C2-EW Platform</h2>
                <p className="text-sm text-gray-600">Module SIG - Maroc</p>
                <div className="mt-2 text-xs">
                    <div>🌍 Projection: Web Mercator</div>
                    <div>📍 Centre: Casablanca</div>
                    <div>🔗 API: {window.location.protocol}//localhost:8000</div>
                </div>
            </div>
        </div>
    );
};

export default SimpleMap;
