import Integer from '../../../../../java/lang/Integer'
import ArrayList from '../../../../../java/util/ArrayList'
import Quadrant from '../Quadrant'

export default class MonotoneChainIndexer {
  getChainStartIndices (pts) {
    let start = 0
    const startIndexList = new ArrayList()
    startIndexList.add(new Integer(start))
    do {
      const last = this.findChainEnd(pts, start)
      startIndexList.add(new Integer(last))
      start = last
    } while (start < pts.length - 1)
    const startIndex = MonotoneChainIndexer.toIntArray(startIndexList)
    return startIndex
  }
  findChainEnd (pts, start) {
    const chainQuad = Quadrant.quadrant(pts[start], pts[start + 1])
    let last = start + 1
    while (last < pts.length) {
      const quad = Quadrant.quadrant(pts[last - 1], pts[last])
      if (quad !== chainQuad) break
      last++
    }
    return last - 1
  }
  interfaces_ () {
    return []
  }
  getClass () {
    return MonotoneChainIndexer
  }
  static toIntArray (list) {
    const array = new Array(list.size()).fill(null)
    for (let i = 0; i < array.length; i++) {
      array[i] = list.get(i).intValue()
    }
    return array
  }
}
