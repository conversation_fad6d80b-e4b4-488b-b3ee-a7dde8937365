/**
 * Gestionnaire de projections cartographiques
 * Module cartographique C2-EW - Support multi-projections
 */

import { get as getProjection, transform, transformExtent } from 'ol/proj';
import { register } from 'ol/proj/proj4';
import proj4 from 'proj4';
import { EventEmitter } from 'events';

/**
 * Gestionnaire de projections pour le SIG
 */
export class ProjectionManager extends EventEmitter {
    constructor(gisEngine) {
        super();
        
        this.gisEngine = gisEngine;
        this.map = gisEngine.getMap();
        this.currentProjection = 'EPSG:3857'; // Web Mercator par défaut
        this.availableProjections = new Map();
        
        this.init();
    }
    
    /**
     * Initialisation du gestionnaire de projections
     */
    init() {
        this.registerMoroccanProjections();
        this.registerCommonProjections();
        this.setupEventHandlers();
        
        console.log('✅ Gestionnaire de projections initialisé');
    }
    
    /**
     * Enregistrement des projections marocaines
     */
    registerMoroccanProjections() {
        // Lambert Conformal Conic Maroc (système officiel)
        proj4.defs('EPSG:26191', 
            '+proj=lcc +lat_1=33.3 +lat_0=33.3 +lon_0=-5.4 +k_0=0.999625769 ' +
            '+x_0=500000 +y_0=300000 +ellps=clrk80 ' +
            '+towgs84=31,146,47,0,0,0,0 +units=m +no_defs'
        );
        
        // UTM Zone 29N (Maroc occidental)
        proj4.defs('EPSG:32629',
            '+proj=utm +zone=29 +datum=WGS84 +units=m +no_defs'
        );
        
        // UTM Zone 30N (Maroc central)
        proj4.defs('EPSG:32630',
            '+proj=utm +zone=30 +datum=WGS84 +units=m +no_defs'
        );
        
        // UTM Zone 28N (Sahara occidental)
        proj4.defs('EPSG:32628',
            '+proj=utm +zone=28 +datum=WGS84 +units=m +no_defs'
        );
        
        // Mercator Maroc (projection locale)
        proj4.defs('EPSG:26192',
            '+proj=merc +lon_0=-6 +k=1 +x_0=500000 +y_0=300000 ' +
            '+ellps=clrk80 +towgs84=31,146,47,0,0,0,0 +units=m +no_defs'
        );
        
        // Enregistrer avec OpenLayers
        register(proj4);
        
        // Ajouter à la liste des projections disponibles
        this.availableProjections.set('EPSG:4326', {
            code: 'EPSG:4326',
            name: 'WGS84 (Géographique)',
            description: 'Système géodésique mondial - Latitude/Longitude',
            units: 'degrees',
            extent: [-180, -90, 180, 90],
            suitable: ['global', 'navigation']
        });
        
        this.availableProjections.set('EPSG:3857', {
            code: 'EPSG:3857',
            name: 'Web Mercator',
            description: 'Projection Mercator pour applications web',
            units: 'meters',
            extent: [-20037508.34, -20048966.1, 20037508.34, 20048966.1],
            suitable: ['web', 'visualization']
        });
        
        this.availableProjections.set('EPSG:26191', {
            code: 'EPSG:26191',
            name: 'Lambert Conformal Conic Maroc',
            description: 'Projection officielle du Royaume du Maroc',
            units: 'meters',
            extent: [-1200000, -800000, 1200000, 1600000],
            suitable: ['morocco', 'official', 'precise']
        });
        
        this.availableProjections.set('EPSG:32629', {
            code: 'EPSG:32629',
            name: 'UTM Zone 29N',
            description: 'Universal Transverse Mercator - Zone 29 Nord (Maroc occidental)',
            units: 'meters',
            extent: [166021.44, 0.00, 833978.56, 9329005.18],
            suitable: ['morocco_west', 'engineering', 'precise']
        });
        
        this.availableProjections.set('EPSG:32630', {
            code: 'EPSG:32630',
            name: 'UTM Zone 30N',
            description: 'Universal Transverse Mercator - Zone 30 Nord (Maroc central)',
            units: 'meters',
            extent: [166021.44, 0.00, 833978.56, 9329005.18],
            suitable: ['morocco_central', 'engineering', 'precise']
        });
        
        this.availableProjections.set('EPSG:32628', {
            code: 'EPSG:32628',
            name: 'UTM Zone 28N',
            description: 'Universal Transverse Mercator - Zone 28 Nord (Sahara)',
            units: 'meters',
            extent: [166021.44, 0.00, 833978.56, 9329005.18],
            suitable: ['sahara', 'engineering', 'precise']
        });
    }
    
    /**
     * Enregistrement des projections communes
     */
    registerCommonProjections() {
        // Projection polaire stéréographique
        proj4.defs('EPSG:3413',
            '+proj=stere +lat_0=90 +lat_ts=70 +lon_0=-45 +k=1 ' +
            '+x_0=0 +y_0=0 +datum=WGS84 +units=m +no_defs'
        );
        
        register(proj4);
    }
    
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        // Écouter les changements de vue
        this.map.getView().on('change:projection', () => {
            const newProjection = this.map.getView().getProjection().getCode();
            this.currentProjection = newProjection;
            this.emit('projectionChanged', { projection: newProjection });
        });
    }
    
    /**
     * Changer la projection de la carte
     */
    changeProjection(projectionCode) {
        try {
            const projection = getProjection(projectionCode);
            if (!projection) {
                throw new Error(`Projection ${projectionCode} non trouvée`);
            }
            
            const view = this.map.getView();
            const currentCenter = view.getCenter();
            const currentZoom = view.getZoom();
            const currentExtent = view.calculateExtent(this.map.getSize());
            
            // Transformer le centre vers la nouvelle projection
            const newCenter = transform(
                currentCenter,
                this.currentProjection,
                projectionCode
            );
            
            // Créer une nouvelle vue avec la nouvelle projection
            const newView = new ol.View({
                projection: projection,
                center: newCenter,
                zoom: currentZoom,
                minZoom: 4,
                maxZoom: 20
            });
            
            // Appliquer la nouvelle vue
            this.map.setView(newView);
            this.currentProjection = projectionCode;
            
            // Adapter l'emprise si c'est une projection marocaine
            if (this.isMoroccanProjection(projectionCode)) {
                this.fitToMorocco();
            }
            
            this.emit('projectionChanged', {
                oldProjection: this.currentProjection,
                newProjection: projectionCode,
                center: newCenter,
                zoom: currentZoom
            });
            
            console.log(`✅ Projection changée vers ${projectionCode}`);
            
        } catch (error) {
            console.error('❌ Erreur lors du changement de projection:', error);
            this.emit('projectionError', { error, projectionCode });
        }
    }
    
    /**
     * Vérifier si c'est une projection marocaine
     */
    isMoroccanProjection(projectionCode) {
        const moroccanProjections = [
            'EPSG:26191', 'EPSG:26192', 
            'EPSG:32628', 'EPSG:32629', 'EPSG:32630'
        ];
        return moroccanProjections.includes(projectionCode);
    }
    
    /**
     * Ajuster la vue sur le territoire marocain
     */
    fitToMorocco() {
        // Emprise du Royaume du Maroc en WGS84
        const moroccoExtentWGS84 = [-17.0, 20.5, -1.0, 36.0];
        
        // Transformer vers la projection actuelle
        const moroccoExtent = transformExtent(
            moroccoExtentWGS84,
            'EPSG:4326',
            this.currentProjection
        );
        
        this.map.getView().fit(moroccoExtent, {
            padding: [50, 50, 50, 50],
            maxZoom: 8
        });
    }
    
    /**
     * Transformer des coordonnées
     */
    transformCoordinates(coordinates, fromProjection, toProjection) {
        return transform(coordinates, fromProjection, toProjection);
    }
    
    /**
     * Transformer une emprise
     */
    transformExtent(extent, fromProjection, toProjection) {
        return transformExtent(extent, fromProjection, toProjection);
    }
    
    /**
     * Obtenir la projection recommandée pour une zone
     */
    getRecommendedProjection(extent) {
        // Convertir l'emprise en WGS84 si nécessaire
        let wgs84Extent = extent;
        if (this.currentProjection !== 'EPSG:4326') {
            wgs84Extent = this.transformExtent(extent, this.currentProjection, 'EPSG:4326');
        }
        
        const [west, south, east, north] = wgs84Extent;
        const centerLon = (west + east) / 2;
        
        // Recommandations basées sur la longitude centrale
        if (centerLon >= -12 && centerLon <= -6) {
            // Zone du Maroc occidental
            return 'EPSG:32629'; // UTM 29N
        } else if (centerLon >= -6 && centerLon <= 0) {
            // Zone du Maroc central/oriental
            return 'EPSG:32630'; // UTM 30N
        } else if (centerLon >= -18 && centerLon <= -12) {
            // Zone du Sahara
            return 'EPSG:32628'; // UTM 28N
        } else {
            // Zone étendue - utiliser Lambert Conformal Conic
            return 'EPSG:26191';
        }
    }
    
    /**
     * Obtenir les projections disponibles
     */
    getAvailableProjections() {
        return Array.from(this.availableProjections.values());
    }
    
    /**
     * Obtenir les projections adaptées à un usage
     */
    getProjectionsForUse(usage) {
        return this.getAvailableProjections().filter(proj => 
            proj.suitable.includes(usage)
        );
    }
    
    /**
     * Obtenir la projection actuelle
     */
    getCurrentProjection() {
        return this.currentProjection;
    }
    
    /**
     * Obtenir les informations d'une projection
     */
    getProjectionInfo(projectionCode) {
        return this.availableProjections.get(projectionCode);
    }
    
    /**
     * Calculer la distorsion pour une zone
     */
    calculateDistortion(extent, projectionCode) {
        // Calcul simplifié de la distorsion
        const projInfo = this.getProjectionInfo(projectionCode);
        if (!projInfo) return null;
        
        // Convertir l'emprise en WGS84
        let wgs84Extent = extent;
        if (this.currentProjection !== 'EPSG:4326') {
            wgs84Extent = this.transformExtent(extent, this.currentProjection, 'EPSG:4326');
        }
        
        const [west, south, east, north] = wgs84Extent;
        const centerLat = (south + north) / 2;
        const centerLon = (west + east) / 2;
        
        // Facteurs de distorsion approximatifs
        let distortion = {
            scale: 1.0,
            angular: 0.0,
            areal: 1.0
        };
        
        switch (projectionCode) {
            case 'EPSG:3857': // Web Mercator
                distortion.scale = 1 / Math.cos(centerLat * Math.PI / 180);
                distortion.areal = distortion.scale * distortion.scale;
                break;
                
            case 'EPSG:26191': // Lambert Conformal Conic Maroc
                // Distorsion minimale au centre du Maroc
                const distanceFromCenter = Math.abs(centerLat - 33.3);
                distortion.scale = 1 + (distanceFromCenter * 0.001);
                distortion.areal = distortion.scale * distortion.scale;
                break;
                
            case 'EPSG:32629':
            case 'EPSG:32630':
            case 'EPSG:32628': // UTM
                // Distorsion UTM basée sur la distance du méridien central
                const utmZone = projectionCode === 'EPSG:32628' ? 28 : 
                               projectionCode === 'EPSG:32629' ? 29 : 30;
                const centralMeridian = (utmZone - 30) * 6 - 3;
                const distanceFromMeridian = Math.abs(centerLon - centralMeridian);
                distortion.scale = 1 + (distanceFromMeridian * 0.0001);
                distortion.areal = distortion.scale * distortion.scale;
                break;
        }
        
        return distortion;
    }
    
    /**
     * Destruction du gestionnaire
     */
    destroy() {
        this.removeAllListeners();
        console.log('🗑️ Gestionnaire de projections détruit');
    }
}

export default ProjectionManager;
