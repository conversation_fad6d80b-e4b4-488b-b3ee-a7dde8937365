-- ===================================
-- Schéma SpatiaLite pour Cache Local
-- Module SIG C2-EW - Fonctionnement Hors Ligne
-- ===================================

-- Initialisation SpatiaLite
SELECT InitSpatialMetadata(1);

-- Activation des extensions spatiales
SELECT load_extension('mod_spatialite');

-- ===================================
-- Tables de Cache Local
-- ===================================

-- Table de cache des couches spatiales
CREATE TABLE IF NOT EXISTS cache_spatial_layers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    layer_type TEXT NOT NULL, -- vector, raster, mbtiles
    data_source TEXT NOT NULL,
    
    -- Propriétés spatiales
    srid INTEGER DEFAULT 4326,
    geometry_type TEXT, -- POINT, LINESTRING, POLYGON
    
    -- Propriétés d'affichage
    visible INTEGER DEFAULT 1, -- SQLite boolean as integer
    opacity REAL DEFAULT 1.0,
    z_index INTEGER DEFAULT 0,
    
    -- Style et métadonnées (JSON as TEXT)
    style_config TEXT, -- JSON string
    metadata TEXT, -- JSON string
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    last_accessed TEXT DEFAULT (datetime('now')),
    access_count INTEGER DEFAULT 0,
    
    -- Sync metadata
    source_id TEXT, -- ID from PostGIS
    sync_status TEXT DEFAULT 'cached', -- cached, dirty, conflict
    last_sync TEXT,
    
    CONSTRAINT chk_opacity CHECK (opacity >= 0.0 AND opacity <= 1.0),
    CONSTRAINT chk_visible CHECK (visible IN (0, 1)),
    CONSTRAINT chk_layer_type CHECK (layer_type IN ('vector', 'raster', 'mbtiles'))
);

-- Table de cache des entités spatiales
CREATE TABLE IF NOT EXISTS cache_spatial_features (
    id TEXT PRIMARY KEY,
    layer_id TEXT NOT NULL,
    
    -- Propriétés
    name TEXT,
    feature_type TEXT NOT NULL,
    properties TEXT, -- JSON string
    style TEXT, -- JSON string
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    last_accessed TEXT DEFAULT (datetime('now')),
    access_count INTEGER DEFAULT 0,
    
    -- Sync metadata
    source_id TEXT, -- ID from PostGIS
    sync_status TEXT DEFAULT 'cached',
    last_sync TEXT,
    
    FOREIGN KEY (layer_id) REFERENCES cache_spatial_layers(id) ON DELETE CASCADE
);

-- Ajout de la colonne géométrie pour cache_spatial_features
SELECT AddGeometryColumn('cache_spatial_features', 'geometry', 4326, 'GEOMETRY', 'XY');

-- ===================================
-- Cache du Territoire Marocain
-- ===================================

-- Table de cache pour le territoire marocain
CREATE TABLE IF NOT EXISTS cache_morocco_territory (
    id TEXT PRIMARY KEY,
    
    -- Noms officiels
    region_name TEXT NOT NULL,
    region_name_ar TEXT, -- Nom en arabe
    region_type TEXT NOT NULL, -- mainland, sahara, maritime
    
    -- Hiérarchie administrative
    administrative_level INTEGER DEFAULT 1,
    parent_region_id TEXT,
    
    -- Métadonnées officielles
    official_code TEXT,
    population INTEGER,
    area_km2 REAL,
    
    -- Validation territoriale
    is_official INTEGER DEFAULT 1,
    source_authority TEXT DEFAULT 'Ministère de l''Intérieur',
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    last_accessed TEXT DEFAULT (datetime('now')),
    access_count INTEGER DEFAULT 0,
    
    -- Sync metadata
    source_id TEXT,
    sync_status TEXT DEFAULT 'cached',
    last_sync TEXT,
    
    CONSTRAINT chk_admin_level CHECK (administrative_level BETWEEN 1 AND 3),
    CONSTRAINT chk_region_type CHECK (region_type IN ('mainland', 'sahara', 'maritime')),
    CONSTRAINT chk_is_official CHECK (is_official IN (0, 1)),
    FOREIGN KEY (parent_region_id) REFERENCES cache_morocco_territory(id)
);

-- Ajout de la colonne géométrie pour le territoire marocain
SELECT AddGeometryColumn('cache_morocco_territory', 'geometry', 4326, 'MULTIPOLYGON', 'XY');

-- ===================================
-- Cache MBTiles
-- ===================================

-- Table de cache des datasets MBTiles
CREATE TABLE IF NOT EXISTS cache_mbtiles_datasets (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    
    -- Fichier MBTiles
    file_path TEXT NOT NULL,
    file_size INTEGER DEFAULT 0,
    
    -- Métadonnées MBTiles
    format TEXT DEFAULT 'pbf', -- pbf, png, jpg
    min_zoom INTEGER DEFAULT 0,
    max_zoom INTEGER DEFAULT 18,
    
    -- Métadonnées et statut
    metadata TEXT, -- JSON string
    is_active INTEGER DEFAULT 1,
    last_accessed TEXT,
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    
    -- Sync metadata
    source_id TEXT,
    sync_status TEXT DEFAULT 'cached',
    last_sync TEXT,
    
    CONSTRAINT chk_zoom_range CHECK (min_zoom <= max_zoom),
    CONSTRAINT chk_zoom_values CHECK (min_zoom >= 0 AND max_zoom <= 22),
    CONSTRAINT chk_is_active CHECK (is_active IN (0, 1)),
    CONSTRAINT chk_format CHECK (format IN ('pbf', 'png', 'jpg'))
);

-- Ajout de la colonne géométrie pour l'emprise
SELECT AddGeometryColumn('cache_mbtiles_datasets', 'bounds', 4326, 'POLYGON', 'XY');

-- ===================================
-- Cache des Analyses de Visibilité
-- ===================================

-- Table de cache pour les analyses de visibilité
CREATE TABLE IF NOT EXISTS cache_visibility_analyses (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    
    -- Paramètres d'observation
    observer_height REAL DEFAULT 1.75,
    max_distance REAL DEFAULT 10000.0,
    target_height REAL DEFAULT 0.0,
    earth_curvature INTEGER DEFAULT 1,
    
    -- MNT utilisé
    dem_dataset TEXT,
    dem_resolution REAL,
    
    -- Statistiques
    total_area REAL,
    visible_percentage REAL,
    calculation_time REAL,
    grid_resolution REAL DEFAULT 100.0,
    
    -- Paramètres de calcul
    parameters TEXT, -- JSON string
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    last_accessed TEXT DEFAULT (datetime('now')),
    access_count INTEGER DEFAULT 0,
    
    -- Sync metadata
    source_id TEXT,
    sync_status TEXT DEFAULT 'cached',
    last_sync TEXT,
    
    CONSTRAINT chk_observer_height CHECK (observer_height >= 0),
    CONSTRAINT chk_max_distance CHECK (max_distance > 0),
    CONSTRAINT chk_grid_resolution CHECK (grid_resolution > 0),
    CONSTRAINT chk_earth_curvature CHECK (earth_curvature IN (0, 1))
);

-- Ajout des colonnes géométriques pour l'analyse de visibilité
SELECT AddGeometryColumn('cache_visibility_analyses', 'observer_point', 4326, 'POINT', 'XY');
SELECT AddGeometryColumn('cache_visibility_analyses', 'visible_area', 4326, 'MULTIPOLYGON', 'XY');

-- ===================================
-- Cache des Mesures Spatiales
-- ===================================

-- Table de cache pour les mesures spatiales
CREATE TABLE IF NOT EXISTS cache_spatial_measurements (
    id TEXT PRIMARY KEY,
    name TEXT,
    measurement_type TEXT NOT NULL, -- distance, area, volume
    
    -- Résultats de mesure
    value REAL NOT NULL,
    unit TEXT NOT NULL,
    
    -- Mesures additionnelles
    perimeter REAL,
    area REAL,
    volume REAL,
    
    -- Paramètres de calcul
    projection_used TEXT,
    calculation_method TEXT,
    
    -- Métadonnées
    notes TEXT,
    properties TEXT, -- JSON string
    
    -- Cache metadata
    cached_at TEXT DEFAULT (datetime('now')),
    expires_at TEXT,
    last_accessed TEXT DEFAULT (datetime('now')),
    access_count INTEGER DEFAULT 0,
    
    -- Sync metadata
    source_id TEXT,
    sync_status TEXT DEFAULT 'cached',
    last_sync TEXT,
    
    CONSTRAINT chk_measurement_type CHECK (measurement_type IN ('distance', 'area', 'volume', 'perimeter')),
    CONSTRAINT chk_value CHECK (value >= 0)
);

-- Ajout de la colonne géométrie pour les mesures
SELECT AddGeometryColumn('cache_spatial_measurements', 'geometry', 4326, 'GEOMETRY', 'XY');

-- ===================================
-- Table de Synchronisation
-- ===================================

-- Table des logs de synchronisation locale
CREATE TABLE IF NOT EXISTS sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    record_id TEXT NOT NULL,
    operation TEXT NOT NULL, -- insert, update, delete
    
    -- Données à synchroniser
    data TEXT, -- JSON string
    
    -- Statut
    status TEXT DEFAULT 'pending', -- pending, synced, error
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Timestamps
    created_at TEXT DEFAULT (datetime('now')),
    synced_at TEXT,
    
    CONSTRAINT chk_operation CHECK (operation IN ('insert', 'update', 'delete')),
    CONSTRAINT chk_status CHECK (status IN ('pending', 'synced', 'error'))
);

-- ===================================
-- Métadonnées de Cache
-- ===================================

-- Table des métadonnées de cache global
CREATE TABLE IF NOT EXISTS cache_metadata (
    key TEXT PRIMARY KEY,
    value TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);

-- Insertion des métadonnées initiales
INSERT OR REPLACE INTO cache_metadata (key, value) VALUES 
('cache_version', '1.0'),
('last_full_sync', NULL),
('sync_strategy', 'incremental'),
('max_cache_size_mb', '1000'),
('auto_cleanup_enabled', '1'),
('cleanup_threshold_days', '30');

-- ===================================
-- Index pour Performance
-- ===================================

-- Index pour cache_spatial_layers
CREATE INDEX IF NOT EXISTS idx_cache_layers_name ON cache_spatial_layers(name);
CREATE INDEX IF NOT EXISTS idx_cache_layers_type ON cache_spatial_layers(layer_type);
CREATE INDEX IF NOT EXISTS idx_cache_layers_accessed ON cache_spatial_layers(last_accessed);
CREATE INDEX IF NOT EXISTS idx_cache_layers_sync ON cache_spatial_layers(sync_status);

-- Index pour cache_spatial_features
CREATE INDEX IF NOT EXISTS idx_cache_features_layer ON cache_spatial_features(layer_id);
CREATE INDEX IF NOT EXISTS idx_cache_features_type ON cache_spatial_features(feature_type);
CREATE INDEX IF NOT EXISTS idx_cache_features_accessed ON cache_spatial_features(last_accessed);
CREATE INDEX IF NOT EXISTS idx_cache_features_sync ON cache_spatial_features(sync_status);

-- Index spatiaux (SpatiaLite)
SELECT CreateSpatialIndex('cache_spatial_features', 'geometry');

-- Index pour cache_morocco_territory
CREATE INDEX IF NOT EXISTS idx_cache_morocco_name ON cache_morocco_territory(region_name);
CREATE INDEX IF NOT EXISTS idx_cache_morocco_type ON cache_morocco_territory(region_type);
CREATE INDEX IF NOT EXISTS idx_cache_morocco_level ON cache_morocco_territory(administrative_level);
CREATE INDEX IF NOT EXISTS idx_cache_morocco_parent ON cache_morocco_territory(parent_region_id);
CREATE INDEX IF NOT EXISTS idx_cache_morocco_accessed ON cache_morocco_territory(last_accessed);

-- Index spatial pour territoire marocain
SELECT CreateSpatialIndex('cache_morocco_territory', 'geometry');

-- Index pour cache_mbtiles_datasets
CREATE INDEX IF NOT EXISTS idx_cache_mbtiles_name ON cache_mbtiles_datasets(name);
CREATE INDEX IF NOT EXISTS idx_cache_mbtiles_active ON cache_mbtiles_datasets(is_active);
CREATE INDEX IF NOT EXISTS idx_cache_mbtiles_accessed ON cache_mbtiles_datasets(last_accessed);

-- Index spatial pour MBTiles
SELECT CreateSpatialIndex('cache_mbtiles_datasets', 'bounds');

-- Index pour cache_visibility_analyses
CREATE INDEX IF NOT EXISTS idx_cache_visibility_name ON cache_visibility_analyses(name);
CREATE INDEX IF NOT EXISTS idx_cache_visibility_accessed ON cache_visibility_analyses(last_accessed);

-- Index spatiaux pour analyses de visibilité
SELECT CreateSpatialIndex('cache_visibility_analyses', 'observer_point');
SELECT CreateSpatialIndex('cache_visibility_analyses', 'visible_area');

-- Index pour cache_spatial_measurements
CREATE INDEX IF NOT EXISTS idx_cache_measurements_type ON cache_spatial_measurements(measurement_type);
CREATE INDEX IF NOT EXISTS idx_cache_measurements_accessed ON cache_spatial_measurements(last_accessed);

-- Index spatial pour mesures
SELECT CreateSpatialIndex('cache_spatial_measurements', 'geometry');

-- Index pour sync_queue
CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status);
CREATE INDEX IF NOT EXISTS idx_sync_queue_table ON sync_queue(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_queue_created ON sync_queue(created_at);

-- ===================================
-- Triggers pour Maintenance
-- ===================================

-- Trigger pour mise à jour automatique de last_accessed
CREATE TRIGGER IF NOT EXISTS trigger_update_access_layers
    AFTER SELECT ON cache_spatial_layers
    BEGIN
        UPDATE cache_spatial_layers 
        SET last_accessed = datetime('now'), access_count = access_count + 1
        WHERE id = NEW.id;
    END;

-- Trigger pour nettoyage automatique du cache
CREATE TRIGGER IF NOT EXISTS trigger_cleanup_expired_cache
    AFTER INSERT ON cache_spatial_layers
    BEGIN
        DELETE FROM cache_spatial_layers 
        WHERE expires_at IS NOT NULL AND expires_at < datetime('now');
        
        DELETE FROM cache_spatial_features 
        WHERE expires_at IS NOT NULL AND expires_at < datetime('now');
        
        DELETE FROM cache_morocco_territory 
        WHERE expires_at IS NOT NULL AND expires_at < datetime('now');
    END;
