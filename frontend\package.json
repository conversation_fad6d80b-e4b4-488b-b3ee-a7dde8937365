{"name": "c2-ew-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.81.2", "@turf/turf": "^6.5.0", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lodash": "^4.17.21", "lucide-react": "^0.294.0", "ol": "^8.2.0", "ol-ext": "^4.0.13", "ol-geocoder": "^4.3.2", "ol-layerswitcher": "^4.1.1", "proj4": "^2.9.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "sql.js": "^1.8.0", "tailwind-merge": "^2.0.0", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@types/d3": "^7.4.3", "@types/d3-geo": "^3.1.0", "@types/geojson": "^7946.0.13", "@types/lodash": "^4.14.202", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/topojson": "^3.2.6", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.0", "vitest": "^0.34.6"}}