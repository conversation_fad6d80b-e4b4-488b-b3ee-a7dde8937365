"""Insert Morocco territory official data

Revision ID: 003_morocco_data
Revises: 002_gis_indexes
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text
import uuid

# revision identifiers, used by Alembic.
revision = '003_morocco_data'
down_revision = '002_gis_indexes'
branch_labels = None
depends_on = None


def upgrade():
    """Insert official Morocco territory data"""
    
    # Get connection
    connection = op.get_bind()
    
    # ===================================
    # Insert Administrative Regions (Level 1)
    # ===================================
    
    regions_data = [
        {
            'name': 'Tanger-Tétouan-Al Hoceïma',
            'name_ar': 'طنجة تطوان الحسيمة',
            'code': 'MA-01',
            'type': 'mainland',
            'bounds': '((-6.0 35.9, -5.0 35.9, -5.0 34.5, -6.0 34.5, -6.0 35.9))'
        },
        {
            'name': 'L\'Oriental',
            'name_ar': 'الشرق',
            'code': 'MA-02',
            'type': 'mainland',
            'bounds': '((-3.0 35.0, -1.0 35.0, -1.0 32.0, -3.0 32.0, -3.0 35.0))'
        },
        {
            'name': 'Fès-Meknès',
            'name_ar': 'فاس مكناس',
            'code': 'MA-03',
            'type': 'mainland',
            'bounds': '((-6.0 34.5, -4.0 34.5, -4.0 32.5, -6.0 32.5, -6.0 34.5))'
        },
        {
            'name': 'Rabat-Salé-Kénitra',
            'name_ar': 'الرباط سلا القنيطرة',
            'code': 'MA-04',
            'type': 'mainland',
            'bounds': '((-7.0 34.5, -5.5 34.5, -5.5 33.5, -7.0 33.5, -7.0 34.5))'
        },
        {
            'name': 'Béni Mellal-Khénifra',
            'name_ar': 'بني ملال خنيفرة',
            'code': 'MA-05',
            'type': 'mainland',
            'bounds': '((-7.0 33.5, -5.0 33.5, -5.0 31.5, -7.0 31.5, -7.0 33.5))'
        },
        {
            'name': 'Casablanca-Settat',
            'name_ar': 'الدار البيضاء سطات',
            'code': 'MA-06',
            'type': 'mainland',
            'bounds': '((-8.5 34.0, -7.0 34.0, -7.0 32.0, -8.5 32.0, -8.5 34.0))'
        },
        {
            'name': 'Marrakech-Safi',
            'name_ar': 'مراكش آسفي',
            'code': 'MA-07',
            'type': 'mainland',
            'bounds': '((-9.5 32.5, -7.0 32.5, -7.0 30.5, -9.5 30.5, -9.5 32.5))'
        },
        {
            'name': 'Drâa-Tafilalet',
            'name_ar': 'درعة تافيلالت',
            'code': 'MA-08',
            'type': 'mainland',
            'bounds': '((-7.0 32.0, -4.0 32.0, -4.0 29.0, -7.0 29.0, -7.0 32.0))'
        },
        {
            'name': 'Souss-Massa',
            'name_ar': 'سوس ماسة',
            'code': 'MA-09',
            'type': 'mainland',
            'bounds': '((-10.0 31.0, -8.0 31.0, -8.0 29.0, -10.0 29.0, -10.0 31.0))'
        },
        {
            'name': 'Guelmim-Oued Noun',
            'name_ar': 'كلميم واد نون',
            'code': 'MA-10',
            'type': 'mainland',
            'bounds': '((-12.0 29.5, -9.0 29.5, -9.0 27.5, -12.0 27.5, -12.0 29.5))'
        },
        {
            'name': 'Laâyoune-Sakia El Hamra',
            'name_ar': 'العيون الساقية الحمراء',
            'code': 'MA-11',
            'type': 'sahara',
            'bounds': '((-14.0 27.5, -11.0 27.5, -11.0 24.5, -14.0 24.5, -14.0 27.5))'
        },
        {
            'name': 'Dakhla-Oued Ed-Dahab',
            'name_ar': 'الداخلة وادي الذهب',
            'code': 'MA-12',
            'type': 'sahara',
            'bounds': '((-16.0 24.5, -13.0 24.5, -13.0 20.5, -16.0 20.5, -16.0 24.5))'
        }
    ]
    
    # Insert regions
    for region in regions_data:
        connection.execute(text("""
            INSERT INTO morocco_territory (
                id, region_name, region_name_ar, region_type, administrative_level,
                official_code, geometry, is_official, source_authority
            ) VALUES (
                :id, :name, :name_ar, :type, 1,
                :code, ST_GeomFromText('MULTIPOLYGON(' || :bounds || ')', 4326), 
                TRUE, 'Ministère de l''Intérieur'
            )
        """), {
            'id': str(uuid.uuid4()),
            'name': region['name'],
            'name_ar': region['name_ar'],
            'type': region['type'],
            'code': region['code'],
            'bounds': region['bounds']
        })
    
    # ===================================
    # Insert Major Cities (Level 2)
    # ===================================
    
    cities_data = [
        {
            'name': 'Rabat',
            'name_ar': 'الرباط',
            'code': 'MA-RAB',
            'population': 577827,
            'area': 117.0,
            'coords': '((-6.85 34.05, -6.80 34.05, -6.80 34.00, -6.85 34.00, -6.85 34.05))'
        },
        {
            'name': 'Casablanca',
            'name_ar': 'الدار البيضاء',
            'code': 'MA-CAS',
            'population': 3359818,
            'area': 324.0,
            'coords': '((-7.65 33.65, -7.55 33.65, -7.55 33.55, -7.65 33.55, -7.65 33.65))'
        },
        {
            'name': 'Fès',
            'name_ar': 'فاس',
            'code': 'MA-FES',
            'population': 1112072,
            'area': 320.0,
            'coords': '((-5.05 34.10, -4.95 34.10, -4.95 34.00, -5.05 34.00, -5.05 34.10))'
        },
        {
            'name': 'Marrakech',
            'name_ar': 'مراكش',
            'code': 'MA-MAR',
            'population': 928850,
            'area': 230.0,
            'coords': '((-8.05 31.70, -7.95 31.70, -7.95 31.60, -8.05 31.60, -8.05 31.70))'
        },
        {
            'name': 'Tanger',
            'name_ar': 'طنجة',
            'code': 'MA-TAN',
            'population': 947952,
            'area': 124.0,
            'coords': '((-5.85 35.80, -5.75 35.80, -5.75 35.70, -5.85 35.70, -5.85 35.80))'
        },
        {
            'name': 'Agadir',
            'name_ar': 'أكادير',
            'code': 'MA-AGA',
            'population': 421844,
            'area': 85.0,
            'coords': '((-9.65 30.45, -9.55 30.45, -9.55 30.35, -9.65 30.35, -9.65 30.45))'
        },
        {
            'name': 'Meknès',
            'name_ar': 'مكناس',
            'code': 'MA-MEK',
            'population': 632079,
            'area': 370.0,
            'coords': '((-5.60 33.95, -5.50 33.95, -5.50 33.85, -5.60 33.85, -5.60 33.95))'
        },
        {
            'name': 'Oujda',
            'name_ar': 'وجدة',
            'code': 'MA-OUJ',
            'population': 494252,
            'area': 96.0,
            'coords': '((-1.95 34.70, -1.85 34.70, -1.85 34.60, -1.95 34.60, -1.95 34.70))'
        },
        {
            'name': 'Laâyoune',
            'name_ar': 'العيون',
            'code': 'MA-LAA',
            'population': 217732,
            'area': 750.0,
            'coords': '((-13.25 27.20, -13.15 27.20, -13.15 27.10, -13.25 27.10, -13.25 27.20))'
        },
        {
            'name': 'Dakhla',
            'name_ar': 'الداخلة',
            'code': 'MA-DAK',
            'population': 106277,
            'area': 1681.0,
            'coords': '((-15.95 23.75, -15.85 23.75, -15.85 23.65, -15.95 23.65, -15.95 23.75))'
        }
    ]
    
    # Insert cities
    for city in cities_data:
        connection.execute(text("""
            INSERT INTO morocco_territory (
                id, region_name, region_name_ar, region_type, administrative_level,
                official_code, population, area_km2, geometry, is_official, source_authority
            ) VALUES (
                :id, :name, :name_ar, 'mainland', 2,
                :code, :population, :area, 
                ST_GeomFromText('MULTIPOLYGON(' || :coords || ')', 4326), 
                TRUE, 'Ministère de l''Intérieur'
            )
        """), {
            'id': str(uuid.uuid4()),
            'name': city['name'],
            'name_ar': city['name_ar'],
            'code': city['code'],
            'population': city['population'],
            'area': city['area'],
            'coords': city['coords']
        })
    
    # ===================================
    # Insert Maritime Zones
    # ===================================
    
    maritime_zones = [
        {
            'name': 'Zone Économique Exclusive Atlantique',
            'name_ar': 'المنطقة الاقتصادية الخالصة الأطلسية',
            'code': 'MA-ZEE-ATL',
            'bounds': '((-20.0 36.0, -6.0 36.0, -6.0 20.0, -20.0 20.0, -20.0 36.0))'
        },
        {
            'name': 'Zone Économique Exclusive Méditerranéenne',
            'name_ar': 'المنطقة الاقتصادية الخالصة المتوسطية',
            'code': 'MA-ZEE-MED',
            'bounds': '((-6.0 36.5, -2.0 36.5, -2.0 35.0, -6.0 35.0, -6.0 36.5))'
        }
    ]
    
    # Insert maritime zones
    for zone in maritime_zones:
        connection.execute(text("""
            INSERT INTO morocco_territory (
                id, region_name, region_name_ar, region_type, administrative_level,
                official_code, geometry, is_official, source_authority
            ) VALUES (
                :id, :name, :name_ar, 'maritime', 1,
                :code, ST_GeomFromText('MULTIPOLYGON(' || :bounds || ')', 4326), 
                TRUE, 'Ministère de l''Intérieur'
            )
        """), {
            'id': str(uuid.uuid4()),
            'name': zone['name'],
            'name_ar': zone['name_ar'],
            'code': zone['code'],
            'bounds': zone['bounds']
        })
    
    # ===================================
    # Update calculated areas
    # ===================================
    
    connection.execute(text("""
        UPDATE morocco_territory 
        SET area_km2 = ST_Area(geometry::GEOGRAPHY) / 1000000.0 
        WHERE area_km2 IS NULL
    """))
    
    # ===================================
    # Create default spatial layers
    # ===================================
    
    # Morocco territory layer
    connection.execute(text("""
        INSERT INTO spatial_layers (
            id, name, description, layer_type, data_source,
            srid, geometry_type, visible, z_index,
            style_config, metadata
        ) VALUES (
            :id, 'Territoire du Royaume du Maroc', 
            'Limites officielles du territoire marocain selon le Ministère de l''Intérieur',
            'vector', 'morocco_territory',
            4326, 'MULTIPOLYGON', TRUE, 1,
            :style, :metadata
        )
    """), {
        'id': str(uuid.uuid4()),
        'style': '{"stroke": {"color": "#FF0000", "width": 2}, "fill": {"color": "#FF0000", "opacity": 0.1}}',
        'metadata': '{"official": true, "source": "Ministère de l\'Intérieur", "type": "administrative_boundaries"}'
    })
    
    # Equipment layers for different types
    equipment_types = [
        {'name': 'COMINT', 'color': '#2196F3', 'description': 'Équipements d\'interception communications'},
        {'name': 'ELINT', 'color': '#FF9800', 'description': 'Équipements de renseignement électronique'},
        {'name': 'Anti-Drone', 'color': '#F44336', 'description': 'Systèmes anti-drones'},
        {'name': 'Brouilleur', 'color': '#9C27B0', 'description': 'Équipements de brouillage'},
        {'name': 'Capteur', 'color': '#4CAF50', 'description': 'Capteurs divers'}
    ]
    
    for eq_type in equipment_types:
        connection.execute(text("""
            INSERT INTO spatial_layers (
                id, name, description, layer_type, data_source,
                srid, geometry_type, visible, z_index,
                style_config, metadata
            ) VALUES (
                :id, :name, :description, 'vector', 'equipment',
                4326, 'POINT', TRUE, 10,
                :style, :metadata
            )
        """), {
            'id': str(uuid.uuid4()),
            'name': f'Équipements {eq_type["name"]}',
            'description': eq_type['description'],
            'style': f'{{"marker": {{"color": "{eq_type["color"]}", "size": 8, "symbol": "circle"}}}}',
            'metadata': f'{{"equipment_type": "{eq_type["name"]}", "category": "military"}}'
        })
    
    # ===================================
    # Update statistics
    # ===================================
    
    connection.execute(text("ANALYZE morocco_territory"))
    connection.execute(text("ANALYZE spatial_layers"))


def downgrade():
    """Remove Morocco territory data"""
    
    connection = op.get_bind()
    
    # Delete in reverse order
    connection.execute(text("DELETE FROM spatial_layers WHERE data_source IN ('morocco_territory', 'equipment')"))
    connection.execute(text("DELETE FROM morocco_territory"))
