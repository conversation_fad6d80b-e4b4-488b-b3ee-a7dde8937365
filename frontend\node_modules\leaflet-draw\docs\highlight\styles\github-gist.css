/**
 * GitHub Gist Theme
 * Author : <PERSON> - https://github.com/Louis<PERSON>
 */

.hljs {
  display: block;
  background:#f8f8f8;
  padding: 0.5em;
  color: #333333;
  overflow-x: auto;
  -webkit-text-size-adjust: none;
}

.hljs-comment,
.bash .hljs-shebang,
.java .hljs-javadoc,
.javascript .hljs-javadoc {
  color: #969896;
}

.hljs-string,
.apache .hljs-sqbracket,
.coffeescript .hljs-subst,
.coffeescript .hljs-regexp,
.cpp .hljs-preprocessor,
.c .hljs-preprocessor,
.javascript .hljs-regexp,
.json .hljs-attribute,
.makefile .hljs-variable,
.markdown .hljs-value,
.markdown .hljs-link_label,
.markdown .hljs-strong,
.markdown .hljs-emphasis,
.markdown .hljs-blockquote,
.nginx .hljs-regexp,
.nginx .hljs-number,
.objectivec .hljs-preprocessor .hljs-title,
.perl .hljs-regexp,
.php .hljs-regexp,
.xml .hljs-value,
.less .hljs-built_in,
.scss .hljs-built_in {
  color: #df5000;
}

.hljs-keyword,
.css .hljs-at_rule,
.css .hljs-important,
.http .hljs-request,
.ini .hljs-setting,
.java .hljs-javadoctag,
.javascript .hljs-tag,
.javascript .hljs-javadoctag,
.nginx .hljs-title,
.objectivec .hljs-preprocessor,
.php .hljs-phpdoc,
.sql .hljs-built_in,
.less .hljs-tag,
.less .hljs-at_rule,
.scss .hljs-tag,
.scss .hljs-at_rule,
.scss .hljs-important,
.stylus .hljs-at_rule,
.go .hljs-typename,
.swift .hljs-preprocessor {
  color: #a71d5d;
}

.apache .hljs-common,
.apache .hljs-cbracket,
.apache .hljs-keyword,
.bash .hljs-literal,
.bash .hljs-built_in,
.coffeescript .hljs-literal,
.coffeescript .hljs-built_in,
.coffeescript .hljs-number,
.cpp .hljs-number,
.cpp .hljs-built_in,
.c .hljs-number,
.c .hljs-built_in,
.cs .hljs-number,
.cs .hljs-built_in,
.css .hljs-attribute,
.css .hljs-hexcolor,
.css .hljs-number,
.css .hljs-function,
.http .hljs-literal,
.http .hljs-attribute,
.java .hljs-number,
.javascript .hljs-built_in,
.javascript .hljs-literal,
.javascript .hljs-number,
.json .hljs-number,
.makefile .hljs-keyword,
.markdown .hljs-link_reference,
.nginx .hljs-built_in,
.objectivec .hljs-literal,
.objectivec .hljs-number,
.objectivec .hljs-built_in,
.php .hljs-literal,
.php .hljs-number,
.python .hljs-number,
.ruby .hljs-prompt,
.ruby .hljs-constant,
.ruby .hljs-number,
.ruby .hljs-subst .hljs-keyword,
.ruby .hljs-symbol,
.sql .hljs-number,
.puppet .hljs-function,
.less .hljs-number,
.less .hljs-hexcolor,
.less .hljs-function,
.less .hljs-attribute,
.scss .hljs-preprocessor,
.scss .hljs-number,
.scss .hljs-hexcolor,
.scss .hljs-function,
.scss .hljs-attribute,
.stylus .hljs-number,
.stylus .hljs-hexcolor,
.stylus .hljs-attribute,
.stylus .hljs-params,
.go .hljs-built_in,
.go .hljs-constant,
.swift .hljs-built_in,
.swift .hljs-number {
  color: #0086b3;
}

.apache .hljs-tag,
.cs .hljs-xmlDocTag,
.css .hljs-tag,
.xml .hljs-title,
.stylus .hljs-tag {
  color: #63a35c;
}

.bash .hljs-variable,
.cs .hljs-preprocessor,
.cs .hljs-preprocessor .hljs-keyword,
.css .hljs-attr_selector,
.css .hljs-value,
.ini .hljs-value,
.ini .hljs-keyword,
.javascript .hljs-tag .hljs-title,
.makefile .hljs-constant,
.nginx .hljs-variable,
.xml .hljs-tag,
.scss .hljs-variable {
  color: #333333;
}

.bash .hljs-title,
.coffeescript .hljs-title,
.cpp .hljs-title,
.c .hljs-title,
.cs .hljs-title,
.css .hljs-id,
.css .hljs-class,
.css .hljs-pseudo,
.ini .hljs-title,
.java .hljs-title,
.javascript .hljs-title,
.makefile .hljs-title,
.objectivec .hljs-title,
.perl .hljs-sub,
.php .hljs-title,
.python .hljs-decorator,
.python .hljs-title,
.ruby .hljs-parent,
.ruby .hljs-title,
.xml .hljs-attribute,
.puppet .hljs-title,
.less .hljs-id,
.less .hljs-pseudo,
.less .hljs-class,
.scss .hljs-id,
.scss .hljs-pseudo,
.scss .hljs-class,
.stylus .hljs-class,
.stylus .hljs-id,
.stylus .hljs-pseudo,
.stylus .hljs-title,
.swift .hljs-title,
.diff .hljs-chunk {
  color: #795da3;
}

.coffeescript .hljs-reserved,
.coffeescript .hljs-attribute {
  color: #1d3e81;
}

.diff .hljs-chunk {
  font-weight: bold;
}

.diff .hljs-addition {
  color: #55a532;
  background-color: #eaffea;
}

.diff .hljs-deletion {
  color: #bd2c00;
  background-color: #ffecec;
}

.markdown .hljs-link_url {
  text-decoration: underline;
}
