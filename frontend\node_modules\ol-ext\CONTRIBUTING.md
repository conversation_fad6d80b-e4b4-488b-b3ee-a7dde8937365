# Contributing to ol-ext

Thanks for your interest in contributing to ol-ext.

## Submitting Bug Reports or Asking Questions

Please use the [GitHub issue tracker](https://github.com/Viglino/ol-ext/issues). 
Before creating a new issue, do a quick search to see if the problem has been reported already.

## Contributing Code

See [`DEVELOPING.md`](DEVELOPING.md) to learn how to get started developing.

Clone the repository and [pull requests](https://help.github.com/articles/using-pull-requests). Make sure
that your pull request follows our pull request guidelines below before submitting it.

This page describes what you need to know to contribute code to ol-ext as a developer.

## Contributor License Agreement

Your contribution will be under our [license](LICENSE.md) 
as per [GitHub's terms of service](https://help.github.com/articles/github-terms-of-service/#6-contributions-under-repository-license).

The CeCILL licence is a french BSD licence.

## Pull request guidelines

Your pull request must:
* Follow the naming convention in the [DEVELOPING.md](DEVELOPING.md)
* Address a single issue or add a single item of functionality.
* Use clear commit messages.
* Be possible to merge automatically.
