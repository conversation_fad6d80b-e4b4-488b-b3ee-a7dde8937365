"""
Service de synchronisation PostGIS/SQLite
Module SIG C2-EW - Synchronisation bidirectionnelle intelligente
"""

import asyncio
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID, uuid4
from pathlib import Path

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, update, delete
from geoalchemy2.functions import ST_AsGeoJSON, ST_GeomFromGeoJSON
from shapely.geometry import shape
from shapely import wkt

from ..models.gis_models import (
    SpatialLayer, SpatialFeature, MoroccoTerritory, 
    MBTilesDataset, VisibilityAnalysis, SpatialMeasurement,
    SpatialCache, SyncLog
)
from ..core.database import get_async_session
from ..core.config import settings

logger = logging.getLogger(__name__)

class SyncService:
    """Service de synchronisation entre PostGIS et SQLite"""
    
    def __init__(self):
        self.postgis_pool = None
        self.sqlite_connections = {}
        self.sync_strategies = {
            'full': self._full_sync,
            'incremental': self._incremental_sync,
            'conflict_resolution': self._resolve_conflicts
        }
        
    async def initialize(self):
        """Initialisation du service de synchronisation"""
        try:
            # Créer le pool de connexions PostGIS
            self.postgis_pool = await asyncpg.create_pool(
                host=settings.DATABASE_HOST,
                port=settings.DATABASE_PORT,
                user=settings.DATABASE_USER,
                password=settings.DATABASE_PASSWORD,
                database=settings.DATABASE_NAME,
                min_size=2,
                max_size=10
            )
            
            logger.info("✅ Service de synchronisation initialisé")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation du service de sync: {e}")
            raise
    
    async def get_sqlite_connection(self, client_id: str) -> sqlite3.Connection:
        """Obtenir une connexion SQLite pour un client"""
        if client_id not in self.sqlite_connections:
            sqlite_path = Path(settings.SQLITE_CACHE_DIR) / f"client_{client_id}.db"
            sqlite_path.parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(str(sqlite_path))
            conn.enable_load_extension(True)
            
            # Charger SpatiaLite
            try:
                conn.load_extension("mod_spatialite")
                conn.execute("SELECT InitSpatialMetadata(1)")
            except Exception as e:
                logger.warning(f"⚠️ Impossible de charger SpatiaLite: {e}")
            
            self.sqlite_connections[client_id] = conn
            
            # Créer les tables si nécessaire
            await self._create_sqlite_tables(conn)
        
        return self.sqlite_connections[client_id]
    
    async def _create_sqlite_tables(self, conn: sqlite3.Connection):
        """Créer les tables SQLite si elles n'existent pas"""
        try:
            # Lire le schéma SQLite
            schema_path = Path(__file__).parent.parent.parent / "database" / "gis" / "spatialite_schema.sql"
            
            if schema_path.exists():
                with open(schema_path, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # Exécuter le schéma
                conn.executescript(schema_sql)
                conn.commit()
                
                logger.info("✅ Tables SQLite créées")
            else:
                logger.warning("⚠️ Fichier de schéma SQLite non trouvé")
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la création des tables SQLite: {e}")
    
    async def sync_to_client(
        self, 
        client_id: str, 
        strategy: str = 'incremental',
        tables: Optional[List[str]] = None,
        extent: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Synchroniser les données PostGIS vers SQLite client"""
        
        session_id = uuid4()
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"🔄 Début de synchronisation vers client {client_id} (stratégie: {strategy})")
            
            # Obtenir les connexions
            sqlite_conn = await self.get_sqlite_connection(client_id)
            
            # Déterminer les tables à synchroniser
            if tables is None:
                tables = [
                    'spatial_layers', 'spatial_features', 'morocco_territory',
                    'mbtiles_datasets', 'visibility_analyses', 'spatial_measurements'
                ]
            
            sync_results = {}
            total_records = 0
            
            async with self.postgis_pool.acquire() as pg_conn:
                for table_name in tables:
                    try:
                        result = await self._sync_table_to_sqlite(
                            pg_conn, sqlite_conn, table_name, extent
                        )
                        sync_results[table_name] = result
                        total_records += result.get('records_synced', 0)
                        
                    except Exception as e:
                        logger.error(f"❌ Erreur sync table {table_name}: {e}")
                        sync_results[table_name] = {'error': str(e)}
            
            # Enregistrer le log de synchronisation
            await self._log_sync_operation(
                session_id, 'download', 'success', 
                f"Synchronisé {total_records} enregistrements vers client {client_id}"
            )
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                'session_id': str(session_id),
                'status': 'success',
                'duration_seconds': duration,
                'total_records': total_records,
                'tables': sync_results
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la synchronisation vers client {client_id}: {e}")
            
            await self._log_sync_operation(
                session_id, 'download', 'error', str(e)
            )
            
            return {
                'session_id': str(session_id),
                'status': 'error',
                'error': str(e)
            }
    
    async def _sync_table_to_sqlite(
        self, 
        pg_conn: asyncpg.Connection,
        sqlite_conn: sqlite3.Connection,
        table_name: str,
        extent: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Synchroniser une table spécifique vers SQLite"""
        
        try:
            # Construire la requête PostGIS
            if table_name == 'spatial_features':
                query = """
                    SELECT 
                        id, layer_id, name, feature_type, properties, style,
                        ST_AsGeoJSON(geometry) as geometry_json,
                        created_at, updated_at
                    FROM spatial_features
                """
                
                if extent:
                    query += f"""
                        WHERE ST_Intersects(
                            geometry, 
                            ST_MakeEnvelope({extent[0]}, {extent[1]}, {extent[2]}, {extent[3]}, 4326)
                        )
                    """
                
            elif table_name == 'morocco_territory':
                query = """
                    SELECT 
                        id, region_name, region_name_ar, region_type,
                        administrative_level, parent_region_id, official_code,
                        population, area_km2, is_official, source_authority,
                        ST_AsGeoJSON(geometry) as geometry_json,
                        created_at, updated_at
                    FROM morocco_territory
                    WHERE is_official = TRUE
                """
                
            elif table_name == 'spatial_layers':
                query = """
                    SELECT 
                        id, name, description, layer_type, data_source,
                        srid, geometry_type, visible, opacity, z_index,
                        style_config, metadata,
                        created_at, updated_at
                    FROM spatial_layers
                """
                
            else:
                # Table générique
                query = f"SELECT * FROM {table_name}"
            
            # Exécuter la requête PostGIS
            rows = await pg_conn.fetch(query)
            
            if not rows:
                return {'records_synced': 0, 'message': 'Aucun enregistrement trouvé'}
            
            # Préparer l'insertion SQLite
            cache_table = f"cache_{table_name}"
            
            # Vider la table cache
            sqlite_conn.execute(f"DELETE FROM {cache_table}")
            
            records_synced = 0
            
            for row in rows:
                try:
                    # Convertir les données
                    data = dict(row)
                    
                    # Traitement spécial pour les géométries
                    if 'geometry_json' in data and data['geometry_json']:
                        geometry_wkt = self._geojson_to_wkt(data['geometry_json'])
                        data['geometry'] = geometry_wkt
                        del data['geometry_json']
                    
                    # Traitement des JSON
                    for key, value in data.items():
                        if isinstance(value, dict) or isinstance(value, list):
                            data[key] = json.dumps(value)
                        elif isinstance(value, UUID):
                            data[key] = str(value)
                        elif isinstance(value, datetime):
                            data[key] = value.isoformat()
                    
                    # Ajouter les métadonnées de cache
                    data['cached_at'] = datetime.utcnow().isoformat()
                    data['source_id'] = data.get('id')
                    data['sync_status'] = 'cached'
                    data['last_sync'] = datetime.utcnow().isoformat()
                    
                    # Construire la requête d'insertion
                    columns = list(data.keys())
                    placeholders = ['?' for _ in columns]
                    values = [data[col] for col in columns]
                    
                    insert_query = f"""
                        INSERT OR REPLACE INTO {cache_table} 
                        ({', '.join(columns)}) 
                        VALUES ({', '.join(placeholders)})
                    """
                    
                    sqlite_conn.execute(insert_query, values)
                    records_synced += 1
                    
                except Exception as e:
                    logger.error(f"❌ Erreur insertion enregistrement {table_name}: {e}")
                    continue
            
            sqlite_conn.commit()
            
            return {
                'records_synced': records_synced,
                'total_available': len(rows)
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur sync table {table_name}: {e}")
            raise
    
    async def sync_from_client(
        self, 
        client_id: str,
        changes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Synchroniser les modifications du client vers PostGIS"""
        
        session_id = uuid4()
        start_time = datetime.utcnow()
        
        try:
            logger.info(f"🔄 Début de synchronisation depuis client {client_id}")
            
            results = []
            conflicts = []
            
            async with self.postgis_pool.acquire() as pg_conn:
                async with pg_conn.transaction():
                    for change in changes:
                        try:
                            result = await self._apply_change_to_postgis(
                                pg_conn, change, client_id
                            )
                            
                            if result.get('conflict'):
                                conflicts.append(result)
                            else:
                                results.append(result)
                                
                        except Exception as e:
                            logger.error(f"❌ Erreur application changement: {e}")
                            results.append({
                                'change_id': change.get('id'),
                                'status': 'error',
                                'error': str(e)
                            })
            
            # Enregistrer le log
            await self._log_sync_operation(
                session_id, 'upload', 'success',
                f"Appliqué {len(results)} changements depuis client {client_id}"
            )
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                'session_id': str(session_id),
                'status': 'success',
                'duration_seconds': duration,
                'applied_changes': len(results),
                'conflicts': len(conflicts),
                'results': results,
                'conflicts_details': conflicts
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur sync depuis client {client_id}: {e}")
            
            await self._log_sync_operation(
                session_id, 'upload', 'error', str(e)
            )
            
            return {
                'session_id': str(session_id),
                'status': 'error',
                'error': str(e)
            }
    
    async def _apply_change_to_postgis(
        self,
        pg_conn: asyncpg.Connection,
        change: Dict[str, Any],
        client_id: str
    ) -> Dict[str, Any]:
        """Appliquer un changement à PostGIS"""
        
        table_name = change.get('table')
        operation = change.get('operation')  # insert, update, delete
        record_id = change.get('record_id')
        data = change.get('data', {})
        
        try:
            if operation == 'insert':
                return await self._insert_record(pg_conn, table_name, data)
            elif operation == 'update':
                return await self._update_record(pg_conn, table_name, record_id, data)
            elif operation == 'delete':
                return await self._delete_record(pg_conn, table_name, record_id)
            else:
                raise ValueError(f"Opération non supportée: {operation}")
                
        except Exception as e:
            logger.error(f"❌ Erreur application changement {operation} sur {table_name}: {e}")
            raise
    
    def _geojson_to_wkt(self, geojson_str: str) -> str:
        """Convertir GeoJSON en WKT"""
        try:
            geojson = json.loads(geojson_str) if isinstance(geojson_str, str) else geojson_str
            geometry = shape(geojson)
            return geometry.wkt
        except Exception as e:
            logger.error(f"❌ Erreur conversion GeoJSON vers WKT: {e}")
            return None
    
    async def _log_sync_operation(
        self,
        session_id: UUID,
        sync_type: str,
        status: str,
        message: str
    ):
        """Enregistrer une opération de synchronisation"""
        try:
            async with get_async_session() as session:
                sync_log = SyncLog(
                    sync_session_id=session_id,
                    sync_type=sync_type,
                    status=status,
                    table_name='multiple',
                    error_message=message if status == 'error' else None
                )
                
                session.add(sync_log)
                await session.commit()
                
        except Exception as e:
            logger.error(f"❌ Erreur enregistrement log sync: {e}")
    
    async def get_sync_status(self, client_id: str) -> Dict[str, Any]:
        """Obtenir le statut de synchronisation pour un client"""
        try:
            sqlite_conn = await self.get_sqlite_connection(client_id)
            
            # Obtenir les métadonnées de cache
            cursor = sqlite_conn.execute("""
                SELECT key, value FROM cache_metadata 
                WHERE key IN ('last_full_sync', 'sync_strategy', 'cache_version')
            """)
            
            metadata = dict(cursor.fetchall())
            
            # Compter les enregistrements en cache
            tables = [
                'cache_spatial_layers', 'cache_spatial_features', 
                'cache_morocco_territory', 'cache_mbtiles_datasets'
            ]
            
            table_counts = {}
            for table in tables:
                try:
                    cursor = sqlite_conn.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_counts[table.replace('cache_', '')] = count
                except:
                    table_counts[table.replace('cache_', '')] = 0
            
            return {
                'client_id': client_id,
                'last_sync': metadata.get('last_full_sync'),
                'sync_strategy': metadata.get('sync_strategy', 'incremental'),
                'cache_version': metadata.get('cache_version', '1.0'),
                'cached_records': table_counts,
                'total_cached': sum(table_counts.values())
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur obtention statut sync: {e}")
            return {'error': str(e)}
    
    async def cleanup_old_caches(self, max_age_days: int = 30):
        """Nettoyer les anciens caches SQLite"""
        try:
            cache_dir = Path(settings.SQLITE_CACHE_DIR)
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            
            cleaned_files = 0
            
            for sqlite_file in cache_dir.glob("client_*.db"):
                if sqlite_file.stat().st_mtime < cutoff_date.timestamp():
                    sqlite_file.unlink()
                    cleaned_files += 1
                    
                    # Supprimer aussi de la mémoire
                    client_id = sqlite_file.stem.replace('client_', '')
                    if client_id in self.sqlite_connections:
                        self.sqlite_connections[client_id].close()
                        del self.sqlite_connections[client_id]
            
            logger.info(f"🧹 Nettoyé {cleaned_files} fichiers de cache anciens")
            
        except Exception as e:
            logger.error(f"❌ Erreur nettoyage caches: {e}")
    
    async def close(self):
        """Fermer les connexions"""
        try:
            # Fermer les connexions SQLite
            for conn in self.sqlite_connections.values():
                conn.close()
            self.sqlite_connections.clear()
            
            # Fermer le pool PostGIS
            if self.postgis_pool:
                await self.postgis_pool.close()
            
            logger.info("✅ Service de synchronisation fermé")
            
        except Exception as e:
            logger.error(f"❌ Erreur fermeture service sync: {e}")

# Instance globale du service
sync_service = SyncService()
