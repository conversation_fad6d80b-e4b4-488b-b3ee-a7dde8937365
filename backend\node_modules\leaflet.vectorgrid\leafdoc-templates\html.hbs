<!DOCTYPE html>
<html>
<head>
	<title>Leaflet.VectorGrid API reference</title>
	<meta charset="utf-8">

<!-- 	<link rel="stylesheet" href="http://www.leafletjs.com/docs/css/normalize.css" /> -->
	<link rel="stylesheet" href="main.css" />
	<script src="https://unpkg.com/highlight"></script>
<!-- 	<link rel="stylesheet" href="http://www.leafletjs.com/docs/highlight/styles/github-gist.css" /> -->
<!-- 	<link rel="stylesheet" href="http://www.leafletjs.com/docs/leaflet.css" /> -->
	<script src="leaflet.js"></script>
</head>
<body class='api-page'>
	<div class='container'>

	<h2>Leaflet.VectorGrid API reference</h2>
	<div id="toc" class="clearfix">
		<div class="toc-col map-col">
			<ul>
				<li><a href="#vectorgrid">VectorGrid</a></li>
				<li><a href="#vectorgrid-slicer">VectorGrid.Slicer</a></li>
				<li><a href="#vectorgrid-protobuf">VectorGrid.Protobuf</a></li>
			</ul>
		</div>
		<div class="toc-col map-col">
			<ul>
				<li><a href="#styling-vectorgrids">Styling VectorGrids</a></li>
				<li><a href="#updating-styles">Updating Styles</a></li>
				<li><a href="#interactivity">Interactivity</a></li>
				<li><a href="#svg-vs-canvas">SVG vs <code>&lt;canvas&gt;</code></a></li>
			</ul>
		</div>
	</div>

	{{{ body }}}

	<div class="footer">
		<p>© 2016-2017 Iván Sánchez Ortega, Per Liedman.</p>
	</div>

	</div>

<!-- 	<script src="http://leafletjs.com/docs/js/docs.js"></script> -->
	<script>
	hljs.configure({tabReplace: '    '});
	hljs.initHighlightingOnLoad();

	var elems = document.querySelectorAll('h2, h3, h4, tr');

	for (var i = 0, len = elems.length; i < len; i++) {
		var el = elems[i];

		if (el.id) {
			var anchor = document.createElement('a');
			anchor.setAttribute('anchor', el.id);
			if (!el.children.length) {
				// For headers, insert the anchor before.
				el.parentNode.insertBefore(anchor, el);
			} else {
				// For table rows, insert the anchor inside the first <td>
				el.querySelector('td').appendChild(anchor);

				// Clicking on the row (meaning "the link icon on the ::before)
				// jumps to the item
				el.parentNode.onclick = function(hash){
					return function(ev) {
						if (ev.offsetX < 0) {
							window.location.hash = '#' + ev.target.parentNode.id;
						}
					};
				}(el.id);
			}
		}
	}

	elems = document.querySelectorAll('div.accordion');
	for (var i = 0, len = elems.length; i < len; i++) {
		var el = elems[i];

		el.querySelector('label').addEventListener('click', function(c){
			return function() {
				if (c.className === 'accordion expanded') {
					c.className = 'accordion collapsed';
				} else {
					c.className = 'accordion expanded';
				}
			};
		}(el));

// 		el.className = 'accordion collapsed';
// 		el.querySelector('.accordion-content').style.display = 'none';
	}

</script>
<style>

	h2 {
		margin-top: 2em;
	}

	h3 {
		margin-top: 1em;
		margin-bottom: .5em;
	}

	div.accordion {
		width: 100%;
/* 		overflow: hidden; */
	}

	div.accordion-overflow {
		width: 100%;
		overflow: hidden;
	}

	label,
	section > h4 {
		display: block;
		font-weight: 500;
		margin: 1em 0 0.25em;
	}

	label {
		cursor: pointer;
	}

	div.accordion > div.accordion-overflow > div.accordion-content {
		max-height: 0;
		display: none;
	}

	div.accordion.collapsed > div.accordion-overflow > div.accordion-content {
		animation-duration: 0.4s;
		animation-name: collapse;
/* 		height: 0; */
		max-height: 0;
		display: block;
		overflow: hidden;
	}

	div.accordion.expanded > div.accordion-overflow > div.accordion-content {
		animation-duration: 0.4s;
		animation-name: expand;
/* 		height: auto; */
		max-height: none;
		display: block;
	}

	@keyframes collapse {
		0% { max-height: 100vh; }
		100% { max-height: 0; }
	}

	@keyframes expand {
		0% { max-height: 0; }
		100% { max-height: 100vh; }
	}

/*	div.accordion > div.accordion-content {
		transition: max-height 0.4s ease-out 0s;
	}*/

	div.accordion.expanded > label > span.expander {
		transform: rotate(90deg);
	}

	div.accordion > label > span.expander {
		transition: transform 0.4s ease-out 0s;
		display: inline-block;
		font-size: 12px;
	}


	table {
		margin-bottom: 0;
	}

/* 	Markdown renders some spurious <p>s inside the table cells */
	td > p {
		margin:0;
	}

/* 	This just looks bad (with the current grey headers for sections which Vlad doesn't really like, so might have to change this) */
	section.collapsable > div.section-comments > p {
		margin:0;
	}

	div.section-comments {
		margin-bottom: 0.25em;
	}

/*	section.collapsable div.section-comments {
		margin: 1em;
		font-size: 12px;
	}*/

	section.collapsable pre {
		margin:0;
	}

	section {
		margin-left: 0.5em;
	}

	section h4, section.collapsable h4 {
		margin-left: -0.5em;
	}



</style>
</body></html>
