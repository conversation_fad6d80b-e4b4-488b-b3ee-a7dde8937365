/**
 * Moteur SIG principal basé sur OpenLayers 8+
 * Module cartographique C2-EW - Fonctionnement hors ligne
 */

import { Map, View } from 'ol';
import { defaults as defaultControls, ScaleLine, MousePosition, OverviewMap } from 'ol/control';
import { defaults as defaultInteractions, DragRotateAndZoom } from 'ol/interaction';
import { Tile as TileLayer, Vector as VectorLayer, Group as LayerGroup } from 'ol/layer';
import { OSM, XYZ, Vector as VectorSource, Cluster } from 'ol/source';
import { Style, Fill, Stroke, Circle, Text, Icon } from 'ol/style';
import { Point, LineString, Polygon, MultiPolygon } from 'ol/geom';
import { Feature } from 'ol';
import { fromLonLat, toLonLat, transformExtent } from 'ol/proj';
import { register } from 'ol/proj/proj4';
import proj4 from 'proj4';
import { getCenter } from 'ol/extent';
import { EventEmitter } from 'events';

// Configuration des projections personnalisées
proj4.defs('EPSG:26191', '+proj=lcc +lat_1=33.3 +lat_0=33.3 +lon_0=-5.4 +k_0=0.999625769 +x_0=500000 +y_0=300000 +ellps=clrk80 +towgs84=31,146,47,0,0,0,0 +units=m +no_defs');
register(proj4);

/**
 * Moteur SIG principal pour la plateforme C2-EW
 */
export class GISEngine extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            target: 'map',
            projection: 'EPSG:3857', // Web Mercator par défaut
            center: [-7.6, 33.6], // Casablanca, Maroc
            zoom: 6,
            minZoom: 4,
            maxZoom: 20,
            enableRotation: true,
            enableOffline: true,
            ...options
        };
        
        this.map = null;
        this.layers = new Map();
        this.layerGroups = new Map();
        this.isInitialized = false;
        this.offlineMode = false;
        
        // Gestionnaires de fonctionnalités
        this.layerManager = null;
        this.projectionManager = null;
        this.performanceManager = null;
        
        this.init();
    }
    
    /**
     * Initialisation du moteur SIG
     */
    init() {
        try {
            this.createMap();
            this.setupControls();
            this.setupInteractions();
            this.setupEventHandlers();
            this.createDefaultLayers();
            
            this.isInitialized = true;
            this.emit('initialized', this);
            
            console.log('✅ Moteur SIG initialisé avec succès');
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation du moteur SIG:', error);
            this.emit('error', error);
        }
    }
    
    /**
     * Création de la carte OpenLayers
     */
    createMap() {
        const view = new View({
            projection: this.options.projection,
            center: fromLonLat(this.options.center),
            zoom: this.options.zoom,
            minZoom: this.options.minZoom,
            maxZoom: this.options.maxZoom,
            enableRotation: this.options.enableRotation,
            constrainResolution: true
        });
        
        this.map = new Map({
            target: this.options.target,
            view: view,
            controls: [], // Ajoutés séparément
            interactions: [], // Ajoutées séparément
            pixelRatio: window.devicePixelRatio || 1
        });
        
        // Configuration pour le territoire marocain
        this.setMoroccoExtent();
    }
    
    /**
     * Configuration de l'emprise pour le territoire marocain
     */
    setMoroccoExtent() {
        // Emprise du Royaume du Maroc (incluant le Sahara)
        const moroccoExtent = transformExtent(
            [-17.0, 20.5, -1.0, 36.0], // [ouest, sud, est, nord]
            'EPSG:4326',
            this.options.projection
        );
        
        this.map.getView().fit(moroccoExtent, {
            padding: [20, 20, 20, 20],
            maxZoom: 8
        });
    }
    
    /**
     * Configuration des contrôles de carte
     */
    setupControls() {
        const controls = [
            // Contrôles par défaut
            ...defaultControls({
                attribution: true,
                zoom: true,
                rotate: this.options.enableRotation
            }),
            
            // Échelle
            new ScaleLine({
                units: 'metric',
                bar: true,
                steps: 4,
                text: true,
                minWidth: 140
            }),
            
            // Position de la souris
            new MousePosition({
                coordinateFormat: (coord) => {
                    const lonLat = toLonLat(coord);
                    return `${lonLat[1].toFixed(6)}°N, ${lonLat[0].toFixed(6)}°E`;
                },
                projection: 'EPSG:4326',
                className: 'custom-mouse-position'
            }),
            
            // Carte de vue d'ensemble
            new OverviewMap({
                className: 'ol-overviewmap ol-custom-overviewmap',
                layers: [
                    new TileLayer({
                        source: new OSM()
                    })
                ],
                collapseLabel: '«',
                label: '»',
                collapsed: true
            })
        ];
        
        controls.forEach(control => this.map.addControl(control));
    }
    
    /**
     * Configuration des interactions
     */
    setupInteractions() {
        const interactions = [
            // Interactions par défaut
            ...defaultInteractions({
                altShiftDragRotate: this.options.enableRotation,
                pinchRotate: this.options.enableRotation
            }),
            
            // Rotation avec glisser
            new DragRotateAndZoom()
        ];
        
        interactions.forEach(interaction => this.map.addInteraction(interaction));
    }
    
    /**
     * Configuration des gestionnaires d'événements
     */
    setupEventHandlers() {
        // Événements de la carte
        this.map.on('moveend', () => {
            const view = this.map.getView();
            const center = toLonLat(view.getCenter());
            const zoom = view.getZoom();
            
            this.emit('viewChanged', {
                center,
                zoom,
                extent: this.getCurrentExtent()
            });
        });
        
        this.map.on('click', (event) => {
            const coordinate = toLonLat(event.coordinate);
            const features = this.map.getFeaturesAtPixel(event.pixel);
            
            this.emit('mapClick', {
                coordinate,
                features,
                pixel: event.pixel
            });
        });
        
        this.map.on('pointermove', (event) => {
            const features = this.map.getFeaturesAtPixel(event.pixel);
            const coordinate = toLonLat(event.coordinate);
            
            this.emit('pointerMove', {
                coordinate,
                features,
                pixel: event.pixel
            });
        });
        
        // Gestion des erreurs de tuiles
        this.map.on('rendercomplete', () => {
            this.emit('renderComplete');
        });
    }
    
    /**
     * Création des couches par défaut
     */
    createDefaultLayers() {
        // Groupe de couches de base
        const baseLayers = new LayerGroup({
            title: 'Cartes de base',
            layers: []
        });
        
        // Couche OpenStreetMap
        const osmLayer = new TileLayer({
            title: 'OpenStreetMap',
            type: 'base',
            visible: true,
            source: new OSM({
                url: this.options.enableOffline 
                    ? '/tiles/osm/{z}/{x}/{y}.png'
                    : undefined
            })
        });
        
        // Couche satellite
        const satelliteLayer = new TileLayer({
            title: 'Vue satellite',
            type: 'base',
            visible: false,
            source: new XYZ({
                url: this.options.enableOffline
                    ? '/tiles/satellite/{z}/{x}/{y}.jpg'
                    : 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
            })
        });
        
        baseLayers.getLayers().extend([osmLayer, satelliteLayer]);
        this.map.addLayer(baseLayers);
        
        // Enregistrer les couches
        this.layers.set('osm', osmLayer);
        this.layers.set('satellite', satelliteLayer);
        this.layerGroups.set('base', baseLayers);
        
        // Groupe de couches vectorielles
        const vectorLayers = new LayerGroup({
            title: 'Couches vectorielles',
            layers: []
        });
        
        this.map.addLayer(vectorLayers);
        this.layerGroups.set('vector', vectorLayers);
        
        // Groupe de couches d'équipements
        const equipmentLayers = new LayerGroup({
            title: 'Équipements',
            layers: []
        });
        
        this.map.addLayer(equipmentLayers);
        this.layerGroups.set('equipment', equipmentLayers);
    }
    
    /**
     * Obtenir l'emprise actuelle de la carte
     */
    getCurrentExtent() {
        const extent = this.map.getView().calculateExtent(this.map.getSize());
        return transformExtent(extent, this.options.projection, 'EPSG:4326');
    }
    
    /**
     * Centrer la carte sur une coordonnée
     */
    centerOn(coordinate, zoom = null) {
        const view = this.map.getView();
        view.setCenter(fromLonLat(coordinate));
        if (zoom !== null) {
            view.setZoom(zoom);
        }
        
        this.emit('centerChanged', { coordinate, zoom });
    }
    
    /**
     * Ajuster la vue sur une emprise
     */
    fitExtent(extent, options = {}) {
        const transformedExtent = transformExtent(extent, 'EPSG:4326', this.options.projection);
        
        this.map.getView().fit(transformedExtent, {
            padding: [20, 20, 20, 20],
            maxZoom: 16,
            ...options
        });
        
        this.emit('extentChanged', { extent });
    }
    
    /**
     * Basculer entre les couches de base
     */
    setBaseLayer(layerName) {
        const baseGroup = this.layerGroups.get('base');
        if (!baseGroup) return;
        
        baseGroup.getLayers().forEach(layer => {
            layer.setVisible(layer.get('title') === layerName);
        });
        
        this.emit('baseLayerChanged', { layerName });
    }
    
    /**
     * Activer/désactiver le mode hors ligne
     */
    setOfflineMode(enabled) {
        this.offlineMode = enabled;
        
        // Reconfigurer les sources de tuiles
        const osmLayer = this.layers.get('osm');
        const satelliteLayer = this.layers.get('satellite');
        
        if (osmLayer) {
            const newSource = new OSM({
                url: enabled ? '/tiles/osm/{z}/{x}/{y}.png' : undefined
            });
            osmLayer.setSource(newSource);
        }
        
        if (satelliteLayer) {
            const newSource = new XYZ({
                url: enabled 
                    ? '/tiles/satellite/{z}/{x}/{y}.jpg'
                    : 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
            });
            satelliteLayer.setSource(newSource);
        }
        
        this.emit('offlineModeChanged', { enabled });
    }
    
    /**
     * Obtenir la carte OpenLayers
     */
    getMap() {
        return this.map;
    }
    
    /**
     * Obtenir une couche par nom
     */
    getLayer(name) {
        return this.layers.get(name);
    }
    
    /**
     * Obtenir un groupe de couches par nom
     */
    getLayerGroup(name) {
        return this.layerGroups.get(name);
    }
    
    /**
     * Destruction du moteur
     */
    destroy() {
        if (this.map) {
            this.map.setTarget(null);
            this.map = null;
        }
        
        this.layers.clear();
        this.layerGroups.clear();
        this.removeAllListeners();
        
        this.isInitialized = false;
        console.log('🗑️ Moteur SIG détruit');
    }
}

export default GISEngine;
