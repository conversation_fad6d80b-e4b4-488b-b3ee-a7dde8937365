// operation.distance
export { default as ConnectedElementLocationFilter } from './distance/ConnectedElementLocationFilter'
export { default as ConnectedElementPointFilter } from './distance/ConnectedElementPointFilter'
export { default as DistanceOp } from './distance/DistanceOp'
export { default as FacetSequence } from './distance/FacetSequence'
export { default as FacetSequenceTreeBuilder } from './distance/FacetSequenceTreeBuilder'
export { default as GeometryLocation } from './distance/GeometryLocation'
export { default as IndexedFacetDistance } from './distance/IndexedFacetDistance'
