/**
 * @see http://download.oracle.com/javase/6/docs/api/java/util/Map.html
 *
 * @constructor
 * @private
 */
export default class Map {
  /**
   * Returns the value to which the specified key is mapped, or null if this map
   * contains no mapping for the key.
   * @param {Object} key
   * @return {Object}
   */
  get () {};
  /**
   * Associates the specified value with the specified key in this map (optional
   * operation).
   * @param {Object} key
   * @param {Object} value
   * @return {Object}
   */
  put () {};

  /**
   * Returns the number of key-value mappings in this map.
   * @return {number}
   */
  size () {};

  /**
   * Returns a Collection view of the values contained in this map.
   * @return {javascript.util.Collection}
   */
  values () {};

  /**
   * Returns a {@link Set} view of the mappings contained in this map.
   * The set is backed by the map, so changes to the map are
   * reflected in the set, and vice-versa.  If the map is modified
   * while an iteration over the set is in progress (except through
   * the iterator's own <tt>remove</tt> operation, or through the
   * <tt>setValue</tt> operation on a map entry returned by the
   * iterator) the results of the iteration are undefined.  The set
   * supports element removal, which removes the corresponding
   * mapping from the map, via the <tt>Iterator.remove</tt>,
   * <tt>Set.remove</tt>, <tt>removeAll</tt>, <tt>retainAll</tt> and
   * <tt>clear</tt> operations.  It does not support the
   * <tt>add</tt> or <tt>addAll</tt> operations.
   *
   * @return {Set} a set view of the mappings contained in this map
   */
  entrySet () {};
}
