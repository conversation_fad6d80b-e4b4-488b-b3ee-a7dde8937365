# Script de lancement complet C2-EW avec module SIG
# Version finale avec toutes les fonctionnalités

Write-Host "🚀 Lancement de l'application C2-EW complète" -ForegroundColor Green
Write-Host "📍 Module SIG avec OpenLayers 8+ et toutes les fonctionnalités" -ForegroundColor Cyan
Write-Host ""

# Fonction pour vérifier si un port est utilisé
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Fonction pour attendre qu'un service soit prêt
function Wait-ForService {
    param(
        [string]$Name,
        [string]$Url,
        [int]$MaxAttempts = 30
    )
    
    Write-Host "⏳ Attente de $Name..." -ForegroundColor Yellow
    
    for ($i = 1; $i -le $MaxAttempts; $i++) {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 2 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $Name est prêt!" -ForegroundColor Green
                return $true
            }
        } catch {
            # Service pas encore prêt
        }
        
        Write-Host "." -NoNewline -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Write-Host "❌ $Name n'a pas pu démarrer dans les temps" -ForegroundColor Red
    return $false
}

# Vérifier les prérequis
Write-Host "📋 Vérification des prérequis..." -ForegroundColor Blue

try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js non trouvé" -ForegroundColor Red
    exit 1
}

try {
    $pythonVersion = python --version
    Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python non trouvé" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Arrêter les processus existants
Write-Host "🧹 Nettoyage des processus existants..." -ForegroundColor Blue
Get-Process | Where-Object {$_.ProcessName -eq "python" -and $_.CommandLine -like "*simple_server*"} | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process | Where-Object {$_.ProcessName -eq "node" -and $_.CommandLine -like "*vite*"} | Stop-Process -Force -ErrorAction SilentlyContinue
Start-Sleep -Seconds 2

# Démarrer le backend
Write-Host "🐍 Démarrage du backend FastAPI..." -ForegroundColor Blue

$backendJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Desktop\C2-EW carte en ligne\backend"
    & ".\venv\Scripts\Activate.ps1"
    python simple_server.py
}

Write-Host "Backend démarré (Job ID: $($backendJob.Id))" -ForegroundColor Green

# Attendre que le backend soit prêt
if (-not (Wait-ForService -Name "Backend API" -Url "http://localhost:8000/health")) {
    Write-Host "❌ Impossible de démarrer le backend" -ForegroundColor Red
    Stop-Job -Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""

# Démarrer le frontend
Write-Host "⚛️ Démarrage du frontend React avec module SIG..." -ForegroundColor Blue

$frontendJob = Start-Job -ScriptBlock {
    Set-Location "C:\Users\<USER>\Desktop\C2-EW carte en ligne\frontend"
    npm run dev
}

Write-Host "Frontend démarré (Job ID: $($frontendJob.Id))" -ForegroundColor Green

# Attendre un peu pour que le frontend démarre
Start-Sleep -Seconds 5

# Détecter le port utilisé par le frontend
$frontendPort = 3000
for ($port = 3000; $port -le 3010; $port++) {
    if (Test-Port -Port $port) {
        $frontendPort = $port
        break
    }
}

Write-Host "Frontend détecté sur le port $frontendPort" -ForegroundColor Green

# Attendre que le frontend soit prêt
if (-not (Wait-ForService -Name "Frontend" -Url "http://localhost:$frontendPort")) {
    Write-Host "❌ Impossible de démarrer le frontend" -ForegroundColor Red
    Stop-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""

# Afficher les informations de l'application
Write-Host "🎉 Application C2-EW avec module SIG déployée!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Services disponibles:" -ForegroundColor Yellow
Write-Host "  🌐 Application Web:      http://localhost:$frontendPort" -ForegroundColor Cyan
Write-Host "  📡 API Backend:          http://localhost:8000" -ForegroundColor Cyan
Write-Host "  📖 Documentation API:    http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "  🏥 Health Check:         http://localhost:8000/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "👤 Comptes de test:" -ForegroundColor Yellow
Write-Host "  👨‍💼 admin / admin123      (Administrateur complet)" -ForegroundColor Cyan
Write-Host "  🔧 operator / op123       (Opérateur équipements)" -ForegroundColor Cyan
Write-Host "  🛠️ technician / tech123   (Technicien maintenance)" -ForegroundColor Cyan
Write-Host "  📊 viewer / view123       (Visualiseur lecture seule)" -ForegroundColor Cyan
Write-Host ""
Write-Host "🗺️ Fonctionnalités SIG disponibles:" -ForegroundColor Yellow
Write-Host "  📍 Carte OpenLayers 8+ avec projections marocaines" -ForegroundColor Cyan
Write-Host "  📏 Outils de mesure géodésique avancés" -ForegroundColor Cyan
Write-Host "  👁️ Analyse de visibilité 3D avec relief" -ForegroundColor Cyan
Write-Host "  🛡️ Gestion complète d'équipements C2-EW" -ForegroundColor Cyan
Write-Host "  🎯 Recherche spatiale intelligente" -ForegroundColor Cyan
Write-Host "  🌍 Support complet du territoire marocain" -ForegroundColor Cyan
Write-Host "  🔄 Synchronisation temps réel" -ForegroundColor Cyan
Write-Host "  📱 Interface responsive et moderne" -ForegroundColor Cyan
Write-Host ""
Write-Host "🛠️ Équipements de test inclus:" -ForegroundColor Yellow
Write-Host "  📡 Station COMINT Alpha (Casablanca)" -ForegroundColor Cyan
Write-Host "  📊 Capteur ELINT Beta (Rabat)" -ForegroundColor Cyan
Write-Host "  🛡️ Système Anti-Drone Gamma (Salé)" -ForegroundColor Cyan
Write-Host "  📵 Brouilleur Delta (Témara)" -ForegroundColor Cyan
Write-Host "  🔍 Capteur Epsilon (Mohammedia)" -ForegroundColor Cyan
Write-Host ""

# Ouvrir automatiquement le navigateur
Write-Host "🌐 Ouverture du navigateur..." -ForegroundColor Blue
Start-Process "http://localhost:$frontendPort"

Write-Host ""
Write-Host "✨ L'application C2-EW est maintenant prête!" -ForegroundColor Green
Write-Host "🎯 Connectez-vous avec admin/admin123 pour accéder à toutes les fonctionnalités" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 Fonctionnalités à tester:" -ForegroundColor Yellow
Write-Host "  1. 🗺️ Navigation sur la carte du Maroc" -ForegroundColor White
Write-Host "  2. 📍 Clic sur les équipements pour voir les détails" -ForegroundColor White
Write-Host "  3. 📏 Utilisation des outils de mesure" -ForegroundColor White
Write-Host "  4. 👁️ Analyse de visibilité depuis un point" -ForegroundColor White
Write-Host "  5. 🔍 Recherche spatiale d'équipements" -ForegroundColor White
Write-Host "  6. ⚙️ Gestion des couches cartographiques" -ForegroundColor White
Write-Host ""
Write-Host "Appuyez sur Ctrl+C pour arrêter tous les services." -ForegroundColor Yellow
Write-Host ""

# Attendre l'interruption de l'utilisateur
try {
    while ($true) {
        Start-Sleep -Seconds 1
        
        # Vérifier si les jobs sont toujours en cours
        if ($backendJob.State -eq "Failed" -or $frontendJob.State -eq "Failed") {
            Write-Host "❌ Un des services a échoué" -ForegroundColor Red
            break
        }
    }
} catch {
    Write-Host ""
    Write-Host "🛑 Arrêt des services..." -ForegroundColor Yellow
} finally {
    # Nettoyer les jobs
    Write-Host "🧹 Nettoyage..." -ForegroundColor Blue
    Stop-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Remove-Job -Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    
    Write-Host "✅ Services arrêtés proprement" -ForegroundColor Green
    Write-Host "👋 Merci d'avoir utilisé C2-EW!" -ForegroundColor Blue
}
